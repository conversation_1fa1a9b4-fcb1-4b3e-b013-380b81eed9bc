package com.stt.android.suuntoplusstore.home

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.suuntoplusstore.R

@Composable
fun SuuntoPlusStoreHomeSearchBar(
    searching: Boolean,
    searchTerm: String,
    onSearchTermChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val focusManager = LocalFocusManager.current

    TextField(
        value = searchTerm,
        onValueChange = {
            onSearchTermChange(it)
        },
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
        keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() }),
        shape = RectangleShape,
        leadingIcon = {
            Icon(
                imageVector = Icons.Filled.Search,
                contentDescription = null
            )
        },
        trailingIcon = {
            SearchBarTrailingIcon(
                searching = searching,
                showClearIcon = searchTerm.isNotEmpty(),
                onClearClick = {
                    onSearchTermChange("")
                }
            )
        },
        placeholder = {
            Text(
                text = stringResource(id = R.string.suunto_plus_store_home_screen_search_placeholder),
                style = MaterialTheme.typography.body
            )
        },
        colors = TextFieldDefaults.textFieldColors(
            textColor = MaterialTheme.colors.nearBlack,
            placeholderColor = MaterialTheme.colors.darkGrey,
            leadingIconColor = MaterialTheme.colors.darkGrey,
            trailingIconColor = MaterialTheme.colors.darkGrey,
            backgroundColor = MaterialTheme.colors.lightGrey,
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent
        ),
        textStyle = MaterialTheme.typography.body,
        singleLine = true,
        modifier = modifier.fillMaxWidth()
    )
}

@Composable
private fun SearchBarTrailingIcon(
    searching: Boolean,
    showClearIcon: Boolean,
    onClearClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (searching) {
        CircularProgressIndicator(
            color = MaterialTheme.colors.darkGrey,
            modifier = modifier.size(MaterialTheme.iconSizes.mini),
            strokeWidth = 2.dp
        )
    } else if (showClearIcon) {
        IconButton(
            onClick = onClearClick,
            modifier = modifier
        ) {
            Icon(
                imageVector = Icons.Filled.Clear,
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
private fun SuuntoPlusStoreHomeSearchBarPreview() {
    AppTheme {
        SuuntoPlusStoreHomeSearchBar(
            searching = false,
            searchTerm = "",
            onSearchTermChange = {}
        )
    }
}
