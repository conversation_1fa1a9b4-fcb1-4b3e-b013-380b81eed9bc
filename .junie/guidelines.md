# Guidelines for Junie

## 1. Prefer Kotlin over Java
* Use Kotlin unless modifying existing Java code.

## 2. Prefer Compose over XML based view
* Use Compose for UI unless modifying existing XML screens.

## 3. Use Compose Material 3
* Use Compose Material 3 unless modifying existing code using Material 2.
* To set the theme, prefer to use `setContentWithM3Theme {}`.

## 4. Prefer constructor injection over field injection
* Declare all mandatory dependencies as `val` and inject them through the constructor.
* Field injection is only allowed for classes created by the framework, such as `Activity` and `Service`.

## 5. Use Timber for logging
* Do not use `println` for logging.
* Do not use `Timber.e()` to log errors. Use `Timber.w()` instead.

## 6. Avoid depending on `impl` modules
* Should only depend on `api` modules.
* Only the `app` module is supposed to depend on `impl` modules.
