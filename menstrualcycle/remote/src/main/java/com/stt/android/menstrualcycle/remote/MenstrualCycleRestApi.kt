package com.stt.android.menstrualcycle.remote

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface MenstrualCycleRestApi {

    @GET("period-tracking/log")
    suspend fun fetchMenstrualCycles(): AskoResponse<List<RemoteMenstrualCycle>>

    @POST("period-tracking/log")
    suspend fun saveMenstrualCycle(@Body remoteMenstrualCycles: List<RemoteMenstrualCycle>): AskoResponse<List<RemoteMenstrualCycle>>

    @POST("period-tracking/log/batch-delete")
    suspend fun deleteMenstrualCycle(@Body remoteKeys: List<String>): AskoResponse<Any>
}
