package com.suunto.soa.bluetooth.ota.service

import android.bluetooth.BluetoothDevice
import kotlinx.coroutines.flow.Flow
import java.io.File

interface SoaOtaService {

    suspend fun initialization()

    fun isInTransferring(): Boolean

    fun isInCheckingBin(): Boolean

    fun isInProgress(): Boolean

    fun disconnect()

    fun getOtaStateFlow(): Flow<OtaState>

    fun transferFirmware(device: BluetoothDevice, otaFile: File, upgradeInfo: UpgradeInfo)

    fun getLogDir(): File
}
