package com.suunto.soa.ble.response

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.suunto.soa.ble.exception.ParseException
import com.suunto.soa.command.Response

class MusicInfoResponse(bytesInfo: ByteArray) : Response<OfflineMusicInfo>(bytesInfo) {

    override val data: OfflineMusicInfo = parseDataArray(dataBytes)

    private val musicInfoAdapter: JsonAdapter<List<OfflineMusicInfo>>
        get() = Moshi.Builder().build().adapter(
            Types.newParameterizedType(
                MutableList::class.java,
                OfflineMusicInfo::class.java
            )
        )

    private fun parseDataArray(dataBytes: ByteArray): OfflineMusicInfo {
        val musicInfoString = String(dataBytes, Charsets.UTF_8)
        return musicInfoAdapter.from<PERSON><PERSON>(musicInfoString)?.first()
            ?: throw ParseException("music info parse error")
    }
}
