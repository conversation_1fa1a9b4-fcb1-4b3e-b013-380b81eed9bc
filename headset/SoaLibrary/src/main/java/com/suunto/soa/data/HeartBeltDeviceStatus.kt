package com.suunto.soa.data

import com.suunto.soa.toUInt
import com.suunto.soa.utils.DataUtils

class HeartBeltDeviceStatus(data: ByteArray) {
    init {
        val firstAttrDataLength = data[0].toUInt()
        if (data.size == firstAttrDataLength + 1) {
            parseAttrList(listOf(data))
        } else {
            val attrDataList = mutableListOf<ByteArray>()
            var attrStartIndex = 0
            var attrDataSize = 0
            while (data.size >= attrStartIndex) {
                if (attrStartIndex == data.size) break
                attrDataSize = (data[attrStartIndex].toUInt()) + 1
                if ((attrStartIndex + attrDataSize) > data.size) break
                val attrData = ByteArray(attrDataSize)
                System.arraycopy(data, attrStartIndex, attrData, 0, attrDataSize)
                attrDataList.add(attrData)
                attrStartIndex += attrDataSize
            }
            parseAttrList(attrDataList)
        }
    }

    private fun parseAttrList(attrLists: List<ByteArray>) {
        attrLists.forEach { attrByteArray ->
            DataUtils.tlvDataSplit(attrByteArray) { attr, value ->
                when (attr[0].toUInt()) {
                    // TODO parse heart belt attributes here
                }
            }
        }
    }
}
