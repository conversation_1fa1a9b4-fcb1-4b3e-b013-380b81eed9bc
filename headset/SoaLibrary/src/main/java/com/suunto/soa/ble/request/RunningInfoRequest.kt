package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.ble.control.attr.RunningInfoAttr
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request
import com.suunto.soa.to4Bytes

internal class RunningInfoRequest(
    vararg attrs: RunningInfoAttr,
    opCode: Byte = RUNNING_INFO_REQUEST
) :
    Request(
        CMD_REQUEST_RESPONSE,
        opCode,
        OpCodeSn.nextOpCodeSn(),
        attrsMask(*attrs)
    ) {

    companion object {
        const val RUNNING_INFO_REQUEST = 0x09.toByte()

        private fun attrsMask(vararg attrs: RunningInfoAttr): ByteArray {
            var attrsValue = 0
            attrs.forEach { attrsValue = attrsValue or it.mask }
            return attrsValue.to4Bytes()
        }
    }
}
