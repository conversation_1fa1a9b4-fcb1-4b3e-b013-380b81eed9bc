package com.suunto.soa.ble.workqueue

import io.reactivex.Scheduler
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers

interface WorkQueue {

    fun <T : Any> add(operation: QueueOperation<T>, addFirst: Boolean? = null)

    fun <T : Any> addSingle(
        operation: QueueOperation<T>,
        addFirst: Boolean = false,
        scheduler: Scheduler = AndroidSchedulers.mainThread()
    ): Single<T>

    fun cancel(tag: Any)

    fun size(): Int

    fun destroy()
}
