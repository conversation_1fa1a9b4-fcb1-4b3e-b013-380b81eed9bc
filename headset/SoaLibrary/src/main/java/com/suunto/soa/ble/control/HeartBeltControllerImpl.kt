package com.suunto.soa.ble.control

import android.bluetooth.BluetoothDevice
import com.suunto.soa.DataConstants
import com.suunto.soa.DataConstants.CMD_REQUEST_NO_RESPONSE
import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.DataConstants.CMD_RESPONSE_NO_RESPONSE
import com.suunto.soa.DataConstants.CMD_RESPONSE_RESPONSE
import com.suunto.soa.ble.control.attr.HeartBeltDeviceAttr
import com.suunto.soa.ble.control.attr.HeartBeltRunningInfoAttr
import com.suunto.soa.ble.device.DeviceConfig
import com.suunto.soa.ble.device.HeartRateBeltConfig
import com.suunto.soa.ble.notify.DeviceStatusNotify
import com.suunto.soa.ble.notify.NotifyFactory
import com.suunto.soa.ble.operation.AnyOperation
import com.suunto.soa.ble.operation.HeartBeltDeviceInfoOperation
import com.suunto.soa.ble.operation.HeartBeltRunningInfoOperation
import com.suunto.soa.ble.operation.NotifyOperation
import com.suunto.soa.ble.operation.UpdateFileInfoOperation
import com.suunto.soa.ble.request.HeartBeltDeviceInfoRequest
import com.suunto.soa.ble.request.HeartBeltRunningInfoRequest
import com.suunto.soa.ble.request.OSInfoRequest
import com.suunto.soa.ble.request.SettingRequest
import com.suunto.soa.ble.request.UpdateFileInfoRequest
import com.suunto.soa.ble.response.AnyResponse
import com.suunto.soa.ble.response.HeartBeltDeviceInfoResponse
import com.suunto.soa.ble.response.HeartRunningInfoResponse
import com.suunto.soa.ble.response.UpdateFileInfoResponse
import com.suunto.soa.ble.service.BleManager
import com.suunto.soa.ble.workqueue.OperationHandler
import com.suunto.soa.ble.workqueue.WorkQueue
import com.suunto.soa.ble.workqueue.WorkQueueImpl
import com.suunto.soa.command.Request
import com.suunto.soa.command.ResponseParamsData
import com.suunto.soa.command.SoaUnique
import com.suunto.soa.data.HeartBeltDeviceStatus
import com.suunto.soa.state.DeviceRunning
import com.suunto.soa.utils.DataUtils.getCmd
import com.suunto.soa.utils.TransformUtils
import io.reactivex.Completable
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.subjects.PublishSubject
import no.nordicsemi.android.ble.callback.DataReceivedCallback
import no.nordicsemi.android.ble.data.Data
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

internal class HeartBeltControllerImpl(
    private val bleManager: BleManager,
    private val deviceRunning: DeviceRunning,
    private val deviceConfig: DeviceConfig,
) : HeartBeltController, InternalController, DataReceivedCallback {

    private val workQueue: WorkQueue = WorkQueueImpl()
    private val operationHandlers = ConcurrentHashMap<Int, OperationHandler>()
    private val notifyObservables = ConcurrentHashMap<Byte, PublishSubject<*>>()

    private fun sendAnyRequest(request: Request): Single<AnyResponse> {
        val operation = AnyOperation(this, bleManager, request, deviceRunning)
        return workQueue.addSingle(operation)
    }

    override fun initDevice(): Single<Boolean> {
        return Single.zip(
            modifyMtu(BLE_MTU),
            enableNotifications(deviceConfig.serviceUuid, deviceConfig.notifyCharacteristic)
        ) { _, enabledNotifications ->
            enabledNotifications
        }
    }

    //region implementation of DataReceivedCallback interface.
    override fun onDataReceived(device: BluetoothDevice, data: Data) {
        data.value?.let {
            Timber.i("[Receive] ${TransformUtils.bytes2HexString(it)}")
            when (getCmd(it)) {
                // For app request, and this response don't require an app reply.
                CMD_RESPONSE_NO_RESPONSE -> {
                    getHandler(SoaUnique.uniqueKey(it))?.onResponse(it)
                }
                // For app request, and this response requires an app reply.
                CMD_RESPONSE_RESPONSE -> {
                    // No action.
                }
                // The device notifies the data and requires a response
                CMD_REQUEST_RESPONSE -> {
                    val notify = NotifyFactory.create(it) ?: return
                    val response = ResponseParamsData(
                        cmd = CMD_RESPONSE_NO_RESPONSE,
                        opCode = notify.opCode,
                        opCodeSn = notify.opCodeSn,
                        status = DataConstants.STATUS_SUCCESS
                    ).toAnyResponse()
                    val operation = NotifyOperation(this, bleManager, response, notify)
                    return workQueue.add(operation)
                }
                // Data notified by the device. No response is required.
                CMD_REQUEST_NO_RESPONSE -> {
                    // No action.
                }
                // Other illegal data.
                else -> {
                    // No action.
                }
            }
        }
    }
    //endregion DataReceivedCallback

    override fun queryRunningInfo(vararg attrs: HeartBeltRunningInfoAttr): Single<HeartRunningInfoResponse> {
        val operation =
            HeartBeltRunningInfoOperation(
                this,
                bleManager,
                HeartBeltRunningInfoRequest(*attrs, opCode = QUERY_REQUEST),
                deviceRunning
            )
        return workQueue.addSingle(operation)
    }

    override fun pushSetting(settingsData: ByteArray): Single<AnyResponse> {
        return sendAnyRequest(SettingRequest(settingsData, opCode = SETTING_REQUEST))
    }

    override fun setOSInfo(data: ByteArray): Single<AnyResponse> {
        return sendAnyRequest(OSInfoRequest(data))
    }

    override fun queryDeviceInfo(vararg attrs: HeartBeltDeviceAttr): Single<HeartBeltDeviceInfoResponse> {
        val operation =
            HeartBeltDeviceInfoOperation(this, bleManager, HeartBeltDeviceInfoRequest(*attrs), deviceRunning)
        return workQueue.addSingle(operation)
    }

    override fun queryUpdateFileInfo(): Single<UpdateFileInfoResponse> {
        val operation =
            UpdateFileInfoOperation(this, bleManager, UpdateFileInfoRequest(), deviceRunning)
        return workQueue.addSingle(operation)
    }

    override fun subscribeDeviceStatusChanged(): Observable<HeartBeltDeviceStatus> {
        var observable = getNotifyObservable(DeviceStatusNotify.DEVICE_STATUS_NOTIFY_OP_CODE)
        if (observable == null) {
            observable = PublishSubject.create<Request>()
            addNotifyObservable(DeviceStatusNotify.DEVICE_STATUS_NOTIFY_OP_CODE, observable)
        }
        return observable as Observable<HeartBeltDeviceStatus>
    }

    override fun unsubscribeDeviceStatusChanged(): Completable {
        return Completable.create {
            getNotifyObservable(DeviceStatusNotify.DEVICE_STATUS_NOTIFY_OP_CODE)?.onComplete()
            removeNotifyObservable(DeviceStatusNotify.DEVICE_STATUS_NOTIFY_OP_CODE)
        }
    }

    override fun enabledBatteryLevelNotification(onGetBatteryLevel: (Int) -> Unit): Single<Boolean> {
        return bleManager.enableBatteryLevelNotifications(
            HeartRateBeltConfig.BATTER_SERVICE_UUID,
            HeartRateBeltConfig.BATTERY_LEVEL_CHARACTERISTIC_UUID
        ) { _, data ->
            data.value?.let {
                if (it.isNotEmpty()) {
                    Timber.i("Battery level: ${it[0].toInt()}")
                    onGetBatteryLevel.invoke(it[0].toInt())
                }
            }
        }
    }

    // region implementation of InternalController interface.
    override fun disconnect(): Single<Boolean> {
        return bleManager.disconnectDevice()
    }

    override fun destroy() {
        workQueue.destroy()
        operationHandlers.forEach { (_, operationHandler) ->
            operationHandler.stop()
        }
        operationHandlers.clear()
        clearNotifyObservables()
    }

    private fun clearNotifyObservables() {
        notifyObservables.forEach { (_, publishSubject) ->
            publishSubject.onError(Exception("controller is destroy"))
        }
        notifyObservables.clear()
    }

    override fun enableNotifications(
        serviceUuid: String,
        characteristicUuid: String
    ): Single<Boolean> {
        return bleManager.enableNotifications(serviceUuid, characteristicUuid, this)
    }

    override fun disableNotifications(
        serviceUuid: String,
        characteristicUuid: String
    ): Single<Boolean> {
        return bleManager.disableNotifications(serviceUuid, characteristicUuid)
    }

    override fun modifyMtu(mtu: Int): Single<Boolean> {
        return bleManager.modifyMtu(mtu)
    }

    override fun addHandler(key: Int, handler: OperationHandler) {
        if (!operationHandlers.containsKey(key)) {
            operationHandlers[key] = handler
        }
    }

    override fun removeHandler(key: Int) {
        if (operationHandlers.containsKey(key)) {
            operationHandlers.remove(key)
        }
    }

    override fun getHandler(key: Int): OperationHandler? {
        return operationHandlers[key]
    }

    override fun addNotifyObservable(key: Byte, observable: PublishSubject<*>) {
        if (!notifyObservables.containsKey(key)) {
            notifyObservables[key] = observable
        }
    }

    override fun removeNotifyObservable(key: Byte) {
        notifyObservables.remove(key)
    }

    override fun getNotifyObservable(key: Byte): PublishSubject<*>? {
        return notifyObservables[key]
    }

    override fun getDeviceConfig(): DeviceConfig = deviceConfig
    //endregion InternalController

    companion object {
        const val BLE_MTU = 240
        const val QUERY_REQUEST = 0x49.toByte()
        const val SETTING_REQUEST = 0x48.toByte()
    }
}
