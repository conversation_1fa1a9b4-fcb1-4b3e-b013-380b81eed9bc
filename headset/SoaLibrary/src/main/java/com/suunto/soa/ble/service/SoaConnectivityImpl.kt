package com.suunto.soa.ble.service

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import com.suunto.soa.ble.control.Controller
import com.suunto.soa.ble.control.HeartBeltController
import com.suunto.soa.ble.control.InternalController
import com.suunto.soa.ble.control.InternalControllerFactory
import com.suunto.soa.ble.control.SoaController
import com.suunto.soa.ble.device.DeviceConfig
import com.suunto.soa.ble.exception.DisconnectionException
import com.suunto.soa.state.DeviceRunning
import com.suunto.soa.state.RunningState
import io.reactivex.Completable
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.subjects.BehaviorSubject
import timber.log.Timber

internal class SoaConnectivityImpl(
    private val context: Context,
    private val deviceConfig: DeviceConfig
) : SoaConnectivity {

    private var connectedDevice: BluetoothDevice? = null

    private val connectState = BehaviorSubject.createDefault<BleState>(BleState.None)

    // Create DeviceRunning instance that manages its own state
    private val deviceRunning = DeviceRunning()

    private var deviceController: InternalController? = null

    private var reconnectDisposable: Disposable? = null

    private var deviceInitDisposable: Disposable? = null

    private var deviceDisconnectDisposable: Disposable? = null

    private var isReconnectNeed: Boolean = false

    private var bleManager: BleManager? = null

    private fun createBleManager(deviceConfig: DeviceConfig): BleManager {
        return BleManager(context, deviceConfig.characteristics).apply {
            setBleCallback(SoaConnectivityObserver(this))
        }
    }

    private fun getBleManager(): BleManager {
        if (bleManager == null) {
            bleManager = createBleManager(deviceConfig)
        }
        return bleManager!!
    }

    private inner class SoaConnectivityObserver(private val bleManager: BleManager) :
        BleConnectivityObserver() {

        override fun onDeviceConnecting(device: BluetoothDevice) {
            super.onDeviceConnecting(device)
            stateChange(BleState.Connecting)
        }

        override fun onDeviceReady(device: BluetoothDevice) {
            super.onDeviceReady(device)
            initOnReady(device)
        }

        override fun onDeviceDisconnecting(device: BluetoothDevice) {
            super.onDeviceDisconnecting(device)
            stateChange(BleState.Disconnecting)
        }

        override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
            super.onDeviceDisconnected(device, reason)
            deviceRunning.setRunningState(RunningState.Unknown)

            val disConnectedReason = BleState.Disconnected.Reason.parse(reason)
            stateChange(BleState.Disconnected(disConnectedReason))
            destroyOnDisconnected()

            if (isReconnectNeed) {
                // delay 3s for reconnectDevice, solve the problem of SU07 headset Bluetooth disconnection
                reconnectDisposable =
                    bleManager.reconnectDevice(device, SoaConnectConfig(3), 3_000L)
                        .doOnSubscribe {
                            // Try to reconnect only once.
                            isReconnectNeed = false
                        }.subscribe({
                            Timber.v("reconnect device result: $it.")
                        }, {
                            Timber.v(it, "reconnect device failed.")
                        })
            }
        }

        override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
            super.onDeviceFailedToConnect(device, reason)
            val failedToConnectReason = BleState.Disconnected.Reason.parse(reason)
            stateChange(BleState.Disconnected(failedToConnectReason))
        }

        private fun initOnReady(device: BluetoothDevice) {
            connectedDevice = device
            // Use the internal DeviceRunning instance directly
            deviceController = InternalControllerFactory.create(
                deviceType = deviceConfig.deviceType,
                bleManager,
                deviceRunning,
                deviceConfig
            )
            deviceController?.let { controller ->
                deviceInitDisposable =
                    controller.initDevice()
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe({ result ->
                            handleDeviceInitResult(result)
                        }, {
                            handleDeviceInitResult(false)
                        })
            }
        }

        private fun handleDeviceInitResult(result: Boolean) {
            Timber.v("handle device init result: $result.")
            isReconnectNeed = if (result) {
                deviceRunning.setRunningState(RunningState.Normal)
                stateChange(BleState.Ready)
                true
            } else {
                deviceRunning.setRunningState(RunningState.Unknown)
                stateChange(BleState.Disconnected(BleState.Disconnected.Reason.UNKNOWN))
                disconnectDevice()
                false
            }
            Timber.v("device isReconnectNeed: $isReconnectNeed.")
        }

        private fun disconnectDevice() {
            deviceDisconnectDisposable?.let {
                if (!it.isDisposed) {
                    it.dispose()
                }
            }
            deviceDisconnectDisposable =
                (deviceController as? SoaController)?.disconnect()?.subscribe({
                    Timber.d("disconnectDevice success!")
                }, {
                    Timber.w("disconnectDevice error:$it")
                })
        }

        private fun destroyOnDisconnected() {
            connectedDevice = null
            (deviceController as? InternalController)?.destroy()
            deviceController = null
        }
    }

    private fun stateChange(state: BleState) = connectState.onNext(state)

    private fun cancelReconnection() {
        isReconnectNeed = false
        reconnectDisposable?.let {
            if (!it.isDisposed) {
                it.dispose()
            }
        }
    }

    override fun connectDevice(device: BluetoothDevice, retryCount: Int): Single<SoaConnectResult> {
        cancelReconnection()
        return getBleManager().connectDevice(device, SoaConnectConfig(retryCount))
    }

    override fun connectDevice(macAddress: String, retryCount: Int): Single<SoaConnectResult> {
        val manager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        val device = manager.adapter.getRemoteDevice(macAddress)
        return connectDevice(device, retryCount)
    }

    override fun disconnectDevice(): Single<Boolean> {
        cancelReconnection()
        return getBleManager().disconnectDevice()
    }

    override fun getConnectedDevice(): Single<BluetoothDevice> = if (connectedDevice == null) {
        Single.error(DisconnectionException("No connected device."))
    } else {
        Single.just(connectedDevice)
    }

    override fun waitForBleState(state: BleState): Completable = connectState.distinctUntilChanged()
        .filter { it == state }
        .firstOrError()
        .ignoreElement()

    override fun waitForBleReady() = waitForBleState(BleState.Ready)

    override fun getConnectState(): Single<BleState> = connectState.firstOrError()

    override fun observableConnectState(): Observable<BleState> = connectState

    override fun waitForController() = Single.create { emitter ->
        if (connectState.value?.isReady != true || deviceController == null || deviceController !is Controller) {
            emitter.onError(DisconnectionException("the connection has been disconnected."))
        } else {
            emitter.onSuccess(deviceController as Controller)
        }
    }

    override fun waitForHeartBeltController() = Single.create { emitter ->
        if (connectState.value?.isReady != true || deviceController == null || deviceController !is HeartBeltController) {
            emitter.onError(DisconnectionException("the connection has been disconnected."))
        } else {
            emitter.onSuccess(deviceController as HeartBeltController)
        }
    }

    override fun getDeviceConfig(): DeviceConfig = deviceConfig

    /**
     * Get the DeviceRunning instance for external components that need it
     */
    override fun getDeviceRunning(): DeviceRunning = deviceRunning
}
