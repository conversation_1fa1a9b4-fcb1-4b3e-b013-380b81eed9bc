package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request

internal class DeviceConfigRequest(
    isSync: Boolean = false,
    configData: ByteArray,
    timeoutMills: Long = TIME_OUT
) : Request(
    CMD_REQUEST_RESPONSE,
    getRequestCode(isSync),
    OpCodeSn.nextOpCodeSn(),
    configData,
    timeoutMills
) {
    companion object {
        fun getRequestCode(isSync: Boolean): Byte {
            return if (isSync) {
                0xF3.toByte()
            } else {
                0xF2.toByte()
            }
        }
    }
}
