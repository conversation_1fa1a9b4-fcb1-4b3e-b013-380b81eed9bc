package com.suunto.extension

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.view.Gravity
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat

interface DslSpannableStringBuilder {
    fun highLightText(text: String, method: (DslSpanBuilder.() -> Unit)? = null)
}

interface DslSpanBuilder {
    fun setColor(color: Int)
    fun setStyle(style: Int)
    fun setUnderline()
    fun onClick(context: Context, @ColorRes color: Int, onClick: View.OnClickListener)
}

class DslSpannableStringBuilderImpl(text: String) : DslSpannableStringBuilder {
    private val builder = SpannableStringBuilder(text)
    var firstIndex: Int = 0
    var lastIndex: Int = 0

    override fun highLightText(text: String, method: (DslSpanBuilder.() -> Unit)?) {
        firstIndex = builder.indexOf(text)
        // found the string and set Span
        if (firstIndex != -1) {
            lastIndex = firstIndex + text.length
            if (lastIndex <= builder.toString().length) {
                val spanBuilder = DslSpanBuilderImpl()
                method?.let { spanBuilder.it() }
                spanBuilder.apply {
                    val span = if (foregroundColorSpan != null) {
                        foregroundColorSpan
                    } else if (styleSpan != null) {
                        styleSpan
                    } else if (onClickSpan != null) {
                        onClickSpan
                    } else if (underlineSpan != null) {
                        underlineSpan
                    } else {
                        null
                    }
                    span?.let {
                        builder.setSpan(
                            it,
                            firstIndex,
                            lastIndex,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                    }
                }
            }
        }
    }

    fun build(): SpannableStringBuilder {
        return builder
    }
}

class ClickTextSpan(
    var context: Context,
    @ColorRes var colorRes: Int,
    var onClick: OnClickListener? = null
) : ClickableSpan() {

    override fun onClick(view: View) {
        onClick?.onClick(view)
    }

    override fun updateDrawState(ds: TextPaint) {
        ds.color = ContextCompat.getColor(context, colorRes)
    }
}

class DslSpanBuilderImpl : DslSpanBuilder {
    var foregroundColorSpan: ForegroundColorSpan? = null
    var onClickSpan: ClickTextSpan? = null
    var styleSpan: StyleSpan? = null
    var underlineSpan: UnderlineSpan? = null
    override fun setColor(color: Int) {
        foregroundColorSpan = ForegroundColorSpan(color)
    }

    override fun setStyle(style: Int) {
        styleSpan = StyleSpan(style)
    }

    override fun setUnderline() {
        underlineSpan = UnderlineSpan()
    }

    override fun onClick(context: Context, @ColorRes color: Int, onClick: OnClickListener) {
        onClickSpan = ClickTextSpan(context, color, onClick)
    }
}

fun TextView.buildSpannableString(
    originalText: String,
    init: DslSpannableStringBuilder.() -> Unit
) {
    val spanStringBuilderImpl = DslSpannableStringBuilderImpl(originalText)
    spanStringBuilderImpl.init()
    text = spanStringBuilderImpl.build()
}

fun String.buildSpannableString(init: DslSpannableStringBuilder.() -> Unit): Spannable {
    val spanStringBuilderImpl = DslSpannableStringBuilderImpl(this)
    spanStringBuilderImpl.init()
    return spanStringBuilderImpl.build()
}

fun TextView.sizeDrawable(
    size: Int,
    startDrawable: Int = 0,
    topDrawable: Int = 0,
    endDrawable: Int = 0,
    bottomDrawable: Int = 0
): TextView {
    sizeDrawable(size, size, startDrawable, topDrawable, endDrawable, bottomDrawable)
    return this
}

fun TextView.sizeDrawable(
    width: Int,
    height: Int,
    startDrawable: Int = 0,
    topDrawable: Int = 0,
    endDrawable: Int = 0,
    bottomDrawable: Int = 0
): TextView {
    val rect = Rect(0, 0, width, height)
    setCompoundDrawablesRelative(
        findDrawable(startDrawable, 0, this)?.apply { bounds = rect },
        findDrawable(topDrawable, 1, this)?.apply { bounds = rect },
        findDrawable(endDrawable, 2, this)?.apply { bounds = rect },
        findDrawable(bottomDrawable, 3, this)?.apply { bounds = rect }
    )
    return this
}

private fun findDrawable(drawableRes: Int, index: Int, textView: TextView): Drawable? {
    if (drawableRes != 0) return AppCompatResources.getDrawable(textView.context, drawableRes)
    if (textView.compoundDrawablesRelative.isNotEmpty()) return textView.compoundDrawablesRelative[index]
    return null
}

fun TextView.setDrawableByDirection(direction: Int, drawable: Drawable? = null) {
    when (direction) {
        Gravity.LEFT, Gravity.START -> {
            setCompoundDrawablesRelative(
                drawable,
                compoundDrawablesRelative[1],
                compoundDrawablesRelative[2],
                compoundDrawablesRelative[3]
            )
        }

        Gravity.TOP -> {
            setCompoundDrawablesRelative(
                compoundDrawablesRelative[0],
                drawable,
                compoundDrawablesRelative[2],
                compoundDrawablesRelative[3]
            )
        }

        Gravity.RIGHT, Gravity.END -> {
            setCompoundDrawablesRelative(
                compoundDrawablesRelative[0],
                compoundDrawablesRelative[1],
                drawable,
                compoundDrawablesRelative[3]
            )
        }

        Gravity.BOTTOM -> {
            setCompoundDrawablesRelative(
                compoundDrawablesRelative[0],
                compoundDrawablesRelative[1],
                compoundDrawablesRelative[2],
                drawable
            )
        }
    }
}
