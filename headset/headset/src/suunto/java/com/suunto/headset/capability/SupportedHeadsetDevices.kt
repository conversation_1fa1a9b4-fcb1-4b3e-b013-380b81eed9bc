package com.suunto.headset.capability

import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import com.suunto.headset.model.ButtonType
import com.suunto.headset.model.DeviceCapability
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.model.LEDLightItem
import com.suunto.headset.model.SportSupports
import com.suunto.headset.model.SportType
import com.suunto.headset.repository.SetUserHeadsetConfigDataSource
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.ButtonFunction
import com.suunto.soa.ble.response.ButtonShortcutKey
import javax.inject.Inject

class SupportedHeadsetDevices @Inject constructor(
    private val featureToggle: FeatureToggle,
    private val setUserHeadsetConfigDataSource: SetUserHeadsetConfigDataSource,
) {
    private val deviceCapability: DeviceCapability
        get() = setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability

    private fun getCapabilities(baseHeadsetFeature: BaseHeadsetFeature): HeadsetCapability {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        return HeadsetCapabilityProvider[deviceType]
    }

    fun supportLanguageSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportLanguageSetting()
    }

    fun supportEqMode(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportEqMode()
    }

    fun supportBodySense(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportBodySense()
    }

    fun supportDualDeviceConnect(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportDualDeviceConnect()
    }

    fun supportGetSerial(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportGetSerial()
    }

    fun supportLowLatency(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportLowLatency()
    }

    fun supportGetConnectedDevices(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportGetConnectedDevices()
    }

    fun supportLEDLightMode(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportLEDLightMode()
    }

    fun supportOfflineMusic(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportOfflineMusic()
    }

    fun supportNeckMovementAssessment(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportNeckMovementAssessment()
    }

    fun supportButtonCustomization(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportButtonCustomization()
    }

    fun supportSwimFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportSwimFeature()
    }

    fun supportJumpRopeFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportJumpRopeFeature(
            setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability
        )
    }

    fun supportRunningFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportRunningFeature(
            setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability
        )
    }

    fun getSportSupports(baseHeadsetFeature: BaseHeadsetFeature): SportSupports {
        return getCapabilities(baseHeadsetFeature).getSportSupports(setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability)
    }

    fun getUserGuideUrl(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        return getCapabilities(baseHeadsetFeature).getUserGuideUrl()
    }

    fun getMoreInformationUrl(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        return getCapabilities(baseHeadsetFeature).getMoreInformationUrl()
    }

    fun getFunctionIntroduction(baseHeadsetFeature: BaseHeadsetFeature): List<HeadsetOnBoardingPage> {
        return getCapabilities(baseHeadsetFeature).getFunctionIntroduction()
    }

    fun getSportsModeList(baseHeadsetFeature: BaseHeadsetFeature): List<SportsMode> {
        return getCapabilities(baseHeadsetFeature).getSportsModeList()
    }

    fun getSoundModeNormalPhotoResIds(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        return getCapabilities(baseHeadsetFeature).getSoundModeNormalPhotoResIds()
    }

    fun getSoundModeOutdoorPhotoResIds(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        return getCapabilities(baseHeadsetFeature).getSoundModeOutdoorPhotoResIds()
    }

    fun getButtonList(baseHeadsetFeature: BaseHeadsetFeature): List<HeadsetButton> {
        return getCapabilities(baseHeadsetFeature).getButtonList()
    }

    fun getButtonShortcutKeyList(baseHeadsetFeature: BaseHeadsetFeature): List<ButtonShortcutKey> {
        return getCapabilities(baseHeadsetFeature).getCustomizationShortcutKeyList()
    }

    fun getButtonType(
        baseHeadsetFeature: BaseHeadsetFeature,
        buttonFunction: ButtonFunction
    ): ButtonType {
        return getCapabilities(baseHeadsetFeature).getHeadsetButtonType(buttonFunction)
    }

    fun getSoundModeList(baseHeadsetFeature: BaseHeadsetFeature): List<EqMode> {
        return getCapabilities(baseHeadsetFeature).getSoundModeList()
    }

    fun isCheckDeviceAvailableBeforeOTA(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).isCheckDeviceAvailableBeforeOTA()
    }

    fun supportMusicModeFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportMusicModeFeature()
    }

    fun supportCallStatusFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportCallStatusFeature()
    }

    fun supportJumpAssessmentFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportJumpAssessment()
    }

    fun supportSportsSwitchFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportSportsSwitch(deviceCapability)
    }

    fun supportSportButtonModify(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportSportButtonModify(deviceCapability)
    }

    fun getSportTypes(baseHeadsetFeature: BaseHeadsetFeature): List<SportType> {
        val sportTypeList =
            getCapabilities(baseHeadsetFeature).getSportTypes(deviceCapability).toMutableList()
        if (!featureToggle[FeatureFlag.EnableHeadsetRunning]) {
            sportTypeList.remove(SportType.RUNNING)
        }
        return sportTypeList
    }

    fun getDefaultSportType(baseHeadsetFeature: BaseHeadsetFeature): SportType? {
        return getCapabilities(baseHeadsetFeature).getDefaultSportType()
    }

    fun getHeadMovementInstructions(baseHeadsetFeature: BaseHeadsetFeature): List<HeadControlInstruction> {
        return getCapabilities(baseHeadsetFeature).getHeadControlInstructions()
    }

    fun supportPoolSwimSport(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportsPoolSwimSport(deviceCapability)
    }

    fun supportPoolDistanceSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportsPoolDistanceSetting(deviceCapability)
    }

    fun supportUserInfoSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportsUserInfoSetting(deviceCapability)
    }

    fun supportGetDeviceLedStatus(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportsGetLedStatus()
    }

    fun getLightModeList(baseHeadsetFeature: BaseHeadsetFeature): List<LEDLightItem> {
        return getCapabilities(baseHeadsetFeature).getLightModeList()
    }

    fun supportGetColorType(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportGetColorType()
    }

    fun supportMultipleBattery(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportMultiBattery()
    }

    fun supportMetronome(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportMetronome()
    }

    fun supportImmersiveMode(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportImmersiveMode()
    }

    fun supportSpatialAudio(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportSpatialAudio()
    }

    fun supportFindHeadphones(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportFindHeadphones()
    }

    fun supportAI(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportAI()
    }

    fun supportOwsConnectionState(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        return getCapabilities(baseHeadsetFeature).supportOwsConnectionState()
    }
}
