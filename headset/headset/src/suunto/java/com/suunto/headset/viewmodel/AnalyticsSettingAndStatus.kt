package com.suunto.headset.viewmodel

sealed class HeadsetSetting<S : HeadsetSettingStatus>(private val oldStatusAndNewStatus: Pair<S, S>) {
    abstract val settingName: String
    fun getOldStatusName(): String {
        return oldStatusAndNewStatus.first.statusName
    }

    fun getNewStatusName(): String {
        return oldStatusAndNewStatus.second.statusName
    }

    class HeadMovementControl(oldStatusAndNewStatus: Pair<SwitchStatus, SwitchStatus>) :
        HeadsetSetting<SwitchStatus>(
            oldStatusAndNewStatus
        ) {
        override val settingName: String
            get() = "HeadMovementControl"
    }

    class SoundMode(oldStatusAndNewStatus: Pair<SoundModeStatus, SoundModeStatus>) :
        HeadsetSetting<SoundModeStatus>(
            oldStatusAndNewStatus
        ) {
        override val settingName: String
            get() = "SoundMode"
    }

    class LEDlights(oldStatusAndNewStatus: Pair<AnalyticsLEDLightMode, AnalyticsLEDLightMode>) :
        HeadsetSetting<AnalyticsLEDLightMode>(
            oldStatusAndNewStatus
        ) {
        override val settingName: String
            get() = "LEDlights"
    }

    class DualDeviceConnection(oldStatusAndNewStatus: Pair<SwitchStatus, SwitchStatus>) :
        HeadsetSetting<SwitchStatus>(
            oldStatusAndNewStatus
        ) {
        override val settingName: String
            get() = "DualDeviceConnection"
    }
}

abstract class HeadsetSettingStatus(val statusName: String)
sealed class SwitchStatus(statusName: String) : HeadsetSettingStatus(statusName) {
    data object On : SwitchStatus("On")
    data object Off : SwitchStatus("Off")
}

sealed class SoundModeStatus(statusName: String) : HeadsetSettingStatus(statusName) {
    data object Normal : SoundModeStatus("Normal")
    data object Outdoor : SoundModeStatus("Outdoor")
}

sealed class AnalyticsLEDLightMode(statusName: String) : HeadsetSettingStatus(statusName) {
    data object Off : AnalyticsLEDLightMode("Off")
    data object ConstantLight : AnalyticsLEDLightMode("Constant light")
    data object Flashing : AnalyticsLEDLightMode("Flashing")
    data object SOS : AnalyticsLEDLightMode("SOS")
    data object Running : AnalyticsLEDLightMode("Running")
    data object Cycling : AnalyticsLEDLightMode("Cycling")
}
