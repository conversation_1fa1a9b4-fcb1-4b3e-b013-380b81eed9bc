package com.suunto.headset.ui

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.content.res.Configuration
import android.view.View
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.repeatOnLifecycle
import by.kirich1409.viewbindingdelegate.viewBinding
import com.google.android.material.snackbar.Snackbar
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.smartdeviceota.LatestVersionInfo
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.CustomTabsUtils
import com.suunto.extension.gone
import com.suunto.extension.navigate
import com.suunto.extension.setTranslucent
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SU07HeadsetFeature
import com.suunto.headset.capability.SU08HeadsetFeature
import com.suunto.headset.capability.SU10HeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.databinding.FragmentHeadsetSettingsBinding
import com.suunto.headset.ext.getNameResId
import com.suunto.headset.model.HeadsetConfig
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.repository.CurrentlyPairedHeadsetConfigUseCase
import com.suunto.headset.ui.view.AlertDialogFragment
import com.suunto.headset.ui.view.buildAlertDialogFragment
import com.suunto.headset.viewmodel.HeadsetSettingViewModel
import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.ColorType
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.service.BleState
import com.suunto.soa.bluetooth.ota.service.isOtaStateUnAvailable
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass
import com.stt.android.R as BaseR

/**
 *  The headset Setting UI
 */
@AndroidEntryPoint
class HeadsetSettingFragment :
    BaseStateFragment<HeadsetSettingContract.UIIntent, HeadsetSettingContract.UIState, HeadsetSettingViewModel>() {

    val binding by viewBinding(FragmentHeadsetSettingsBinding::bind, R.id.container)
    private var alertDialogFragment: AlertDialogFragment? = null
    private var updateDialogFragment: AlertDialogFragment? = null
    private var latestVersionResponse: LatestVersionInfo? = null

    private var currentDevice: BluetoothDevice? = null
    override fun getLayoutResId() = R.layout.fragment_headset_settings

    override fun appbarElevationEnabled() = false

    override val viewModelClass: KClass<HeadsetSettingViewModel>
        get() = HeadsetSettingViewModel::class

    override fun showAboutDeviceMenu(): Boolean = true

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    @Inject
    lateinit var currentlyPairedHeadsetConfigUseCase: CurrentlyPairedHeadsetConfigUseCase

    @Inject
    lateinit var stateManage: HeadsetStateManage

    private var soundMode: EqMode? = null
    private var otaTipBar: Snackbar? = null
    private var sportsMode: SportsMode? = null
    private var isReconnectClicked: Boolean = false
    private var isSwimming = false
    private var bodySenseOpened: Boolean = false

    override fun initView() {
        super.initView()
        setHeadsetImage()
        initStateManage()
        showSettingUI()
        showInstruction()
        setItemClicked()
        setBackFinishActivity()
        handleObserveOTAState()
        handleObserveConnectState()
    }

    private fun setHeadsetImage() {
        when (baseHeadsetFeature) {
            is SU08HeadsetFeature ->
                binding.myHeadset.headsetImage.setImageResource(R.drawable.icon_su08)

            is SU07HeadsetFeature ->
                binding.myHeadset.headsetImage.setImageResource(R.drawable.icon_su07)

            is SU10HeadsetFeature ->
                binding.myHeadset.headsetImage.setImageResource(R.drawable.icon_su10)

            else -> binding.myHeadset.headsetImage.setImageResource(R.drawable.headset_suunto_sound_pro)
        }
    }

    private fun initStateManage() {
        stateManage.onReconnectClicked = {
            val mac = currentlyPairedHeadsetConfigUseCase.getHeadsetLastMac()
            val pid = currentlyPairedHeadsetConfigUseCase.getHeadsetPid()
            HeadsetSettingContract.UIIntent.ConnectDevice(mac, pid).sendIntent()
            isReconnectClicked = true
        }
    }

    private fun showInstruction() {
        val showInstruction = arguments?.getBoolean(SHOW_INSTRUCTION) ?: false
        if (showInstruction) {
            navigate(R.id.fragment_instruction)
            arguments?.putBoolean(SHOW_INSTRUCTION, false)
        }
    }

    private fun handleObserveOTAState() {
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                runSuspendCatching {
                    viewModel.getDeviceRunning().getRunningStateFlow()
                        .filter { stateManage.notConnectLastDevice() }
                        .map { it.isOtaState() }
                        .distinctUntilChanged()
                        .collect {
                            Timber.i("ota is in ota process: $it")
                            when {
                                it -> {
                                    otaTipBar = Snackbar.make(
                                        binding.root,
                                        getString(R.string.update_headphone),
                                        Snackbar.LENGTH_INDEFINITE
                                    )
                                        .apply { show() }
                                }

                                else -> {
                                    otaTipBar?.dismiss()
                                }
                            }
                            Timber.d("handleObserveOTAState otaProgress: $it")
                            stateManage.processUIStateByOTAProgress(it, this@HeadsetSettingFragment)
                            setPairAnotherDeviceDisabled(it)
                        }
                }.onFailure { thr ->
                    Timber.w(thr, "subscribeOtaState failed.")
                }
            }
        }
    }

    private fun setPairAnotherDeviceDisabled(disabled: Boolean) {
        binding.headsetSettingPairAnotherDeviceLabel.setTranslucent(disabled)
        with(binding.ivConnectAnotherDevice) {
            alpha = if (disabled) 0.5F else 1F
            isClickable = !disabled
        }
    }

    override fun dispatchConnectStatus(state: BleState) {
        super.dispatchConnectStatus(state)
        val disconnected = !state.isReady
        if (!disconnected) {
            isReconnectClicked = false
        }
        // reset tryConnectLastDevice and promptDisconnect when headphone connected
        stateManage.resetTryConnectLastDevice(state)
        stateManage.processUIStateByBleState(state, this)
        if (state != BleState.Connecting) {
            Timber.d("HeadsetSettingFragment dispatchConnectStatus: disconnected: $disconnected")
            setDisconnectedUI(disconnected)
        }
        if (state.isReady) {
            updateConfigInfo()
        }
        HeadsetSettingContract.UIIntent.UpdateDeviceStatus(state).sendIntent()
    }

    @SuppressLint("MissingPermission")
    override fun bindState() {
        super.bindState()
        HeadsetSettingContract.UIState::otaState.bindStateNotNull {
            if (isOtaStateUnAvailable(it)) {
                // if ble disconnected, HeadsetActivity will show another error snackbar.
                // delay 1s to display the OTA abnormality snackbar.
                delay(1000L)
                Snackbar.make(
                    binding.root,
                    getString(R.string.ota_update_fail),
                    Snackbar.LENGTH_LONG
                ).show()
            }
        }
        HeadsetSettingContract.UIState::headsetConfig.bindStateNotNull {
            showHeadsetData(it)
        }
        HeadsetSettingContract.UIState::connectState.bindStateNotNull {
            stateManage.processUIStateByConnectState(it, this)
        }
        HeadsetSettingContract.UIState::currentDeviceAndConnectIsFailed.bindState {
            currentDevice = it.first
            setupToolbar(it.first?.name ?: currentlyPairedHeadsetConfigUseCase.getHeadsetName())
            // the current device is not connected
            if (it.first == null && it.second != null) {
                // try to connect to the last headphone immediately if user has paired the headphone and exited the headphone related pages then entered the details page for the first time
                if (it.second == false) {
                    stateManage.setTryConnectLastDevice()
                    Timber.d("HeadsetSettingFragment bindState: tryConnectLastDevice = true")
                } else { // connect last headphone is failed
                    setDisconnectedUI(true)
                }
            }
        }
        HeadsetSettingContract.UIState::lowLatency.bindStateNotNull {
            binding.headsetSettingScLowLatency.isChecked = it
        }
        HeadsetSettingContract.UIState::operationResult.bindStateNotNull {
            if (!it) {
                val status = binding.headsetSettingScLowLatency.isChecked
                binding.headsetSettingScLowLatency.isChecked = !status
            }
        }
        HeadsetSettingContract.UIState::powerPercent.bindStateNotNull {
            stateManage.processUIStateByPowerPercent(it, this)
        }

        HeadsetSettingContract.UIState::multipleBattery.bindStateNotNull {
            stateManage.updateMultipleBatteryUI(it, this)
        }

        HeadsetSettingContract.UIState::soundMode.bindStateNotNull {
            binding.headsetSettingSoundMode.text = when (it) {
                EqMode.OUTDOOR -> getString(R.string.outdoor)
                EqMode.UNDERWATER -> getString(R.string.sound_mode_underwater)
                EqMode.NORMAL -> getString(R.string.normal)
                else -> ""
            }
            soundMode = it
        }

        HeadsetSettingContract.UIState::dualDeviceConnectionEnabled.bindStateNotNull {
            with(binding.headsetSettingDualDeviceConnection) {
                if (it) {
                    this.setText(R.string.headset_setting_on)
                } else {
                    this.setText(R.string.headset_setting_off)
                }
            }
        }

        HeadsetSettingContract.UIState::bodySenseEnabled.bindStateNotNull {
            bodySenseOpened = it
            with(binding.headsetSettingBodySensing) {
                if (it) {
                    this.setText(R.string.headset_setting_on)
                } else {
                    this.setText(R.string.headset_setting_off)
                }
            }
        }

        HeadsetSettingContract.UIState::latestVersionResponse.bindState {
            latestVersionResponse = it
        }
        HeadsetSettingContract.UIState::needPopupUpdate.bindStateNotNull {
            if (it) {
                showUpdateDialog()
                HeadsetSettingContract.UIIntent.ResetNeedPopupUpdate.sendIntent()
            }
        }
        HeadsetSettingContract.UIState::sportsMode.bindStateNotNull {
            sportsMode = it
        }
        HeadsetSettingContract.UIState::ledState.bindStateNotNull {
            with(binding.headsetSettingIndicator) {
                if (it == LedStatus.CLOSE) {
                    this.setText(R.string.headset_setting_off)
                } else {
                    this.setText(R.string.headset_setting_on)
                }
            }
        }
        HeadsetSettingContract.UIState::sportStatus.bindStateNotNull {
            isSwimming = it.isOpenWaterSwimming || it.isPoolSwimming
            stateManage.updateSportUI(this, it)
        }
        HeadsetSettingContract.UIState::musicMode.bindStateNotNull {
            with(binding) {
                headsetSettingMusicMode.text =
                    if (it == MusicMode.ONLINE) {
                        getString(com.suunto.music.R.string.bluetooth_music)
                    } else {
                        getString(com.suunto.music.R.string.offline_music_title)
                    }
            }
        }
        HeadsetSettingContract.UIState::callStatus.bindStateNotNull {
            val calling = it == CallStatus.CALL
            stateManage.updateCallingState(calling)
            stateManage.controlSu07Feature(binding, calling)
        }
        HeadsetSettingContract.UIState::neckIntervalData.bindStateNotNull {
            with(binding.layoutNeckMoveMonitoring.neckMoveMonitoringState) {
                setText(if (it.enable) R.string.headset_setting_on else R.string.headset_setting_off)
            }
        }
        HeadsetSettingContract.UIState::currentSportType.bindState { sportType ->
            with(binding.layoutSportType.currentSportType) {
                text = sportType?.run { getString(this.getNameResId()) } ?: ""
            }
        }
        HeadsetSettingContract.UIState::syncingData.bindState { syncing ->
            with(binding) {
                syncDataLayer.isVisible = syncing
                syncHeadData.isVisible = syncing
                myHeadset.ivHeadsetDataSync.isVisible = syncing
                if (syncing) {
                    myHeadset.ivHeadsetDataSync.playAnimation()
                } else {
                    myHeadset.ivHeadsetDataSync.pauseAnimation()
                }
                myHeadset.deviceConnectAnimation.isVisible = !syncing
            }
        }

        HeadsetSettingContract.UIState::colorType.bindState {
            updateHeadphoneIcon(it)
        }
    }

    private fun updateHeadphoneIcon(colorType: ColorType) {
        // this will be deleted
    }

    @SuppressLint("MissingPermission")
    private fun showUpdateDialog() {
        latestVersionResponse?.let {
            updateDialogFragment = buildAlertDialogFragment(
                title = getString(
                    R.string.dialog_ota_upgrade_title,
                    currentDevice?.name ?: ""
                ),
                content = getString(
                    com.stt.android.R.string.watch_updates_version,
                    it.firmwareVersion ?: ""
                ),
                positiveText = getString(BaseR.string.ok),
                negativeText = getString(BaseR.string.cancel)
            ) {
                onPositiveClick = {
                    navigate(
                        R.id.fragment_ota_version_available,
                        bundleOf(LATEST_VERSION to it),
                        navOptions = null
                    )
                }
                show(
                    <EMAIL>,
                    HeadsetSettingFragment::class.java.name
                )
            }
        }
    }

    private fun setDisconnectedUI(disconnected: Boolean) {
        if (!disconnected) {
            alertDialogFragment?.dismissNow()
            alertDialogFragment = null
            showSettingUI()
        }
    }

    private fun showDisconnectedDialog() {
        alertDialogFragment = buildAlertDialogFragment(
            content = getString(R.string.dialog_disconnected_title),
            positiveText = getString(BaseR.string.ok)
        ) {
            onPositiveClick = {
                dismiss()
                isReconnectClicked = false
                alertDialogFragment = null
                enterPairAnotherPage(true)
            }
            show(
                <EMAIL>,
                HeadsetSettingFragment::class.java.name
            )
        }
    }

    private fun setItemClicked() {
        listOf(
            binding.headsetSettingSoundModeLabel,
            binding.headsetSettingSoundModeInfo,
            binding.headsetSettingSoundMode
        ).setOnClickListenerThrottled {
            soundModeClicked()
        }

        listOf(
            binding.headsetSettingDualDeviceConnection,
            binding.headsetSettingDualDeviceConnectionLabel,
            binding.headsetSettingDualDeviceConnectionInfo
        ).setOnClickListenerThrottled {
            navigate(R.id.fragment_dual_device)
        }

        listOf(
            binding.headsetSettingIndicator,
            binding.headsetSettingIndicatorInfo,
            binding.headsetSettingIndicatorLabel
        ).setOnClickListenerThrottled {
            navigate(R.id.fragment_indicator, bundleOf(INDICATOR_STATUS to sportsMode))
        }

        binding.headsetSettingFunctionIntroductionLabel.setOnClickListenerThrottled {
            navigate(R.id.fragment_instruction)
        }

        binding.headsetSettingUserGuideLabel.setOnClickListenerThrottled {
            supportedHeadsetDevices.getUserGuideUrl(baseHeadsetFeature)?.let {
                CustomTabsUtils.launchCustomTab(
                    requireContext(),
                    getString(it)
                )
            }
        }

        listOf(
            binding.ivConnectAnotherDevice,
            binding.headsetSettingPairAnotherDeviceLabel
        ).setOnClickListenerThrottled {
            enterPairAnotherPage()
        }

        listOf(
            binding.headsetSettingBodySensing,
            binding.headsetSettingBodySensingInfo,
            binding.headsetSettingBodySensingLabel
        ).setOnClickListenerThrottled {
            navigate(
                R.id.fragment_body_sensing,
                bundleOf(
                    BODY_SENSE_ENABLED to bodySenseOpened
                )
            )
        }

        binding.headsetSettingScLowLatency.setOnClickListenerThrottled {
            HeadsetSettingContract.UIIntent.SetLowLatency(
                if (binding.headsetSettingScLowLatency.isChecked) LowLatencyMode.ON else LowLatencyMode.OFF
            ).sendIntent()
        }
        listOf(
            binding.headsetSettingMusicMode,
            binding.headsetSettingMusicModeInfo,
            binding.headsetSettingMusicModeLabel
        ).setOnClickListenerThrottled {
            startActivity(Intent(context, OfflineMusicManageActivity::class.java))
        }

        listOf(
            binding.headsetSettingButtonCustomization,
            binding.headsetSettingButtonCustomizationInfo
        ).setOnClickListenerThrottled {
            navigate(R.id.fragment_customize_button)
        }

        listOf(
            binding.layoutNeckMoveMonitoring.neckMoveMonitoringLabel,
            binding.layoutNeckMoveMonitoring.neckMoveMonitoringIntroduce,
            binding.layoutNeckMoveMonitoring.neckMoveMonitoringState
        ).setOnClickListenerThrottled {
            navigate(R.id.neckMovementMonitoring)
        }

        listOf(
            binding.layoutNeckMobilityAssessment.neckMobilityAssessmentLabel,
            binding.layoutNeckMobilityAssessment.neckMobilityIntroduction,
            binding.layoutNeckMobilityAssessment.neckMobilityAssessmentNext
        ).setOnClickListenerThrottled {
            val params = bundleOf(KEY_SWIMMING to isSwimming)
            navigate(R.id.neckMobilityAssessment, params)
        }

        listOf(
            binding.layoutJumpAssessment.jumpAssessmentLabel,
            binding.layoutJumpAssessment.jumpAssessmentIntroduction,
            binding.layoutJumpAssessment.jumpAssessmentNext
        ).setOnClickListenerThrottled {
            // if headset is swimming, can't do jump assessment
            val params = bundleOf(KEY_SWIMMING to isSwimming)
            navigate(R.id.jump_assessment, params)
        }

        listOf(
            binding.layoutSportType.sportsSwitchLabel,
            binding.layoutSportType.sportsSwitchInstruction,
            binding.layoutSportType.currentSportType
        ).setOnClickListenerThrottled {
            navigate(R.id.sport_switch)
        }
        binding.syncDataLayer.setOnClickListener {
            // do nothing. only to intercept click event
        }
    }

    private fun enterPairAnotherPage(forceDisconnect: Boolean = false) {
        // not deal with disconnection in View Model, disconnect before connecting another device every time
        navigate(
            R.id.fragment_device_pair_list,
            if (forceDisconnect) bundleOf(DevicePairListFragment.FORCE_DISCONNECT to true) else null
        )
    }

    private fun soundModeClicked() {
        soundMode?.let {
            navigate(
                R.id.fragment_sound_mode,
                bundleOf(SoundModeFragment.KEY_SOUND_MODE to it)
            )
        }
    }

    private fun showHeadsetData(headsetConfig: HeadsetConfig) {
        if (supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
            with(binding.headsetSettingBodySensing) {
                if (headsetConfig.bodySenseEnabled) {
                    this.setText(R.string.headset_setting_on)
                } else {
                    this.setText(R.string.headset_setting_off)
                }
            }
        }
        if (supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
            binding.headsetSettingSoundMode.text = when (headsetConfig.soundMode) {
                EqMode.OUTDOOR -> getString(R.string.outdoor)
                EqMode.UNDERWATER -> getString(R.string.sound_mode_underwater)
                EqMode.NORMAL -> getString(R.string.normal)
                else -> ""
            }
        }
        if (supportedHeadsetDevices.supportLowLatency(baseHeadsetFeature)) {
            binding.headsetSettingScLowLatency.isChecked = headsetConfig.lowLatency
        }
        if (supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
            with(binding.headsetSettingIndicator) {
                if (headsetConfig.sportsMode == SportsMode.CLOSE) {
                    this.setText(R.string.headset_setting_off)
                } else {
                    this.setText(R.string.headset_setting_on)
                }
            }
        }
        if (supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
            with(binding.headsetSettingDualDeviceConnection) {
                if (headsetConfig.dualDeviceConnectionEnabled) {
                    this.setText(R.string.headset_setting_on)
                } else {
                    this.setText(R.string.headset_setting_off)
                }
            }
        }
    }

    private fun updateConfigInfo() {
        HeadsetSettingContract.UIIntent.GetDeviceCapability.sendIntent()
        if (supportedHeadsetDevices.supportGetColorType(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetColorType.sendIntent()
        }
        if (supportedHeadsetDevices.supportLanguageSetting(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SetAppLanguage.sendIntent()
        }
        if (supportedHeadsetDevices.supportUserInfoSetting(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SetUserInfo.sendIntent()
        }
        if (supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetBodySense.sendIntent()
        }
        if (supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetEQMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.LoadSportsMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetSupportDevicesConnect.sendIntent()
        }
        if (supportedHeadsetDevices.supportLowLatency(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetLowLatency.sendIntent()
        }
        if (supportedHeadsetDevices.supportMusicModeFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.UpdateMusicMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportCallStatusFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SyncCallStatus.sendIntent()
        }
        if (supportedHeadsetDevices.supportNeckMovementAssessment(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.UpdateNeckReminderInterval.sendIntent()
        }
        if (supportedHeadsetDevices.supportSportsSwitchFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SyncCurrentSportType.sendIntent()
        }
        val sportSupports = supportedHeadsetDevices.getSportSupports(baseHeadsetFeature)
        if (sportSupports.isSupportSport()) {
            HeadsetSettingContract.UIIntent.SyncCurrentSportStatus(sportSupports).sendIntent()
            HeadsetSettingContract.UIIntent.SyncHeadphoneData(sportSupports).sendIntent()
        }
    }

    private fun showSettingUI() {
        with(binding) {
            if (!supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
                headsetSettingBodySensing.gone()
                headsetSettingBodySensingInfo.gone()
                headsetSettingBodySensingLabel.gone()
                divider.gone()
            }
            if (!supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
                headsetSettingSoundMode.gone()
                headsetSettingSoundModeInfo.gone()
                headsetSettingSoundModeLabel.gone()
                dividerSoundMode.gone()
            }
            if (!supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
                headsetSettingIndicator.gone()
                headsetSettingIndicatorInfo.gone()
                headsetSettingIndicatorLabel.gone()
                divider3.gone()
            }
            if (!supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
                headsetSettingDualDeviceConnection.gone()
                headsetSettingDualDeviceConnectionInfo.gone()
                headsetSettingDualDeviceConnectionLabel.gone()
                divider2.gone()
            }
            if (!supportedHeadsetDevices.supportLowLatency(baseHeadsetFeature)) {
                headsetSettingScLowLatency.gone()
                headsetSettingLowLatencyLabel.gone()
                divider5.gone()
            }
            if (!supportedHeadsetDevices.supportButtonCustomization(baseHeadsetFeature)) {
                headsetSettingButtonCustomization.gone()
                headsetSettingButtonCustomizationInfo.gone()
                dividerButtonCustomization.gone()
            }
            if (!supportedHeadsetDevices.supportOfflineMusic(baseHeadsetFeature)) {
                headsetSettingMusicModeLabel.gone()
                headsetSettingMusicModeInfo.gone()
                headsetSettingMusicMode.gone()
                dividerMusicMode.gone()
            }
            if (!supportedHeadsetDevices.supportNeckMovementAssessment(baseHeadsetFeature)) {
                layoutNeckMoveMonitoring.layoutNeckMoveMonitoring.visibility = View.GONE
                layoutNeckMobilityAssessment.layoutNeckMobilityAssessment.visibility = View.GONE
            }
            if (!supportedHeadsetDevices.supportJumpAssessmentFeature(baseHeadsetFeature)) {
                layoutJumpAssessment.layoutJumpAssessment.visibility = View.GONE
            }
            if (!supportedHeadsetDevices.supportSportsSwitchFeature(baseHeadsetFeature)) {
                layoutSportType.layoutSportType.visibility = View.GONE
            }
            if (supportedHeadsetDevices.getUserGuideUrl(baseHeadsetFeature) == 0) {
                divider6.gone()
                headsetSettingUserGuideLabel.gone()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        rebuildShowingDialog(alertDialogFragment) {
            showDisconnectedDialog()
        }
        rebuildShowingDialog(updateDialogFragment) {
            showUpdateDialog()
        }
    }

    private fun rebuildShowingDialog(
        dialogFragment: AlertDialogFragment?,
        onAfterDismiss: () -> Unit
    ) {
        dialogFragment?.dialog?.let {
            if (it.isShowing) {
                dialogFragment.dismissAllowingStateLoss()
                onAfterDismiss.invoke()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stateManage.onDestroy()
        updateDialogFragment = null
        alertDialogFragment = null
    }

    companion object {
        const val KEY_SWIMMING = "key_swimming"
    }
}

const val SHOW_INSTRUCTION = "SHOW_INSTRUCTION"
