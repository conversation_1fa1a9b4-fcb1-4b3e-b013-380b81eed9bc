package com.suunto.headset.settingv2

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.content.res.Configuration
import androidx.compose.runtime.collectAsState
import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.repeatOnLifecycle
import by.kirich1409.viewbindingdelegate.viewBinding
import com.google.android.material.snackbar.Snackbar
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.smartdeviceota.LatestVersionInfo
import com.stt.android.utils.CustomTabsUtils
import com.suunto.extension.navigate
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SU10HeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.databinding.FragmentHeadsetSettingsV2Binding
import com.suunto.headset.model.SportType
import com.suunto.headset.model.SwitchSettingType
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.repository.CurrentlyPairedHeadsetConfigUseCase
import com.suunto.headset.ui.BODY_SENSE_ENABLED
import com.suunto.headset.ui.DevicePairListFragment
import com.suunto.headset.ui.INDICATOR_STATUS
import com.suunto.headset.ui.LATEST_VERSION
import com.suunto.headset.ui.OfflineMusicManageActivity
import com.suunto.headset.ui.SoundModeFragment
import com.suunto.headset.ui.ows.SoundModeSettingFragment.Companion.KEY_OWS_SOUND_MODE
import com.suunto.headset.ui.ows.SwitchSettingFragment.Companion.KEY_SWITCH_SETTING_TYPE
import com.suunto.headset.ui.view.AlertDialogFragment
import com.suunto.headset.ui.view.buildAlertDialogFragment
import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.ColorType
import com.suunto.soa.ble.control.attr.CommonSwitchState
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.OwsSoundMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.service.BleState
import com.suunto.soa.bluetooth.ota.service.isOtaStateUnAvailable
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass
import com.stt.android.R as BaseR

/**
 *  The headset Setting UI
 */
@AndroidEntryPoint
class HeadsetSettingFragment :
    BaseStateFragment<HeadsetSettingContract.UIIntent, HeadsetSettingContract.UIState, HeadsetSettingViewModel>() {

    val binding by viewBinding(FragmentHeadsetSettingsV2Binding::bind, R.id.container)
    private var updateDialogFragment: AlertDialogFragment? = null
    private var latestVersionResponse: LatestVersionInfo? = null

    private var currentDevice: BluetoothDevice? = null
    override fun getLayoutResId() = R.layout.fragment_headset_settings_v2

    override fun appbarElevationEnabled() = false

    override val viewModelClass: KClass<HeadsetSettingViewModel>
        get() = HeadsetSettingViewModel::class

    override fun showAboutDeviceMenu(): Boolean = true

    override fun showFindHeadphoneMenu(): Boolean =
        supportedHeadsetDevices.supportFindHeadphones(baseHeadsetFeature)

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    @Inject
    lateinit var currentlyPairedHeadsetConfigUseCase: CurrentlyPairedHeadsetConfigUseCase

    private var soundMode: EqMode? = null
    private var owsSoundMode: OwsSoundMode? = null
    private var otaTipBar: Snackbar? = null
    private var sportsMode: SportsMode? = null
    private var isSwimming = false
    private var bodySenseOpened: Boolean = false
    private var currentColorType = ColorType.BLACK

    override fun initView() {
        super.initView()
        binding.composeView.setContentWithM3Theme {
            HeadPhoneSettingScreen(
                settingItems = viewModel.settingItems.collectAsState().value,
                headphoneTopInfo = viewModel.headphoneTopInfo.collectAsState().value,
                onClickItem = ::handleClickEvent,
            )
        }
        showInstruction()
        setBackFinishActivity()
        handleObserveOTAState()
        handleObserveConnectState()
    }

    private fun handleClickEvent(type: SettingItemType) {
        when (type) {
            SettingItemType.SPORT_MODE -> navigate(R.id.sport_switch)
            SettingItemType.DUAL_DEVICE_CONNECTION -> navigate(
                R.id.fragment_dual_device,
                bundleOf(KEY_HEADPHONE_COLOR_RES_ID to currentColorType)
            )

            SettingItemType.BODY_SENSE -> navigate(
                R.id.fragment_body_sensing,
                bundleOf(
                    BODY_SENSE_ENABLED to bodySenseOpened
                )
            )

            SettingItemType.INDICATOR -> navigate(
                R.id.fragment_indicator,
                bundleOf(INDICATOR_STATUS to sportsMode)
            )

            SettingItemType.SOUND_MODE -> {
                when (baseHeadsetFeature) {
                    is SU10HeadsetFeature -> navigate(
                        R.id.ows_sound_mode,
                        bundleOf(KEY_OWS_SOUND_MODE to owsSoundMode)
                    )

                    else -> soundModeClicked()
                }
            }

            SettingItemType.MUSIC_MODE -> startActivity(
                Intent(
                    context,
                    OfflineMusicManageActivity::class.java
                )
            )

            SettingItemType.KEY_CUSTOMIZATION -> {
                when (baseHeadsetFeature) {
                    is SU10HeadsetFeature -> navigate(
                        R.id.ows_customization_key,
                        bundleOf(
                            KEY_OWS_CONNECTION_STATE to viewModel.state.owsConnectionState
                        )
                    )

                    else -> navigate(
                        R.id.fragment_customize_button,
                        bundleOf(KEY_HEADPHONE_COLOR_RES_ID to currentColorType)
                    )
                }
            }

            SettingItemType.NECK_MOVE_MONITORING -> navigate(R.id.neckMovementMonitoring)

            SettingItemType.NECK_MOBILITY_ASSESSMENT -> {
                val params =
                    bundleOf(KEY_SWIMMING to isSwimming)
                navigate(R.id.neckMobilityAssessment, params)
            }

            SettingItemType.JUMP_ASSESSMENT -> {
                // if headset is swimming, can't do jump assessment
                val params =
                    bundleOf(KEY_SWIMMING to isSwimming)
                navigate(R.id.jump_assessment, params)
            }

            SettingItemType.PAIR_ANOTHER_DEVICE -> enterPairAnotherPage()
            SettingItemType.RECONNECT -> reconnectDevice()
            SettingItemType.FUNCTION_INTRODUCTION -> navigate(R.id.fragment_instruction)
            SettingItemType.USER_GUIDE -> supportedHeadsetDevices.getUserGuideUrl(baseHeadsetFeature)
                ?.let {
                    CustomTabsUtils.launchCustomTab(
                        requireContext(),
                        getString(it)
                    )
                }

            SettingItemType.METRONOME -> navigate(
                R.id.metronome_setting,
                bundleOf(
                    KEY_METRONOME_DATA to viewModel.state.metronomeData
                )
            )

            SettingItemType.IMMERSIVE_MODE -> navigate(
                R.id.ows_switch_setting,
                args = bundleOf(
                    KEY_SWITCH_SETTING_TYPE to SwitchSettingType.IMMERSIVE_MODE.name,
                    KEY_SWITCH_STATE to viewModel.state.immersiveMode?.name
                )
            )

            SettingItemType.SPATIAL_AUDIO -> navigate(
                R.id.ows_switch_setting,
                args = bundleOf(
                    KEY_SWITCH_SETTING_TYPE to SwitchSettingType.SPATIAL_AUDIO.name,
                    KEY_SWITCH_STATE to viewModel.state.spatialAudioState?.name
                )
            )

            SettingItemType.AI_SETTING -> navigate(R.id.ows_ai_setting)
            SettingItemType.AI_CHAT -> navigate(R.id.headphone_ai_chat)
        }
    }

    private fun reconnectDevice() {
        val mac = currentlyPairedHeadsetConfigUseCase.getHeadsetLastMac()
        val pid = currentlyPairedHeadsetConfigUseCase.getHeadsetPid()
        HeadsetSettingContract.UIIntent.ConnectDevice(mac, pid).sendIntent()
    }

    private fun showInstruction() {
        val showInstruction = arguments?.getBoolean(SHOW_INSTRUCTION) ?: false
        if (showInstruction) {
            navigate(R.id.fragment_instruction)
            arguments?.putBoolean(SHOW_INSTRUCTION, false)
        }
    }

    private fun handleObserveOTAState() {
        viewModel.initOtaStateObserve()
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                runSuspendCatching {
                    viewModel.getDeviceRunning().getRunningStateFlow()
                        .map { it.isOtaState() }
                        .distinctUntilChanged()
                        .collect {
                            Timber.i("ota is in ota process: $it")
                            when {
                                it -> {
                                    otaTipBar = Snackbar.make(
                                        binding.root,
                                        getString(R.string.update_headphone),
                                        Snackbar.LENGTH_INDEFINITE
                                    )
                                        .apply { show() }
                                }

                                else -> {
                                    otaTipBar?.dismiss()
                                }
                            }
                            Timber.d("handleObserveOTAState otaProgress: $it")
                            viewModel.handleOTAState(it)
                            viewModel.disabledSettingItem(SettingItemType.PAIR_ANOTHER_DEVICE, true)
                        }
                }.onFailure { thr ->
                    Timber.w(thr, "subscribeOtaState failed.")
                }
            }
        }
    }

    override fun dispatchConnectStatus(state: BleState) {
        super.dispatchConnectStatus(state)
        viewModel.updateConnectionState(state.toConnectState())
        if (state.isReady) {
            updateConfigInfo()
        }
        HeadsetSettingContract.UIIntent.UpdateDeviceStatus(state).sendIntent()
    }

    @SuppressLint("MissingPermission")
    override fun bindState() {
        super.bindState()
        HeadsetSettingContract.UIState::otaState.bindStateNotNull {
            if (isOtaStateUnAvailable(it)) {
                // if ble disconnected, HeadsetActivity will show another error snackbar.
                // delay 1s to display the OTA abnormality snackbar.
                delay(1000L)
                Snackbar.make(
                    binding.root,
                    getString(R.string.ota_update_fail),
                    Snackbar.LENGTH_LONG
                ).show()
            }
        }
        HeadsetSettingContract.UIState::currentDeviceAndConnectIsFailed.bindState {
            currentDevice = it.first
            setupToolbar(it.first?.name ?: currentlyPairedHeadsetConfigUseCase.getHeadsetName())
        }

        HeadsetSettingContract.UIState::soundMode.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.SOUND_MODE,
                when (it) {
                    EqMode.OUTDOOR -> R.string.outdoor
                    EqMode.UNDERWATER -> R.string.sound_mode_underwater
                    EqMode.NORMAL -> R.string.normal
                    else -> null
                }
            )
            soundMode = it
        }

        HeadsetSettingContract.UIState::owsSoundMode.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.SOUND_MODE,
                when (it) {
                    OwsSoundMode.LEGENDARY -> R.string.sound_mode_legendary
                    OwsSoundMode.BASS_BOOST -> R.string.sound_mode_bass_boost
                    OwsSoundMode.TREBLE_BOOST -> R.string.sound_mode_treble_boost
                    OwsSoundMode.VOCAL_BOOST -> R.string.sound_mode_vocal_boost
                    OwsSoundMode.CUSTOMIZATION -> R.string.sound_mode_customization
                    else -> null
                }
            )
            owsSoundMode = it
        }

        HeadsetSettingContract.UIState::dualDeviceConnectionEnabled.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.DUAL_DEVICE_CONNECTION,
                if (it) {
                    R.string.headset_setting_on
                } else {
                    R.string.headset_setting_off
                }
            )
        }

        HeadsetSettingContract.UIState::bodySenseEnabled.bindStateNotNull {
            bodySenseOpened = it
            viewModel.updateSettingItemState(
                SettingItemType.BODY_SENSE,
                if (it) {
                    R.string.headset_setting_on
                } else {
                    R.string.headset_setting_off
                }
            )
        }

        HeadsetSettingContract.UIState::latestVersionResponse.bindState {
            latestVersionResponse = it
        }
        HeadsetSettingContract.UIState::needPopupUpdate.bindStateNotNull {
            if (it) {
                showUpdateDialog()
                HeadsetSettingContract.UIIntent.ResetNeedPopupUpdate.sendIntent()
            }
        }
        HeadsetSettingContract.UIState::ledState.bindStateNotNull {
            if (it == LedStatus.CLOSE) {
                sportsMode = SportsMode.CLOSE
                viewModel.updateSettingItemState(
                    SettingItemType.INDICATOR,
                    R.string.headset_setting_off
                )
            }
        }
        HeadsetSettingContract.UIState::sportsMode.bindStateNotNull {
            sportsMode = it
            viewModel.updateSettingItemState(
                SettingItemType.INDICATOR,
                when (it) {
                    SportsMode.CLOSE -> R.string.headset_setting_off
                    SportsMode.RUNNING -> R.string.custom_mode_running
                    SportsMode.SOS -> R.string.custom_mode_sos
                    SportsMode.TEAM -> R.string.custom_mode_constant_light
                    SportsMode.LEADER -> R.string.custom_mode_flashing
                    SportsMode.CYCLING -> R.string.custom_mode_cycling
                }
            )
        }
        HeadsetSettingContract.UIState::sportStatus.bindStateNotNull {
            isSwimming = it.isOpenWaterSwimming || it.isPoolSwimming
            viewModel.updateSportState(if (it.isSporting()) it else null)
        }
        HeadsetSettingContract.UIState::musicMode.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.MUSIC_MODE,
                when (it) {
                    MusicMode.ONLINE -> com.suunto.music.R.string.bluetooth_music
                    MusicMode.OFFLINE -> com.suunto.music.R.string.offline_music_title
                    else -> com.suunto.music.R.string.offline_music_title
                }
            )
        }
        HeadsetSettingContract.UIState::callStatus.bindStateNotNull {
            val calling = it == CallStatus.CALL
            viewModel.handleCalling(calling)
        }
        HeadsetSettingContract.UIState::neckIntervalData.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.NECK_MOVE_MONITORING,
                if (it.enable) R.string.headset_setting_on else R.string.headset_setting_off
            )
        }
        HeadsetSettingContract.UIState::currentSportType.bindState { sportType ->
            viewModel.updateSettingItemState(
                SettingItemType.SPORT_MODE,
                when (sportType) {
                    SportType.OPEN_WATER_SWIMMING -> com.stt.android.core.R.string.open_water_swimming
                    SportType.JUMP_ROPE -> com.stt.android.core.R.string.jump_rope
                    SportType.RUNNING -> com.stt.android.core.R.string.running
                    SportType.POOL_SWIMMING -> com.stt.android.core.R.string.swimming
                    null -> null
                }
            )
        }
        HeadsetSettingContract.UIState::syncingData.bindState { syncing ->
            viewModel.updateSyncingData(syncing)
        }

        HeadsetSettingContract.UIState::colorType.bindState {
            currentColorType = it
            getHeadphoneIconRes(it)?.let { iconRes ->
                viewModel.updateHeadphoneIcon(iconRes)
            }
        }

        HeadsetSettingContract.UIState::metronomeData.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.METRONOME,
                if (it.commonSwitchState == CommonSwitchState.OPEN) R.string.headset_setting_on else R.string.headset_setting_off
            )
        }

        HeadsetSettingContract.UIState::spatialAudioState.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.SPATIAL_AUDIO,
                if (it == CommonSwitchState.OPEN) R.string.headset_setting_on else R.string.headset_setting_off
            )
        }

        HeadsetSettingContract.UIState::immersiveMode.bindStateNotNull {
            viewModel.updateSettingItemState(
                SettingItemType.IMMERSIVE_MODE,
                if (it == CommonSwitchState.OPEN) R.string.headset_setting_on else R.string.headset_setting_off
            )
        }
    }

    private fun getHeadphoneIconRes(colorType: ColorType): Int? {
        return when (colorType) {
            ColorType.BLACK -> R.drawable.su09_black

            ColorType.RED ->
                R.drawable.su09_red

            ColorType.GRAY,
            ColorType.UNKNOWN ->
                R.drawable.su09_black

            ColorType.DEFAULT -> null
        }
    }

    @SuppressLint("MissingPermission")
    private fun showUpdateDialog() {
        latestVersionResponse?.let {
            updateDialogFragment = buildAlertDialogFragment(
                title = getString(
                    R.string.dialog_ota_upgrade_title,
                    currentDevice?.name ?: ""
                ),
                content = getString(
                    com.stt.android.R.string.watch_updates_version,
                    it.firmwareVersion ?: ""
                ),
                positiveText = getString(BaseR.string.ok),
                negativeText = getString(BaseR.string.cancel)
            ) {
                onPositiveClick = {
                    navigate(
                        R.id.fragment_ota_version_available,
                        bundleOf(LATEST_VERSION to it),
                        navOptions = null
                    )
                }
                show(
                    <EMAIL>,
                    HeadsetSettingFragment::class.java.name
                )
            }
        }
    }

    private fun enterPairAnotherPage(forceDisconnect: Boolean = false) {
        // not deal with disconnection in View Model, disconnect before connecting another device every time
        navigate(
            R.id.fragment_device_pair_list,
            if (forceDisconnect) bundleOf(DevicePairListFragment.FORCE_DISCONNECT to true) else null
        )
    }

    private fun soundModeClicked() {
        soundMode?.let {
            navigate(
                R.id.fragment_sound_mode,
                bundleOf(SoundModeFragment.KEY_SOUND_MODE to it)
            )
        }
    }

    private fun updateConfigInfo() {
        HeadsetSettingContract.UIIntent.GetDeviceCapability.sendIntent()
        if (supportedHeadsetDevices.supportOwsConnectionState(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetOwsConnectionState.sendIntent()
        }
        if (supportedHeadsetDevices.supportGetColorType(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetColorType.sendIntent()
        }
        if (supportedHeadsetDevices.supportLanguageSetting(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SetAppLanguage.sendIntent()
        }
        if (supportedHeadsetDevices.supportUserInfoSetting(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SetUserInfo.sendIntent()
        }
        if (supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetBodySense.sendIntent()
        }
        if (supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetEQMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.LoadSportsMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetSupportDevicesConnect.sendIntent()
        }
        if (supportedHeadsetDevices.supportLowLatency(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetLowLatency.sendIntent()
        }
        if (supportedHeadsetDevices.supportMusicModeFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.UpdateMusicMode.sendIntent()
        }
        if (supportedHeadsetDevices.supportCallStatusFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SyncCallStatus.sendIntent()
        }
        if (supportedHeadsetDevices.supportNeckMovementAssessment(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.UpdateNeckReminderInterval.sendIntent()
        }
        if (supportedHeadsetDevices.supportSportsSwitchFeature(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.SyncCurrentSportType.sendIntent()
        }
        val sportSupports = supportedHeadsetDevices.getSportSupports(baseHeadsetFeature)
        if (sportSupports.isSupportSport()) {
            HeadsetSettingContract.UIIntent.SyncCurrentSportStatus(sportSupports).sendIntent()
            HeadsetSettingContract.UIIntent.SyncHeadphoneData(sportSupports).sendIntent()
        }
        if (supportedHeadsetDevices.supportMetronome(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetMetronomeData.sendIntent()
        }
        if (supportedHeadsetDevices.supportSpatialAudio(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetSpatioAudioState.sendIntent()
        }
        if (supportedHeadsetDevices.supportImmersiveMode(baseHeadsetFeature)) {
            HeadsetSettingContract.UIIntent.GetImmersiveMode.sendIntent()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        rebuildShowingDialog(updateDialogFragment) {
            showUpdateDialog()
        }
    }

    private fun rebuildShowingDialog(
        dialogFragment: AlertDialogFragment?,
        onAfterDismiss: () -> Unit
    ) {
        dialogFragment?.dialog?.let {
            if (it.isShowing) {
                dialogFragment.dismissAllowingStateLoss()
                onAfterDismiss.invoke()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        updateDialogFragment = null
    }

    companion object {
        const val KEY_SWIMMING = "key_swimming"
        const val KEY_HEADPHONE_COLOR_RES_ID = "KEY_HEADPHONE_COLOR_RES_ID"
        const val KEY_METRONOME_DATA = "KEY_METRONOME_DATA"
        const val KEY_SWITCH_STATE = "KEY_SWITCH_STATE"
        const val KEY_OWS_CONNECTION_STATE = "ows_connection_state"
    }
}

const val SHOW_INSTRUCTION = "SHOW_INSTRUCTION"
