package com.suunto.headset.ui

import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.repeatOnLifecycle
import by.kirich1409.viewbindingdelegate.viewBinding
import com.google.android.material.snackbar.Snackbar
import com.stt.android.data.smartdeviceota.LatestVersionInfo
import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.watch.watchupdates.setVersionInfoLog
import com.suunto.extension.gone
import com.suunto.extension.navigate
import com.suunto.extension.popBackStack
import com.suunto.extension.visible
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetContract
import com.suunto.headset.databinding.FragmentOtaVersionAvailableBinding
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.viewmodel.HeadsetViewModel
import com.suunto.headset.viewmodel.OTAHelper
import com.suunto.headset.viewmodel.OTAUploadLogHelper
import com.suunto.soa.ble.service.BleState
import com.suunto.soa.bluetooth.ota.exception.OtaException
import com.suunto.soa.bluetooth.ota.service.OtaState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.BufferedReader
import java.io.InputStreamReader
import java.text.DecimalFormat
import java.util.Locale
import javax.inject.Inject
import kotlin.reflect.KClass
import com.stt.android.R as BaseR

/**
 *  OTA headset upgrade available version UI.
 */
@AndroidEntryPoint
class OTAVersionAvailableFragment :
    BaseStateFragment<HeadsetContract.UIIntent, HeadsetContract.UIState, HeadsetViewModel>() {

    private val binding by viewBinding(FragmentOtaVersionAvailableBinding::bind, R.id.sv_root)

    override fun getLayoutResId() = R.layout.fragment_ota_version_available

    override val viewModelClass: KClass<HeadsetViewModel>
        get() = HeadsetViewModel::class

    override fun getTitleResId(): Int = R.string.headphone_update

    private var latestVersionResponse: LatestVersionInfo? = null

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var downloadFileFeature: DownloadFileFeature

    @Inject
    lateinit var otaUploadLogHelper: OTAUploadLogHelper

    @Inject
    lateinit var otaHelper: OTAHelper

    @Inject
    lateinit var featureToggle: FeatureToggle

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    private var disconnected = false

    private var isOtaSucceed: Boolean = false

    private var otaEnabled = true

    override fun initView() {
        super.initView()
        checkOtaEnabled()
        handleObserveConnectState()
        handlePopBackAfterOtaSucceedOnBackground()
        latestVersionResponse = arguments?.get(LATEST_VERSION) as? LatestVersionInfo
        // use cached ota version information during ota process
        if (latestVersionResponse == null) {
            latestVersionResponse = lastOTAVersion
        } else { // the current ota version information is cached
            lastOTAVersion = latestVersionResponse
        }
        context?.let {
            latestVersionResponse?.let { versionRes ->
                HeadsetContract.UIIntent.StartGlobalObserveOTA(versionRes, it).sendIntent()
            }
        }
        with(binding) {
            stateLayout.tvUpdateNow.setOnClickListenerThrottled {
                stateLayout.tvUpdateNow.gone()
                latestVersionResponse?.let { latestVersionResponse ->
                    val isCheck =
                        supportedHeadsetDevices.isCheckDeviceAvailableBeforeOTA(baseHeadsetFeature)
                    HeadsetContract.UIIntent.OTAUpgrade(isCheck, latestVersionResponse).sendIntent()
                    setProgress(0F, false)
                }
            }
            tvUpdateVersion.text = getString(
                com.stt.android.R.string.watch_updates_version,
                latestVersionResponse?.firmwareVersion
            )
            tvUpdatePackageSize.text = getString(
                com.stt.android.R.string.watch_updates_size_in_megabytes,
                sizeInMegabytes(latestVersionResponse?.resourceSize?.toLong() ?: 0L)
            )
            learnMoreLayout.root.setOnClickListenerThrottled {
                supportedHeadsetDevices.getMoreInformationUrl(baseHeadsetFeature)
                    ?.takeIf { it != 0 }
                    ?.let { CustomTabsUtils.launchCustomTab(requireContext(), getString(it)) }
            }
            binding.versionLogLayout.root.isVisible =
                latestVersionResponse?.versionLog?.isNotBlank() == true
            latestVersionResponse?.versionLog?.let {
                setVersionInfoLog(versionLogLayout.versionLog, requireActivity(), it)
            }
            stateLayout.root.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                updateTvTipsVisibility()
            }
        }
    }

    /**
     * During the OTA process,
     * When the app is in the background for a while, popBackStack() may not work properly.
     */
    private fun handlePopBackAfterOtaSucceedOnBackground() {
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                if (isOtaSucceed) {
                    /**
                     * Solve the bug that navigation cannot be displayed normally
                     * after jumping to other fragments due to bluetoothEnabled status error.
                     */
                    delay(1000L)
                    popBackAndNavigationAfterOtaSucceed()
                    isOtaSucceed = false
                }
            }
        }
    }

    private fun checkOtaEnabled() {
        val isCheck =
            supportedHeadsetDevices.isCheckDeviceAvailableBeforeOTA(baseHeadsetFeature)
        viewModel.checkOTAEnabled(isCheck)
    }

    override fun bindState() {
        super.bindState()
        subscribeDownloadState()
        subscribeOtaState()
        HeadsetContract.UIState::otaState.bindStateNotNull { otaState ->
            when (otaState) {
                is OtaState.Failed -> {
                    otaEnabled = true
                    handleOTAFailed(
                        otaUploadLogHelper.getErrorMsg(
                            otaState,
                            context
                        ) ?: ""
                    )
                }

                is OtaState.OTACheckFailed,
                is OtaState.BatteryLower,
                is OtaState.PhoneCalling,
                is OtaState.OfflineMusicPlaying,
                is OtaState.MusicScanning -> {
                    otaEnabled = false
                    controlOTAView(otaUploadLogHelper.getErrorMsg(otaState, context) ?: "")
                }

                is OtaState.OTAEnabled -> {
                    otaEnabled = true
                    controlOTAView()
                }

                else -> {
                    // don't support
                }
            }
        }
    }

    override fun dispatchConnectStatus(state: BleState) {
        super.dispatchConnectStatus(state)
        if (isOtaSucceed) return
        this.disconnected = !state.isReady
        if (disconnected) {
            retry()
        }
    }

    private fun controlOTAView(errorMessage: String = "") {
        if (otaEnabled) {
            binding.stateLayout.tvUpdateNow.visible()
            binding.stateLayout.tvUpdateNow.setText(R.string.update_now)
            binding.stateLayout.errorTips.gone()
        } else {
            binding.stateLayout.tvUpdateNow.gone()
            binding.stateLayout.errorTips.visible()
            binding.stateLayout.errorTips.text = errorMessage
        }
    }

    private fun subscribeDownloadState() {
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.CREATED) {
                try {
                    downloadFileFeature.getDownloadState()
                        .filter { it != DownloadState.None }
                        .collect {
                            Timber.i("download state: ${it.javaClass.simpleName}")
                            when (it) {
                                is DownloadState.Finished -> {
                                    if (otaHelper.isTargetDownloadFile(
                                            it.saveFile,
                                            latestVersionResponse
                                        )
                                    ) {
                                        binding.stateLayout.downloadProgress.progress = 100
                                        binding.stateLayout.tvProgress.text =
                                            getString(
                                                R.string.download_progress_text,
                                                "${binding.stateLayout.downloadProgress.progress}%"
                                            )
                                        setProgress(0F)
                                    }
                                }

                                is DownloadState.Failed -> {
                                    showErrorIfNoNetwork {
                                        handleOTAFailed(getString(R.string.ota_file_download_failed))
                                    }
                                }

                                is DownloadState.Downloading -> {
                                    binding.stateLayout.downloadProgress.progress = it.progress
                                    binding.stateLayout.tvProgress.text =
                                        getString(
                                            R.string.download_progress_text,
                                            "${binding.stateLayout.downloadProgress.progress}%"
                                        )
                                }

                                else -> {}
                            }
                        }
                } catch (thr: Throwable) {
                    Timber.w(thr, "SubscribeDownloadState failed.")
                }
            }
        }
    }

    private fun subscribeOtaState() {
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.CREATED) {
                try {
                    otaHelper.getOtaStateFlow()
                        .distinctUntilChanged()
                        .collect {
                            Timber.i("ota state from ui: ${it.javaClass.simpleName}")
                            when (it) {
                                is OtaState.Preparing -> {
                                    setProgress(0F)
                                }

                                is OtaState.Transferring -> {
                                    val progress = it.transferProgress
                                    Timber.i(
                                        "ota state from ui set progress: ${progress.progress * 100F / progress.total}"
                                    )
                                    setProgress(progress.progress * 100F / progress.total)
                                }

                                is OtaState.Failed -> {
                                    showErrorIfNoNetwork {
                                        handleOTAFailed(
                                            if (disconnected) {
                                                getString(R.string.headphone_not_connected)
                                            } else {
                                                otaUploadLogHelper.getErrorMsg(it, context) ?: ""
                                            }
                                        )
                                    }
                                    showOTALogForFieldTester(it)
                                }

                                is OtaState.Succeed -> {
                                    isOtaSucceed = true
                                    setProgress(100f)
                                    popBackAndNavigationAfterOtaSucceed()
                                }

                                else -> {
                                    // do nothing
                                }
                            }
                        }
                } catch (thr: Throwable) {
                    Timber.w(thr, "subscribeOtaState failed.")
                }
            }
        }
    }

    private fun showOTALogForFieldTester(it: OtaState.Failed) {
        if ((it.thr is OtaException) && featureToggle[FeatureFlag.ShowHeadSetMacAddress]) {
            val otaErrorMessage = (it.thr as? OtaException)?.message
            val otaCausedMessage =
                (it.thr as? OtaException)?.cause?.message
            binding.stateLayout.errorTips.run {
                visible()
                text = buildString {
                    append(
                        otaErrorMessage?.let { errorMessage ->
                            errorMessage.byteInputStream().use { ins ->
                                val otaErrorReader =
                                    BufferedReader(InputStreamReader(ins))
                                otaErrorReader.readLine()
                            }
                        } ?: ""
                    )
                    append("\n")
                    append(
                        otaCausedMessage?.let { causedMessage ->
                            causedMessage.byteInputStream().use { ins ->
                                val otaCausedReader =
                                    BufferedReader(InputStreamReader(ins))
                                otaCausedReader.readLine()
                            }
                        } ?: ""
                    )
                }
            }
        } else {
            binding.stateLayout.errorTips.gone()
        }
    }

    private fun handleOTAFailed(tips: String) {
        Snackbar.make(
            binding.root,
            tips,
            Snackbar.LENGTH_LONG
        ).show()
        retry()
    }

    private fun setProgress(progress: Float, setProgressText: Boolean = true) {
        with(binding.stateLayout) {
            setProgressVisibility(true)
            tvTips.visible()
            tvUpdateNow.gone()
            downloadProgress.progress = progress.toInt()
            if (setProgressText) {
                tvProgress.text = if (progress < 0.01) {
                    getString(R.string.transfer_firmware_preparing)
                } else {
                    getString(
                        R.string.transfer_firmware_progress_text,
                        "${String.format(Locale.US, "%.2f", progress)}%"
                    )
                }
            }
        }
    }

    private fun retry() {
        with(binding.stateLayout) {
            setProgressVisibility(false)
            // if ota is not enabled, don't show retry button
            if (otaEnabled) {
                tvUpdateNow.text = getString(BaseR.string.try_again)
                tvUpdateNow.visible()
            }
        }
    }

    private fun setProgressVisibility(visible: Boolean) {
        with(binding.stateLayout) {
            tvProgress.isVisible = visible
            downloadProgress.isVisible = visible
        }
    }

    private fun updateTvTipsVisibility() {
        with(binding.stateLayout) {
            tvTips.isVisible = downloadProgress.isVisible || tvUpdateNow.isVisible
        }
    }

    private fun sizeInMegabytes(sizeInBytes: Long): String {
        val df = DecimalFormat("0.00")
        val inMegaBytes = sizeInBytes.toFloat() / (1024f * 1024f)
        return df.format(inMegaBytes)
    }

    private fun popBackAndNavigationAfterOtaSucceed() {
        popBackStack()
        navigate(
            R.id.fragment_ota_version_latest,
            bundleOf(
                REBOOT_WAIT to true,
                VERSION_LOG to latestVersionResponse?.versionLog
            ),
            navOptions = null
        )
    }
}

const val LATEST_VERSION = "LATEST_VERSION"
private var lastOTAVersion: LatestVersionInfo? = null
