package com.suunto.headset.ui

import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.graphics.Typeface
import android.provider.Settings
import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.DiffUtil
import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.suunto.extension.buildSpannableString
import com.suunto.extension.navigate
import com.suunto.extension.popBackStack
import com.suunto.extension.visible
import com.suunto.headset.R
import com.suunto.headset.contract.HeadsetPairContract
import com.suunto.headset.databinding.FragmentHeadsetPairListBinding
import com.suunto.headset.databinding.HeadsetScanFooterBinding
import com.suunto.headset.databinding.HeadsetScanHeaderBinding
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.viewmodel.HeadsetPairViewModel
import com.suunto.soa.ble.scanner.Device
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.reflect.KClass

/**
 *  The headset pair list UI
 */
@AndroidEntryPoint
class DevicePairListFragment :
    BaseStateFragment<HeadsetPairContract.UIIntent, HeadsetPairContract.UIState, HeadsetPairViewModel>() {

    private val binding by viewBinding(FragmentHeadsetPairListBinding::bind, R.id.coordinator)

    private val footerBinding by viewBinding(
        HeadsetScanFooterBinding::bind,
        viewProvider = { layoutInflater.inflate(R.layout.headset_scan_footer, binding.list, false) }
    )

    private val headerBinding by viewBinding(
        HeadsetScanHeaderBinding::bind,
        viewProvider = { layoutInflater.inflate(R.layout.headset_scan_header, binding.list, false) }
    )

    private var lastDevices: List<Device> = emptyList()

    companion object {
        const val COUNT_DOWN_SECONDS = 5
        const val FORCE_DISCONNECT = "force_disconnect"
    }

    @Inject
    lateinit var featureToggle: FeatureToggle

    override fun getLayoutResId() = R.layout.fragment_headset_pair_list

    override fun getTitleResId() = R.string.headphone_ui_status_pairing

    override val viewModelClass: KClass<HeadsetPairViewModel>
        get() = HeadsetPairViewModel::class

    private val deviceAdapter by lazy { DeviceListAdapter(requireContext(), featureToggle) }

    override fun appbarElevationEnabled() = false

    override fun initView() {
        super.initView()
        setPairList()
        setHeaderView()
        setFooterView()
        val disconnect = arguments?.get(FORCE_DISCONNECT) as? Boolean
        HeadsetPairContract.UIIntent.ScanDevice(disconnect ?: false).sendIntent()
        startCountDownTimer()
    }

    private fun setPairList() {
        deviceAdapter.onClickConnect = {
            HeadsetPairContract.UIIntent.GotoConnectDevice(it)
                .sendIntent()
        }

        deviceAdapter.onClickGotoBluetoothSetting = {
            goToBluetoothSetting()
        }
        deviceAdapter.setHasStableIds(true)
        with(binding.list) {
            setHasFixedSize(true)
            layoutManager = WrapContentLinearLayoutManager(context)
            adapter = deviceAdapter
            itemAnimator = null
        }
    }

    override fun bindState() {
        super.bindState()
        HeadsetPairContract.UIState::devices.bindStateNotNull {
            deviceAdapter.data = it
            val diffResult = withContext(Dispatchers.IO) {
                DiffUtil.calculateDiff(DeviceListAdapter.DiffCallBack(lastDevices, it), true)
            }
            diffResult.dispatchUpdatesTo(DeviceListUpdateCallback(deviceAdapter))
            headerBinding.tvScanHeader.text = if (deviceAdapter.data.isNotEmpty()) {
                getString(R.string.headphone_ui_status_found_device)
            } else {
                getString(R.string.headphone_ui_status_looking_for_devices)
            }
            lastDevices = it
        }
        HeadsetPairContract.UIState::connectTarget.bindStateNotNull {
            connectDevice(it.currentDevice, it.currentPid)
        }
    }

    private fun startCountDownTimer() {
        viewLifecycleOwner.lifecycle.coroutineScope.launch {
            viewLifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                delay((COUNT_DOWN_SECONDS * 1000).toLong())
                footerBinding.deviceScanInstructionsHeader.visible()
                footerBinding.deviceScanInstructionsSummary.visible()
            }
        }
    }

    private fun setFooterView() {
        deviceAdapter.removeAllFooterView()
        footerBinding.deviceScanContent.buildSpannableString(getString(R.string.headphone_ui_status_scan_info)) {
            highLightText(getString(R.string.headphone_ui_status_scan_info_bold_1)) {
                setStyle(Typeface.BOLD)
            }
            highLightText(getString(R.string.headphone_ui_status_scan_info_bold_2)) {
                setStyle(Typeface.BOLD)
            }
        }
        footerBinding.deviceScanInstructionsSummary.setOnClickListenerThrottled {
            goToBluetoothSetting()
        }
        deviceAdapter.addFooterView(footerBinding.root)
    }

    private fun setHeaderView() {
        deviceAdapter.removeAllHeaderView()
        deviceAdapter.addHeaderView(
            layoutInflater.inflate(
                R.layout.headset_scan_empty_item,
                binding.list,
                false
            )
        )
        deviceAdapter.addHeaderView(headerBinding.root)
    }

    private fun goToBluetoothSetting() {
        val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
        startActivity(intent)
    }

    private fun connectDevice(device: BluetoothDevice, currentPid: Int?) {
        val bundle = bundleOf(
            CONNECT_DEVICE_MAC to device.address,
            CONNECT_DEVICE_PID to currentPid,
            SHOW_INSTRUCTION to true
        )
        popBackStack()
        navigate(R.id.fragment_device_pairing, bundle, navOptions = null)
    }
}
