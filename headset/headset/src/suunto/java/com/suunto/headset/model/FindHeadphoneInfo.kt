package com.suunto.headset.model

import com.suunto.headset.R
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.soa.ble.control.attr.FindHeadphoneType

data class HeadphoneConnectionState(
    val left: HeadsetSettingContract.ConnectState,
    val right: HeadsetSettingContract.ConnectState,
)

data class FindHeadphoneInfo(
    val type: FindHeadphoneType,
    val playedSound: <PERSON>olean,
)

fun HeadphoneConnectionState.getAllHeadphoneConnectionState(): HeadsetSettingContract.ConnectState {
    return when {
        left == HeadsetSettingContract.ConnectState.Connected && right == HeadsetSettingContract.ConnectState.Connected -> HeadsetSettingContract.ConnectState.Connected
        left == HeadsetSettingContract.ConnectState.Disconnected || right == HeadsetSettingContract.ConnectState.Disconnected -> HeadsetSettingContract.ConnectState.Disconnected
        else -> HeadsetSettingContract.ConnectState.Connecting
    }
}

fun HeadphoneConnectionState.getDisconnectedMessage() =
    when {
        left == HeadsetSettingContract.ConnectState.Disconnected && right == HeadsetSettingContract.ConnectState.Disconnected -> R.string.both_headphone_disconnected
        right == HeadsetSettingContract.ConnectState.Disconnected -> R.string.right_headphone_disconnected
        left == HeadsetSettingContract.ConnectState.Disconnected -> R.string.left_headphone_disconnected
        else -> null
    }
