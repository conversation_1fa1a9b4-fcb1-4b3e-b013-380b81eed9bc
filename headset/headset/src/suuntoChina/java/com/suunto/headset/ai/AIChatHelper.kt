package com.suunto.headset.ai

import android.content.Context
import com.ss.bytertc.engine.RTCRoom
import com.ss.bytertc.engine.RTCRoomConfig
import com.ss.bytertc.engine.RTCVideo
import com.ss.bytertc.engine.UserInfo
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler
import com.ss.bytertc.engine.handler.IRTCVideoEventHandler
import com.ss.bytertc.engine.type.ChannelProfile
import com.suunto.headset.ai.MessageParser.parseData
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import java.nio.ByteBuffer
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AIChatHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private var rtcVideo: RTCVideo? = null
    private var rtcRoom: RTCRoom? = null

    private var aiMessageListener: AIMessageListener? = null
    private var isInitialized = false
    private var currentVoiceUserId = ""

    // cache current user's subtitles, because the subtitles may be received in multiple sentences
    private val currentSubtitles = hashMapOf<String, StringBuilder>()

    fun initialize(appId: String) {
        if (isInitialized) return
        rtcVideo = RTCVideo.createRTCVideo(
            context,
            appId,
            object : IRTCVideoEventHandler() {
                override fun onWarning(warn: Int) {
                    super.onWarning(warn)
                    Timber.w("AIChatHelper:onWarning: $warn")
                }

                override fun onError(err: Int) {
                    super.onError(err)
                    Timber.w("AIChatHelper:onError: $err")
                }
            },
            null,
            null
        )
        isInitialized = true
    }

    fun joinRoom(token: String, roomId: String, userId: String) {
        rtcVideo?.startAudioCapture()
        rtcRoom = rtcVideo?.createRTCRoom(roomId)
        val userInfo = UserInfo(userId, "")
        val roomConfig = RTCRoomConfig(ChannelProfile.CHANNEL_PROFILE_CHAT_ROOM, true, true, true)
        rtcRoom?.apply {
            joinRoom(token, userInfo, roomConfig)
            setRTCRoomEventHandler(object : IRTCRoomEventHandler() {
                override fun onTokenWillExpire() {
                    super.onTokenWillExpire()
                }

                override fun onRoomBinaryMessageReceived(uid: String?, message: ByteBuffer?) {
                    super.onRoomBinaryMessageReceived(uid, message)
                    message?.let { byteData ->
                        val aiMessages = parseData(byteData)
                        val firstMessage = aiMessages.firstOrNull()
                        firstMessage?.let { msg ->
                            if (!msg.paragraph) {
                                if (currentVoiceUserId != msg.userId) {
                                    val cachedSubtitles = currentSubtitles[msg.userId]
                                    if (cachedSubtitles == null) {
                                        currentSubtitles[msg.userId] = StringBuilder()
                                    } else cachedSubtitles.clear()
                                    currentVoiceUserId = msg.userId
                                    aiMessageListener?.onUserVoiceStateChanged(
                                        msg.userId,
                                        VoiceState.STARTED
                                    )
                                } else {
                                    if (msg.definite) {
                                        currentSubtitles[msg.userId]?.append(
                                            String(
                                                msg.text.toByteArray(),
                                                Charsets.UTF_8
                                            )
                                        )?.let {
                                            currentSubtitles[msg.userId] = it
                                        }
                                    }

                                    aiMessageListener?.onUserVoiceStateChanged(
                                        msg.userId,
                                        VoiceState.PROGRESS
                                    )
                                }
                            } else {
                                aiMessageListener?.onUserVoiceStateChanged(
                                    msg.userId,
                                    VoiceState.STOPPED
                                )
                                currentSubtitles[msg.userId]?.append(
                                    String(
                                        msg.text.toByteArray(),
                                        Charsets.UTF_8
                                    )
                                )?.let {
                                    aiMessageListener?.onSubtitleReceived(it.toString(), msg.userId)
                                }
                                currentSubtitles[msg.userId]?.clear()
                            }
                        } ?: aiMessageListener?.onUserVoiceStateChanged("", VoiceState.IDLE)
                    }
                }
            })
        }
    }

    fun destroy() {
        rtcRoom?.leaveRoom()
        rtcRoom?.destroy()
        RTCVideo.destroyRTCVideo()
        rtcVideo = null
        rtcRoom = null
        isInitialized = false
        currentVoiceUserId = ""
        aiMessageListener = null
    }

    fun setAIMessageListener(listener: AIMessageListener) {
        aiMessageListener = listener
    }
}
