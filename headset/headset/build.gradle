plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.moshi"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
}
android {
    namespace 'com.suunto.headset'
    buildFeatures.buildConfig = true
}

dependencies {
    implementation project(Deps.HeadsetSoaLibrary)
    implementation project(Deps.remoteBase)
    implementation project(Deps.domain)
    implementation project(Deps.appBase)
    implementation project(Deps.core)
    implementation project(Deps.datasource)
    implementation project(Deps.analytics)
    implementation(project(Deps.composeUi))
    implementation project(Deps.musicManager)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.smartDevice)
    implementation project(Deps.featureToggleApi)
    implementation libs.nordicsemi.ble
    implementation libs.rxjava2.android
    implementation libs.retrofit
    implementation libs.androidx.documentfile
    implementation libs.androidx.lifecycle.process
    implementation libs.androidx.browser
    implementation libs.androidx.corektx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.lottie
    implementation libs.easypermissions
    implementation libs.okhttp.logging
    implementation libs.viewbindingpropertydelegate.noreflection
    implementation libs.mpandroid
    compileOnly libs.androidx.work
    implementation libs.lottie.compose
    implementation libs.exoplayer

    chinaImplementation libs.volcenginertc
}
