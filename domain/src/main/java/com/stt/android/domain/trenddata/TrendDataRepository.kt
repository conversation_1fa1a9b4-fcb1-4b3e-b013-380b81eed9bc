package com.stt.android.domain.trenddata

import kotlinx.coroutines.flow.Flow

interface TrendDataRepository {
    /**
     * Get a list of [TrendData] entities between the specified timestamps.
     * @param fromTimestamp UTC timestamp in ms of the start of the day to query from
     * @param toTimestamp UTC timestamp in ms of the start of the day to query to
     * @return List of [TrendData] for the requested dates range if any exist
     */
    fun fetchTrendDataForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long,
        aggregated: Boolean
    ): Flow<List<TrendData>>

    /**
     * Save a list of [TrendData] in the both local and remote data source
     * @param trendDataList list of [TrendData] to save in the data source
     * @param replaceConflicts true if data needs to be replaced in case of conflict
     * @param requireBackendSync true if data needs to be synced to backend in the future
     * @return If any of the TrendData entries were stored in the local database. Can be false if the
     * list is empty or [replaceConflicts] is false and all of entries had been inserted already.
     */
    suspend fun saveTrendData(
        trendDataList: List<TrendData>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean

    fun fetchTrendDataExists(): Flow<Boolean>

    /**
     * @param checkLimit - Stop the count at threshold value. If user has data for longer periods
     *   of time and we just want to see if some threshold is met, using this optimizes the
     *   fetching to stop at the threshold value and not go through all of the data.
     */
    suspend fun fetchNumDaysWithTrendData(checkLimit: Int = Int.MAX_VALUE): Int
}
