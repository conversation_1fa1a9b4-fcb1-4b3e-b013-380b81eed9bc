package com.stt.android.domain.mapbox

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.CoroutineUseCase
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt

/**
 * Use case for fetching reverse geocoded place name, full address, city etc
 * Implements coordinate caching mechanisms to prevent reverse geocoding again coordinates that
 * has already been processed.
 *
 * Singleton so app wide instance cache can be used (ie feed to workout details)
 */
@Singleton
class FetchLocationNameUseCase @Inject constructor(
    private val geocodingAPI: GeocodingAPI,
) : CoroutineUseCase<Place?, FetchLocationNameUseCase.Params> {
    private val reverseGeocodedMap = mutableMapOf<String, Place?>()

    override suspend fun run(params: Params): Place? {
        // init geocoder by locale
        params.locale?.let {
            geocodingAPI.initGeocoder(it)
        }
        val (latitude, longitude) = if (params.useCoarseAccuracy) {
            params.latitude.roundToDecimals(COORDINATE_DECIMAL_PLACES_ACCURACY) to
                params.longitude.roundToDecimals(COORDINATE_DECIMAL_PLACES_ACCURACY)
        } else {
            params.latitude to params.longitude
        }
        val coordinatesKey = "$latitude $longitude"
        if (reverseGeocodedMap.containsKey(coordinatesKey)) {
            // We might have already tried to fetch place for the given coordinate, but get a null
            // result, so need to check if the key already exists.
            return reverseGeocodedMap[coordinatesKey]
        }
        if (params.useCacheOnly) {
            return null
        }

        // No cache try to fetch
        val place = runSuspendCatching {
            geocodingAPI.fetchPlaceForLocation(
                latitude = latitude,
                longitude = longitude
            )
        }.getOrElse { e ->
            Timber.w(e, "Failed to get location with ${e.message}")
            null
        }
        // Delete one entry if cache is full
        if (reverseGeocodedMap.size > NUMBER_OF_COORDINATES_CACHED) {
            reverseGeocodedMap.remove(reverseGeocodedMap.keys.toTypedArray()[0])
        }
        reverseGeocodedMap[coordinatesKey] = place
        return place
    }

    /**
     * @param latitude
     * @param longitude
     * @param useCoarseAccuracy whether the accuracy of the search should be coarse (not accurate)
     */
    data class Params(
        val latitude: Double,
        val longitude: Double,
        val useCoarseAccuracy: Boolean = false,
        val useCacheOnly: Boolean = false,
        val locale: Locale? = null
    )

    companion object {
        /**
         * Determines how many coordinates' addresses to be cached.
         * The oldest ones will be deleted.
         */
        private const val NUMBER_OF_COORDINATES_CACHED = 1000

        /**
         * We truncate the lat,long if need to n decimal places to reduce accuracy
         *
         * 1 Decimals is 11.1 KM of accuracy ie 0.1
         * 2 Decimals is 1.11 KM of accuracy ie 0.01
         * 3 Decimals is 111 m of accuracy ie 0.001
         * 4 Decimals is 11.1 m of accuracy ie 0.0001
         * ...
         * See http://wiki.gis.com/wiki/index.php/Decimal_degrees
         */
        private const val COORDINATE_DECIMAL_PLACES_ACCURACY = 4
    }

    private fun Double.roundToDecimals(decimals: Int): Double {
        var dotAt = 1
        repeat(decimals) { dotAt *= 10 }
        val roundedValue = (this * dotAt).roundToInt()
        return (roundedValue / dotAt) + (roundedValue % dotAt).toDouble() / dotAt
    }
}
