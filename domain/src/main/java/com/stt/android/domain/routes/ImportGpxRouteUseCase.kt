package com.stt.android.domain.routes

import android.net.Uri
import com.stt.android.domain.Point
import io.ticofab.androidgpxparser.parser.domain.Gpx
import io.ticofab.androidgpxparser.parser.domain.WayPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ImportGpxRouteUseCase
@Inject constructor(
    private val gpxLoader: GpxLoader,
    routeTool: RouteTool,
    private val waypointTools: WaypointTools,
) : BaseImportRouteUseCase(routeTool) {
    suspend fun convertGpxToRoute(
        uri: Uri,
        username: String,
        defaultRouteName: String,
    ): ImportRouteResult = withContext(Dispatchers.IO) {
        gpxLoader.loadGpx(uri)
            .convertToRoute(
                username = username,
                defaultRouteName = defaultRouteName,
            )
    }

    /*
     * Parsed GPX is mapped to Route objects. GPX route can be in 2 different structures which are "track" or "route".
     */
    private fun Gpx.convertToRoute(
        username: String,
        defaultRouteName: String,
    ): ImportRouteResult = convertToRoute(
        routePoints = routePoints,
        waypoints = wayPoints.asSequence()
            .distinctBy { it.latitude to it.longitude }
            .map { wayPoint ->
                wayPoint.toRoutePoint(
                    point = Point(wayPoint.longitude, wayPoint.latitude, wayPoint.elevation),
                    waypointTools = waypointTools,
                )
            }.toList(),
        username = username,
        routeName = routeName ?: defaultRouteName,
    )

    private companion object {
        val Gpx.routeName: String?
            get() {
                // Priorities:
                // 1. Route/track name
                // 2. Metadata name
                return if (isTrack) {
                    tracks[0].trackName
                } else {
                    routes[0].routeName
                } ?: metadata?.name
            }

        val Gpx.isTrack
            get() = tracks != null && tracks.isNotEmpty() && tracks[0].trackSegments.isNotEmpty()

        val Gpx.isRoute
            get() = routes != null && routes.isNotEmpty() && routes[0].routePoints.isNotEmpty()

        val Gpx.routePoints: List<Point>
            get() = when {
                isTrack -> tracks[0].trackSegments.flatMap { it.trackPoints }
                isRoute -> routes[0].routePoints
                else -> throw ImportRouteException()
            }.mapNotNull { p ->
                if (p.longitude != null && p.latitude != null) {
                    Point(
                        longitude = p.longitude,
                        latitude = p.latitude,
                        altitude = p.elevation,
                    )
                } else {
                    null
                }
            }.takeUnless(List<*>::isEmpty)
                ?: throw throw ImportRouteException(
                    when {
                        isTrack -> "GPX track cannot be empty"
                        else -> "GPX route cannot be empty"
                    }
                )

        fun WayPoint.toRoutePoint(point: Point, waypointTools: WaypointTools): Point {
            val isAutogenerated = this.desc == AUTO_GENERATED_WAYPOINT_IDENTIFIER
            return when {
                isAutogenerated && (this.type == "Begin" || this.type == "End") -> {
                    // Ignore autogenerated Begin and End waypoints
                    point.copy(name = null, type = null)
                }

                isAutogenerated && this.type == "Waypoint" -> {
                    // These are segment markers, do not create waypoints
                    point.copy(
                        name = AUTO_GENERATED_WAYPOINT_IDENTIFIER,
                        type = SEGMENT_MARKER_TYPE
                    )
                }
                // Rest are handled like waypoints.
                else -> point.copy(
                    name = this.name,
                    type = waypointTools.gpxWaypointTypeToInt(type)
                )
            }
        }
    }
}
