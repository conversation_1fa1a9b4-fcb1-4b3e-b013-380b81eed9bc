package com.stt.android.domain.subscriptions

import com.stt.android.coroutines.runSuspendCatching
import timber.log.Timber
import javax.inject.Inject

class HandlePendingPurchasesUseCase
@Inject constructor(
    private val pendingPurchaseRepository: PendingPurchaseRepository,
    private val playBillingHandler: PlayBillingHandler,
) {
    enum class Result {
        UPGRADED_TO_PREMIUM,
        ERROR,
        NO_PURCHASES_TO_HANDLE,
    }

    suspend fun handlePendingPurchases(): Result {
        val purchases = playBillingHandler.getPendingPurchases()

        return if (purchases.isEmpty()) {
            Result.NO_PURCHASES_TO_HANDLE
        } else {
            var success = false
            for (purchase in purchases) {
                runSuspendCatching {
                    Timber.d("handlePendingPurchases: storing and handling purchase")
                    pendingPurchaseRepository.storeAndSendPurchase(purchase)
                    playBillingHandler.acknowledgePurchase(requireNotNull(purchase.token))
                    success = true
                }.onFailure { e ->
                    Timber.w(e, "Failed to handle pending purchase")
                }
            }

            if (success) {
                Result.UPGRADED_TO_PREMIUM
            } else {
                Result.ERROR
            }
        }
    }
}
