package com.stt.android.domain;

import android.content.res.Resources;

/**
 * Implement this interface to provide search capabilities.
 */
public interface Filterable {
    /**
     * Creates a string representation of this object which can be used for filtering/searching purposes.
     *
     * @param constraints the constraints used to filter the data
     * @param resources   required for localization purposes
     * @return true if the constraints apply to this object
     */
    boolean applyFilter(CharSequence[] constraints, Resources resources);
}
