package com.stt.android.domain

import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.BackpressureStrategy
import io.reactivex.Completable
import io.reactivex.Flowable
import io.reactivex.Maybe
import io.reactivex.Observable
import io.reactivex.Scheduler
import io.reactivex.Single

/**
 * Base UseCase to hold some utility conversions common to all use cases
 */
abstract class BaseUseCase(
    protected val scheduler: Scheduler,
    protected val mainThread: Scheduler
) {

    protected fun <T> Flowable<T>.toV1(): rx.Observable<T> {
        return RxJavaInterop.toV1Observable(this)
    }

    protected fun <T> Observable<T>.toV1(backpressureStrategy: BackpressureStrategy = BackpressureStrategy.LATEST): rx.Observable<T> {
        return RxJavaInterop.toV1Observable(this, backpressureStrategy)
    }

    protected fun <T> Single<T>.toV1(): rx.Single<T> = RxJavaInterop.toV1Single(this)

    protected fun <T> Maybe<T>.toV1(): rx.Single<T> = RxJavaInterop.toV1Single(this)

    protected fun Completable.toV1(): rx.Completable = RxJavaInterop.toV1Completable(this)
}
