syntax = "proto3";

import "Wrappers.proto";
import "RoutesV2.proto";

option java_package = "com.sportstracker.apiserver.domain.route.v2.old";
option java_outer_classname = "RouteEntitiesProtosOld";

message RouteOld {
    StringValue id = 1;
    StringValue username = 2;
    StringValue description = 3;
    repeated string coauthors = 4;
    repeated int32 activities = 5;
    repeated .RouteSegment segments = 6;
    .RoutePoint startPoint = 7;
    .RoutePoint centerPoint = 8;
    .RoutePoint endPoint = 9;
    .RouteVisibility visibility = 10;
    Int64Value created = 11;
    FloatValue averageSpeed = 12;
    FloatValue totalDistance = 13; // Total distance in meters
    repeated StringValue comments = 14;
    Int64Value modified = 15;
    bool watchEnabled = 16;
    Int32Value watchSyncResponseCode = 17;
    .SyncState watchSyncState = 18;
    Int32Value watchRouteId = 19;
    Int64Value clientModifiedDate = 20;
}
