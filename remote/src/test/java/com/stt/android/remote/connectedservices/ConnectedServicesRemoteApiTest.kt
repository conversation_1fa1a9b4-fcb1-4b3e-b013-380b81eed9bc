package com.stt.android.remote.connectedservices

import com.stt.android.remote.response.AskoErrorWrapper
import com.stt.android.remote.response.AskoResponse
import io.reactivex.Completable
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class ConnectedServicesRemoteApiTest {
    @Mock private lateinit var connectedServicesSTRestApi: ConnectedServicesSTRestApi

    @Mock private lateinit var connectedServicesOAuth1Api: ConnectedServicesOAuth1Api
    private lateinit var connectedServicesRemoteApi: ConnectedServicesRemoteApi

    private fun <T> successResponseOf(payload: T) =
        AskoResponse(AskoErrorWrapper(200, ""), mapOf(), payload)

    @Before
    fun setup() {
        connectedServicesRemoteApi = ConnectedServicesRemoteApi(
            connectedServicesSTRestApi,
            connectedServicesOAuth1Api
        )
    }

    @Test
    fun `fetch available services list should access st remote api`() = runTest {
        val response = successResponseOf(RemotePartnerConnectionResponse(emptyList()))
        `when`(connectedServicesSTRestApi.fetchAvailableServices())
            .thenReturn(response)

        connectedServicesRemoteApi.fetchAvailableServices()

        verify(connectedServicesSTRestApi).fetchAvailableServices()
    }

    @Test
    fun `requestAuthForPartner should access st remote api`() {
        `when`(connectedServicesSTRestApi.requestAuthForPartner("", emptyMap()))
            .thenReturn(Completable.complete())

        connectedServicesRemoteApi.requestAuthForPartner("", emptyMap())
            .test()
            .assertNoErrors()
            .assertComplete()

        verify(connectedServicesSTRestApi).requestAuthForPartner("", emptyMap())
    }

    @Test
    fun `disconnectService should access st remote api`() {
        `when`(connectedServicesSTRestApi.disconnectService(anyString()))
            .thenReturn(Completable.complete())

        connectedServicesRemoteApi.disconnectService(anyString())
            .test()
            .assertNoErrors()
            .assertComplete()

        verify(connectedServicesSTRestApi).disconnectService(anyString())
    }
}
