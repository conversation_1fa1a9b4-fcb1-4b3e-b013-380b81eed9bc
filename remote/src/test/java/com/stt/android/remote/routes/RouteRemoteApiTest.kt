package com.stt.android.remote.routes

import com.sportstracker.apiserver.domain.route.v2.RouteEntitiesProtos.ListOfRoute
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import kotlin.test.assertTrue

@RunWith(MockitoJUnitRunner::class)
class RouteRemoteApiTest {
    @Mock
    private lateinit var routeRestApi: RouteRestApi

    @Mock
    private lateinit var topRouteRestApi: TopRouteRestApi
    private lateinit var routeRemoteApi: RouteRemoteApi
    private lateinit var remoteRoute: RemoteRoute

    @Before
    fun setup() {
        routeRemoteApi = RouteRemoteApi(routeRestApi, topRouteRestApi)
        val anyPoint = RemotePoint(0.0, 0.0)
        remoteRoute = RemoteRoute(
            key = "key",
            ownerUserName = "username",
            name = "name",
            visibility = "PRIVATE",
            activityIds = listOf(1),
            averageSpeed = 10.2,
            startPoint = anyPoint,
            centerPoint = anyPoint,
            stopPoint = anyPoint,
            segments = listOf(),
            createdDate = 0,
            totalDistance = 1.0
        )
    }

    @Test
    fun `saving route should make post request`() = runTest {
        // prepare
        `when`(routeRestApi.saveRoute(remoteRoute.toProto())).thenReturn(remoteRoute.toProto())
        routeRemoteApi.createRoute(remoteRoute)
        verify(routeRestApi).saveRoute(remoteRoute.toProto())
    }

    @Test
    fun `fetching routes should fetch routes from rest api`() = runTest {
        // prepare
        `when`(routeRestApi.fetchRoutes()).thenReturn(ListOfRoute.getDefaultInstance())
        assertTrue(routeRemoteApi.fetchRoutes().isEmpty())
        verify(routeRestApi).fetchRoutes()
    }

    @Test
    fun `deleting route should delete route using rest api`() = runTest {
        // prepare
        val key = "key"
        `when`(routeRestApi.deleteRoute(key)).thenReturn(Unit)
        routeRemoteApi.deleteRoute(key)
        verify(routeRestApi).deleteRoute(key)
    }

    @Test
    fun `updating route should update route using rest api`() = runTest {
        `when`(routeRestApi.updateRoute(any(), any())).thenReturn(Unit)
        routeRemoteApi.updateRoute(remoteRoute)
        verify(routeRestApi).updateRoute(remoteRoute.key, remoteRoute.toProto())
    }

    @Test
    fun `fetching a top route from rest api`() = runTest {
        `when`(topRouteRestApi.fetchTopRoute(any())).thenReturn(remoteRoute.toProto())
        val topRoute = routeRemoteApi.fetchTopRoute(remoteRoute.key)
        assertThat(topRoute).isInstanceOf(RemoteTopRoute::class.java)
        assertThat(topRoute!!.key).isEqualTo(remoteRoute.key)
    }

    @Test
    fun `fetching a top route from rest api with invalid key`() = runTest {
        `when`(topRouteRestApi.fetchTopRoute(any())).thenReturn(null)
        val topRoute = routeRemoteApi.fetchTopRoute(remoteRoute.key)
        assertThat(topRoute).isNull()
    }

    @Test
    fun `fetching a route from rest api`() = runTest {
        whenever(routeRestApi.fetchRoute(any())).thenReturn(remoteRoute.toProto())
        val result = routeRemoteApi.fetchRouteByKey("key")
        assertThat(result).isInstanceOf(RemoteRoute::class.java)
        assertThat(result.key).isEqualTo(remoteRoute.key)
    }

    @Test(expected = RuntimeException::class)
    fun `fetching a route from rest api with invalid key`() = runTest {
        val error = RuntimeException()
        whenever(routeRestApi.fetchRoute(any())).thenThrow(error)
        routeRemoteApi.fetchRouteByKey("key")
    }

    @Test
    fun `fetch routes paged limit 10 should return 8 available routes in 1 page`() = runTest {
        whenever(routeRestApi.fetchRoutesPage(any(), any(), any(), any(), any()))
            .thenReturn(
                ListOfRoute.newBuilder().addAllItems(
                    listOf(
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                    )
                ).build()
            )
        val actualPages = withTimeout(500L) {
            routeRemoteApi.fetchRoutesPaged(
                includeSegments = false,
                since = 0,
                limit = 10,
                newestFirst = true
            ).toList()
        }

        assertThat(actualPages.size).isEqualTo(1)
        assertThat(actualPages[0].size).isEqualTo(8)
    }

    @Test
    fun `fetch routes paged limit 5 should return 8 available routes in 2 pages`() = runTest {
        whenever(routeRestApi.fetchRoutesPage(any(), any(), any(), any(), any()))
            .thenReturn(
                ListOfRoute.newBuilder().addAllItems(
                    listOf(
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                    )
                ).build()
            )
            .thenReturn(
                ListOfRoute.newBuilder().addAllItems(
                    listOf(
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                        remoteRoute.toProto(),
                    )
                ).build()
            )

        val actualPages = withTimeout(500L) {
            routeRemoteApi.fetchRoutesPaged(
                includeSegments = false,
                since = 0,
                limit = 5,
                newestFirst = true
            ).toList()
        }

        assertThat(actualPages.size).isEqualTo(2)
        assertThat(actualPages[0].size).isEqualTo(5)
        assertThat(actualPages[1].size).isEqualTo(3)
    }

    @Test
    fun `fetch routes paged limit 10 should return one empty list if no routes`() = runTest {
        whenever(routeRestApi.fetchRoutesPage(any(), any(), any(), any(), any()))
            .thenReturn(ListOfRoute.getDefaultInstance())

        val actualPages = withTimeout(500L) {
            routeRemoteApi.fetchRoutesPaged(
                includeSegments = false,
                since = 0,
                limit = 10,
                newestFirst = true
            ).toList()
        }

        assertThat(actualPages.size).isEqualTo(1)
        assertThat(actualPages[0].size).isEqualTo(0)
    }
}
