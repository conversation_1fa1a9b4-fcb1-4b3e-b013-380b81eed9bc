package com.stt.android.workout.planner.editstep

import com.soy.algorithms.planner.WorkoutStep

enum class EditStepTargetMode {
    HEART_RATE,
    POWER,
    PACE,
    SPEED,
    NONE
}

fun WorkoutStep.Exercise.Target?.asModeEnum(): EditStepTargetMode = when (this) {
    is WorkoutStep.Exercise.Target.HeartRate -> EditStepTargetMode.HEART_RATE
    is WorkoutStep.Exercise.Target.Power -> EditStepTargetMode.POWER
    is WorkoutStep.Exercise.Target.Pace -> EditStepTargetMode.PACE
    is WorkoutStep.Exercise.Target.Speed -> EditStepTargetMode.SPEED
    null -> EditStepTargetMode.NONE
}
