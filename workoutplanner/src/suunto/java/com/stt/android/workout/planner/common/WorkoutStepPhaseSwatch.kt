package com.stt.android.workout.planner.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.utils.color
import com.stt.android.utils.displayNameResource

@Composable
fun WorkoutStepPhaseSwatch(
    phase: WorkoutStep.Exercise.Phase,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(16.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(phase.color)
    )
}

@Preview
@Composable
private fun WorkoutStepPhaseSwatchPreview() {
    AppTheme {
        Surface {
            Column(Modifier.padding(MaterialTheme.spacing.medium)) {
                for (phase in WorkoutStep.Exercise.Phase.values()) {
                    Row {
                        WorkoutStepPhaseSwatch(phase)
                        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                        Text(stringResource(phase.displayNameResource))
                    }
                }
            }
        }
    }
}
