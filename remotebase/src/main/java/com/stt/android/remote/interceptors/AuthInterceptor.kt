package com.stt.android.remote.interceptors

import com.stt.android.remote.AuthProvider
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Network interceptor which injects the injected [AuthProvider] into the request headers
 */
internal class AuthInterceptor(private val authProvider: AuthProvider) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        return chain.proceed(
            // Create a new request before proceeding with the headers
            // received from the auth provider
            chain.request()
                .newBuilder()
                .apply {
                    authProvider.getAuthHeaders()?.let { headers ->
                        for ((key, value) in headers) {
                            addHeader(key, value)
                        }
                    }
                }.build()
        )
    }
}
