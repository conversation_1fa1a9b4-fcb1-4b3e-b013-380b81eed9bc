package com.stt.android.remote

import javax.inject.Qualifier

@Qualifier
@MustBeDocumented
@Retention
annotation class SharedOkHttpClient

@Qualifier
@MustBeDocumented
@Retention
annotation class BrandForAsko

@Qualifier
@MustBeDocumented
@Retention
annotation class BaseUrlWithoutApiVersion

@Qualifier
@MustBeDocumented
@Retention
annotation class BaseUrlWithoutPath

@Qualifier
@MustBeDocumented
@Retention
annotation class BaseUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class BaseUrlV2

@Qualifier
@MustBeDocumented
@Retention
annotation class ServerStatusBaseUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class RemoteConfigBaseUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class AssetsBaseUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class RoutingBaseUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class SuuntoWeatherBackendUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class SuuntoCloudApiUrl

@Qualifier
@MustBeDocumented
@Retention
annotation class UserAgent

@Qualifier
@MustBeDocumented
@Retention
annotation class SharedSecret

@Qualifier
@MustBeDocumented
@Retention
annotation class MoshiAdapter

@Qualifier
@MustBeDocumented
@Retention
annotation class BackendTimeOffsetPrefKey

@Qualifier
@MustBeDocumented
@Retention
annotation class OpenWeatherMapAppId

@Qualifier
@MustBeDocumented
@Retention
annotation class RemoteConfigCacheDirectory

@Qualifier
@MustBeDocumented
@Retention
annotation class ComposeMapSnapshotOkHttpClient

@Qualifier
@MustBeDocumented
@Retention
annotation class OpenWeatherMapApi

@Qualifier
@MustBeDocumented
@Retention
annotation class SuuntoWeatherBackendApi

@Qualifier
@MustBeDocumented
@Retention
annotation class OpenWeatherMapAppKey

@Qualifier
@MustBeDocumented
@Retention
annotation class TidesBaseUrl
