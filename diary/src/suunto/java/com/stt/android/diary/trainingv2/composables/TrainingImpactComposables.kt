package com.stt.android.diary.trainingv2.composables

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.insights.coach.TrainingCoach
import com.stt.android.diary.insights.impact.ImpactTrainingModel
import com.stt.android.diary.insights.impact.ImpactTypeSelector
import com.stt.android.diary.insights.impact.ImpactUiState
import com.stt.android.diary.insights.impact.Impacts
import com.stt.android.home.diary.R

@Composable
fun TrainingImpact(
    impactUiState: ImpactUiState,
    onWorkoutImpactTypeClick: (WorkoutImpactType) -> Unit,
    onShowInfoClicked: (workoutImpactType: WorkoutImpactType) -> Unit,
    onTrainingModelInfoClicked: () -> Unit,
    modifier: Modifier = Modifier,
    impactCoachPhraseIds: List<Int>? = null,
) {
    Section(
        titleRes = R.string.training_hub_impact,
        modifier = modifier,
        onActionClick = {
            onShowInfoClicked(impactUiState.selectedImpactType)
        },
    ) {
        ImpactTypeSelector(
            selectedType = impactUiState.selectedImpactType,
            onWorkoutImpactTypeClick = onWorkoutImpactTypeClick,
            supportVerticalPadding = false,
            nonSelectedBackgroundColor = Color.Transparent,
        )
        AnimatedVisibility(visible = impactUiState.supportTrainingIntensityModel) {
            SubSection(
                titleRes = R.string.training_hub_sub_section_training_model,
                onActionClick = onTrainingModelInfoClicked
            ) {
                impactCoachPhraseIds?.let {
                    TrainingCoach(
                        phrasesIds = it,
                        modifier = Modifier.padding(bottom = MaterialTheme.spacing.medium)
                    )
                }
                impactUiState.trainingHubDateRange?.let {
                    ImpactTrainingModel(
                        currentTrainingIntensityModel = impactUiState.trainingIntensityModel.first,
                        comparisonTrainingIntensityModel = impactUiState.trainingIntensityModel.second,
                        trainingHubDateRange = it,
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Impacts(
            impacts = impactUiState.impacts,
            unclassifiedWorkoutsCount = impactUiState.unclassifiedWorkoutsCount,
            isExpanded = impactUiState.isExpanded,
            supportComparison = impactUiState.supportComparison,
        )
    }
}
