package com.stt.android.diary.dailyhealth

import com.stt.android.diary.DiaryFragmentInfo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class DailyHealthModule {
    @Binds
    @Named("DAILY_HEALTH_FRAGMENT_INFO")
    abstract fun bindDailyHealthFragmentInfo(creator: DailyHealthFragmentInfo): DiaryFragmentInfo
}
