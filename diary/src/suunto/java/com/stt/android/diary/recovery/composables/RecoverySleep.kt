package com.stt.android.diary.recovery.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.insights.common.SectionHeader
import com.stt.android.home.diary.R

@Composable
fun SleepGraphHeader(onShowSleepInfoSheet: () -> Unit, modifier: Modifier = Modifier) {
    SectionHeader(
        titleRes = R.string.training_hub_sleep,
        onActionClick = onShowSleepInfoSheet,
        modifier = modifier
            .background(MaterialTheme.colors.surface)
            .padding(horizontal = MaterialTheme.spacing.medium)
    )
}

@Preview
@Composable
private fun SleepGraphHeaderPreview() {
    AppTheme {
        SleepGraphHeader(
            onShowSleepInfoSheet = {},
        )
    }
}
