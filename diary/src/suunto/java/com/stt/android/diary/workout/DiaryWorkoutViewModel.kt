package com.stt.android.diary.workout

import androidx.lifecycle.viewModelScope
import androidx.paging.InvalidatingPagingSourceFactory
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController.WORKOUTS_PAGE_SIZE
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.diary.common.GraphInvalidator
import com.stt.android.diary.common.GraphInvalidatorDelegate
import com.stt.android.diary.graph.data.ChartPage
import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.diary.graph.paging.GraphId
import com.stt.android.diary.graph.paging.GraphPagingSource
import com.stt.android.diary.graph.paging.GraphPagingSourceFactory
import com.stt.android.diary.workout.paging.WorkoutPagingSource
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.primaryGraphDataTypes
import com.stt.android.domain.diary.models.secondaryGraphDataTypes
import com.stt.android.domain.workouts.GetPagedWorkoutHeadersUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.diary.SelectedGraphTimeRangeLiveData
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn

abstract class DiaryWorkoutViewModel(
    private val daoFactory: DaoFactory,
    coroutinesDispatchers: CoroutinesDispatchers,
    userController: CurrentUserController,
    protected val graphPagingSourceFactory: GraphPagingSourceFactory,
    private val getPagedWorkoutHeadersUseCase: GetPagedWorkoutHeadersUseCase,
    private val diaryPage: DiaryPage,
    val selectedGraphTimeRange: SelectedGraphTimeRangeLiveData,
    val selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData,
    val selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData,
) : CoroutineViewModel(coroutinesDispatchers), GraphInvalidator by GraphInvalidatorDelegate() {
    private var workoutPagingSource: WorkoutPagingSource? = null

    abstract val activityTypeFilter: WorkoutPagingSource.ActivityTypeFilter?

    val graphs: Flow<PagingData<ChartPage>> = Pager(
        config = PagingConfig(
            pageSize = 1,
            prefetchDistance = 1,
            enablePlaceholders = false,
            initialLoadSize = 2
        ),
        pagingSourceFactory = InvalidatingPagingSourceFactory {
            createGraphPagingSource().apply {
                addGraphPagingSource(this)
            }
        }
    ).flow
        .flowOn(Default)
        .cachedIn(viewModelScope)

    val workouts: Flow<PagingData<WorkoutHeader>> = Pager(
        config = PagingConfig(
            pageSize = WORKOUTS_PAGE_SIZE,
            prefetchDistance = WORKOUTS_PAGE_SIZE,
            enablePlaceholders = false,
            initialLoadSize = 2 * WORKOUTS_PAGE_SIZE
        ),
        pagingSourceFactory = InvalidatingPagingSourceFactory {
            WorkoutPagingSource(
                viewModelScope,
                daoFactory,
                getPagedWorkoutHeadersUseCase,
                userController,
                activityTypeFilter
            ).also { workoutPagingSource = it }
        }
    ).flow
        .flowOn(Default)
        .cachedIn(viewModelScope)

    init {
        selectedPrimaryGraphLiveData.initialize(DiaryPage.TRAINING)
        selectedSecondaryGraphLiveData.initialize(DiaryPage.TRAINING)
    }

    override fun onCleared() {
        super.onCleared()
        onClearedGraphPagingSources()
        workoutPagingSource?.unregisterObservers(daoFactory)
    }

    protected open fun createGraphPagingSource(): GraphPagingSource = graphPagingSourceFactory.create(
        viewModelScope,
        GraphId.WORKOUT,
        selectedGraphTimeRange.value ?: GraphTimeRange.entries.first(),
        selectedPrimaryGraphLiveData.value ?: diaryPage.primaryGraphDataTypes.first(),
        selectedSecondaryGraphLiveData.value ?: diaryPage.secondaryGraphDataTypes.firstOrNull(),
        pageToLoad
    )
}
