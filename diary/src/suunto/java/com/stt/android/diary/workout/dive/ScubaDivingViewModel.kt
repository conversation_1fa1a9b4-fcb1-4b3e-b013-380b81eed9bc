package com.stt.android.diary.workout.dive

import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.diary.graph.paging.GraphId
import com.stt.android.diary.graph.paging.GraphPagingSourceFactory
import com.stt.android.diary.workout.DiaryWorkoutViewModel
import com.stt.android.diary.workout.paging.WorkoutPagingSource
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.GetPagedWorkoutHeadersUseCase
import com.stt.android.home.diary.SelectedGraphTimeRangeLiveData
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ScubaDivingViewModel @Inject constructor(
    daoFactory: DaoFactory,
    coroutinesDispatchers: CoroutinesDispatchers,
    userController: CurrentUserController,
    graphPagingSourceFactory: GraphPagingSourceFactory,
    getPagedWorkoutHeadersUseCase: GetPagedWorkoutHeadersUseCase,
    selectedGraphTimeRange: SelectedGraphTimeRangeLiveData,
    selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData,
    selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData,
) : DiaryWorkoutViewModel(
    daoFactory,
    coroutinesDispatchers,
    userController,
    graphPagingSourceFactory,
    getPagedWorkoutHeadersUseCase,
    DiaryPage.SCUBA_DIVING,
    selectedGraphTimeRange,
    selectedPrimaryGraphLiveData,
    selectedSecondaryGraphLiveData,
) {
    init {
        selectedPrimaryGraphLiveData.initialize(DiaryPage.SCUBA_DIVING)
        selectedSecondaryGraphLiveData.initialize(DiaryPage.SCUBA_DIVING)
    }

    override val activityTypeFilter: WorkoutPagingSource.ActivityTypeFilter =
        WorkoutPagingSource.ActivityTypeFilter.OnlyActivityType(ActivityType.SCUBADIVING)

    override fun createGraphPagingSource() = graphPagingSourceFactory.create(
        viewModelScope,
        GraphId.SCUBA_DIVE,
        selectedGraphTimeRange.value ?: GraphTimeRange.entries.first(),
        GraphDataType.SCUBA_DIVE_COUNT,
        null,
        pageToLoad
    )
}
