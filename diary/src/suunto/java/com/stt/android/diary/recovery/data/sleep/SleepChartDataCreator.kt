package com.stt.android.diary.recovery.data.sleep

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.DAILY
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.MONTHLY
import com.stt.android.chart.api.model.ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SEVEN_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIXTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.SIX_WEEKS
import com.stt.android.chart.api.model.ChartGranularity.THIRTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS
import com.stt.android.chart.api.model.ChartGranularity.WEEKLY
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.usecases.bpmYRangeConverter
import com.stt.android.chart.impl.usecases.percentYRangeConverter
import com.stt.android.chart.impl.usecases.toNearest
import com.stt.android.controllers.UserSettingsController
import com.stt.android.diary.recovery.data.sleep.ChartDataCreator.Average
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.secondsFromStartOfDay
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.toHours
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.toLocalDate
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.toPercent
import com.stt.android.diary.recovery.model.SleepChartData
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.domain.sleep.Sleep
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min
import kotlin.time.Duration

class SleepChartDataCreator @Inject constructor(
    private val chartDataCreator: ChartDataCreator,
    private val userSettingsController: UserSettingsController,
) {

    fun createSleepDurationGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = chartDataCreator.createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.BAR,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.sleepDuration?.toHours() },
        dataConverter = { it },
        yRangeConverter = { _, max -> 
            if (max == 0f) {
                Pair(0f, 9f)  // When no data, display 0-9 range
            } else {
                Pair(0f, max.toNearest())
            }
        },
        selectionType = SleepChartSelectionType.SLEEP_DURATION,
    )

    fun createSleepRegularityGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ): SleepChartData.Series {
        var minX = 0.0
        var maxX = 0.0
        var minY = Float.MAX_VALUE
        var maxY = 0f
        val validSleepList = sleepList.filter {
            val longSleep = it.longSleep
            longSleep != null && longSleep.fellAsleep > 0L && longSleep.wokeUp > 0L && longSleep.wokeUp > longSleep.fellAsleep
        }
        val secondsInDay = TimeUnit.HOURS.toSeconds(24)
        fun sleepRange(sleep: Sleep): Pair<Float, Float> {
            val startSeconds = sleep.longSleep!!.fellAsleep.secondsFromStartOfDay()
            val endSeconds = sleep.longSleep!!.wokeUp.secondsFromStartOfDay()
            val startAdjustment = if (endSeconds >= startSeconds) {
                secondsInDay
            } else {
                0L
            }
            return (startSeconds + startAdjustment).toFloat() to (endSeconds + secondsInDay).toFloat()
        }

        val entries = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                validSleepList.map { sleep ->
                    val (low, high) = sleepRange(sleep)
                    minY = min(minY, low)
                    maxY = max(maxY, high)
                    SleepChartData.Entry(
                        x = sleep.timestamp.toLocalDate().toEpochDay(),
                        high = high,
                        low = low,
                    )
                }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                validSleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .map { (startOfWeek, weeklySleepList) ->
                        val weeklyLowAverage = Average()
                        val weeklyHighAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            val (low, high) = sleepRange(sleep)
                            weeklyLowAverage.feed(low)
                            weeklyHighAverage.feed(high)
                        }
                        minY = min(minY, weeklyLowAverage.result)
                        maxY = max(maxY, weeklyHighAverage.result)
                        SleepChartData.Entry(
                            x = startOfWeek.toEpochDay(),
                            high = weeklyHighAverage.result,
                            low = weeklyLowAverage.result,
                        )
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                validSleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .map { (epochMonth, monthlySleepList) ->
                        val monthlyLowAverage = Average()
                        val monthlyHighAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            val (low, high) = sleepRange(sleep)
                            monthlyLowAverage.feed(low)
                            monthlyHighAverage.feed(high)
                        }
                        minY = min(minY, monthlyLowAverage.result)
                        maxY = max(maxY, monthlyHighAverage.result)
                        SleepChartData.Entry(
                            x = epochMonth.toLong(),
                            high = monthlyHighAverage.result,
                            low = monthlyLowAverage.result,
                        )
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        
        // Handle empty data case - set reasonable Y axis range for sleep regularity (0 to 12:00)
        if (minY == Float.MAX_VALUE) {
            minY = 0f
            maxY = 43200f  // 12:00 in seconds (12 * 3600)
        }
        
        return SleepChartData.Series(
            selectionType = SleepChartSelectionType.SLEEP_REGULARITY,
            chartType = ChartType.CANDLESTICK,
            axisRange = SleepChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY.toDouble(),
                maxY = maxY.toDouble(),
            ),
            entries = listOf(entries),
            average = 0f,
        )
    }

    fun createSleepNapGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = chartDataCreator.createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.BAR,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.getMergedNap()?.duration?.toHours() },
        dataConverter = { it },
        yRangeConverter = { _, max ->
            if (max == 0f) {
                Pair(0f, 3f)  // When no data, display 0-3 range
            } else {
                Pair(0f, max.toNearest())
            }
        },
        selectionType = SleepChartSelectionType.NAP_DURATION,
    )

    fun createSleepTotalGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ): SleepChartData.Series {
        var minX = 0.0
        var maxX = 0.0
        var maxY = 0f
        val average = Average()
        val longSleepEntries = mutableListOf<SleepChartData.Entry>()
        val napEntries = mutableListOf<SleepChartData.Entry>()
        when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                sleepList.forEach { sleep ->
                    val x = sleep.timestamp.toLocalDate().toEpochDay()
                    sleep.longSleep
                        ?.sleepDuration
                        ?.takeIf { it > Duration.ZERO }
                        ?.let { value ->
                            longSleepEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                    sleep.getMergedNap()
                        ?.duration
                        ?.takeIf { it > Duration.ZERO }
                        ?.let { value ->
                            napEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                    sleep.totalSleepDuration
                        .takeIf { it > Duration.ZERO }
                        ?.inWholeSeconds
                        ?.toFloat()
                        ?.let { value ->
                            maxY = max(maxY, value)
                            average.feed(value)
                        }
                }
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .forEach { (startOfWeek, weeklySleepList) ->
                        val weeklyLongSleepAverage = Average()
                        val weeklyNapAverage = Average()
                        val weeklyTotalAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            sleep.longSleep?.sleepDuration?.takeIf { it > Duration.ZERO }?.let { value ->
                                weeklyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.getMergedNap()?.duration?.takeIf { it > Duration.ZERO }?.let { value ->
                                weeklyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration.takeIf { it > Duration.ZERO }
                                ?.inWholeSeconds
                                ?.toFloat()
                                ?.let { value ->
                                    weeklyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = startOfWeek.toEpochDay()
                        weeklyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        weeklyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        weeklyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .forEach { (epochMonth, monthlySleepList) ->
                        val monthlyLongSleepAverage = Average()
                        val monthlyNapAverage = Average()
                        val monthlyTotalAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            sleep.longSleep?.sleepDuration?.takeIf { it > Duration.ZERO }?.let { value ->
                                monthlyLongSleepAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.getMergedNap()?.duration?.takeIf { it > Duration.ZERO }?.let { value ->
                                monthlyNapAverage.feed(value.inWholeSeconds.toFloat())
                            }
                            sleep.totalSleepDuration.takeIf { it > Duration.ZERO }
                                ?.inWholeSeconds
                                ?.toFloat()
                                ?.let { value ->
                                    monthlyTotalAverage.feed(value)
                                    average.feed(value)
                                }
                        }
                        val x = epochMonth.toLong()
                        monthlyLongSleepAverage.result.takeIf { it > 0f }?.let { value ->
                            longSleepEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        monthlyNapAverage.result.takeIf { it > 0f }?.let { value ->
                            napEntries.add(
                                SleepChartData.Entry(
                                    x = x,
                                    high = value.toHours(),
                                )
                            )
                        }
                        monthlyTotalAverage.result.takeIf { it > 0f }?.let { value ->
                            maxY = max(maxY, value)
                        }
                    }
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        
        // Handle empty data case - set reasonable Y axis range for sleep total time (0 to 9 hours)
        if (maxY == 0f) {
            maxY = 32400f  // 9 hours in seconds (9 * 3600)
        }
        
        return SleepChartData.Series(
            selectionType = SleepChartSelectionType.TOTAL_TIME,
            chartType = ChartType.BAR,
            axisRange = SleepChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 0.0,
                maxY = maxY.toHours().toNearest().toDouble(),
            ),
            entries = listOf(longSleepEntries.toList(), napEntries.toList()),
            average = average.result.toHours(),
        )
    }

    fun createBloodOxygenGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = chartDataCreator.createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.maxSpO2 },
        dataConverter = { it.toPercent() },
        yRangeConverter = { startY, endY ->
            if (endY == 0f) {
                Pair(40f, 100f)  // When no data, display 40-100% range
            } else {
                percentYRangeConverter(startY, endY)
            }
        },
        selectionType = SleepChartSelectionType.MAX_SLEEP_SPO2,
    )

    fun createMinHrDuringSleepGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = chartDataCreator.createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.minHr?.inBpm?.toFloat() },
        dataConverter = { it },
        yRangeConverter = { startY, endY ->
            if (startY == 0f && endY == 0f) {
                // When no data, return 40-120 range directly
                Pair(40f, 100f)
            } else {
                bpmYRangeConverter(startY, endY)
            }
        },
        selectionType = SleepChartSelectionType.MIN_SLEEP_HR,
    )

    fun createAvgHrDuringSleepGraphData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
    ) = chartDataCreator.createSleepGraphData(
        chartGranularity = chartGranularity,
        chartType = ChartType.LINE,
        from = from,
        to = to,
        sleepList = sleepList,
        dataExtractor = { it.longSleep?.avgHr?.inBpm?.toFloat() },
        dataConverter = { it },
        yRangeConverter = { startY, endY ->
            if (startY == 0f && endY == 0f) {
                // When no data, return 40-120 range directly
                Pair(40f, 100f)
            } else {
                bpmYRangeConverter(startY, endY)
            }
        },
        selectionType = SleepChartSelectionType.AVG_SLEEP_HR,
    )
} 
