package com.stt.android.diary.recovery.composables

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.chart.impl.screen.components.ChartHighlight
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.recovery.v2.RecoveryV2Event
import com.stt.android.domain.diary.models.RecoveryZone

@Composable
fun RecoveryChart(
    recoveryScore: Int?,
    recoveryZone: RecoveryZone?,
    chartData: ChartData?,
    chartHighlight: ChartHighlightViewData,
    dateTimeText: String,
    isDaily: Boolean,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            RecoveryStateHeader(
                recoveryScore = recoveryScore ?: 0,
                recoveryZone = recoveryZone ?: RecoveryZone.NO_DATA,
                onEvent = onEvent,
                dateTimeText = dateTimeText,
                isDaily = isDaily,
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                ),
            )
            ChartHighlight(
                viewData = chartHighlight,
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .align(Alignment.BottomCenter),
            )
        }
        CommonChart(
            chartData = chartData,
            height = 250.dp,
            onEntrySelected = { entryX ->
                onEvent(RecoveryV2Event.ShowHighlight(null, entryX))
            },
            onNoEntrySelected = {
                onEvent(RecoveryV2Event.HideHighlight)
            },
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium)
        )
    }
} 
