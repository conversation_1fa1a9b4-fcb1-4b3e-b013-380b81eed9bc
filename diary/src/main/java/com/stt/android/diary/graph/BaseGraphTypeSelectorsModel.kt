package com.stt.android.diary.graph

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.text.SpannableString
import android.text.Spanned
import android.view.View
import androidx.annotation.StyleRes
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.widget.ImageViewCompat
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.ThemeColors
import com.stt.android.diary.common.GraphTypeSelectorsHolder
import com.stt.android.diary.graph.extensions.locString
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.CenteredImageSpan

@EpoxyModelClass
abstract class BaseGraphTypeSelectorsModel : EpoxyModelWithHolder<GraphTypeSelectorsHolder>() {
    override fun getDefaultLayout() = R.layout.graph_type_selectors

    @EpoxyAttribute
    var usingImperialSystem: Boolean = false

    @EpoxyAttribute
    var graphTimeRange: GraphTimeRange? = null

    @EpoxyAttribute
    var selectedLeftGraphType: GraphDataType? = null

    @EpoxyAttribute
    var selectedRightGraphType: GraphDataType? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var primarySelectorClicked: View.OnClickListener? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var secondarySelectorClicked: View.OnClickListener? = null

    @EpoxyAttribute
    @get:StyleRes
    var theme: Int = 0

    override fun bind(holder: GraphTypeSelectorsHolder) {
        val context = holder.leftName.context

        selectedLeftGraphType?.let {
            holder.leftSelector.visibility = View.VISIBLE
            holder.leftName.text = getLeftNameSpannable(context, it)
            holder.leftUnit.text =
                getUnitText(context, it, graphTimeRange ?: GraphTimeRange.EIGHT_WEEKS)
            holder.leftSelector.setOnClickListener(primarySelectorClicked)
            holder.leftSelector.isClickable = primarySelectorClicked != null
        } ?: run {
            holder.leftSelector.visibility = View.INVISIBLE
        }

        selectedRightGraphType?.let {
            holder.rightSelector.visibility = View.VISIBLE
            holder.rightName.text = getRightNameSpannable(context, it)
            holder.rightUnit.text =
                getUnitText(context, it, graphTimeRange ?: GraphTimeRange.EIGHT_WEEKS)
            holder.rightSelector.setOnClickListener(secondarySelectorClicked)
            holder.rightSelector.isClickable = secondarySelectorClicked != null
        } ?: run {
            holder.rightSelector.visibility = View.INVISIBLE
            holder.leftLegend.visibility = View.GONE // Hide legend when secondary graph is missing
        }

        ImageViewCompat.setImageTintMode(holder.leftLegend, PorterDuff.Mode.SRC_IN)
        ImageViewCompat.setImageTintList(
            holder.leftLegend,
            ColorStateList.valueOf(
                with(ContextThemeWrapper(context, theme)) {
                    ThemeColors.resolveColor(this, com.stt.android.R.attr.suuntoDiaryAccentColor)
                }
            )
        )
    }

    // AutoSizeTextMode requires known width. Using ImageSpan to show the arrow next to the text.
    // With compound drawables, the arrow would lie at the far edge of the TextView.
    private fun getLeftNameSpannable(
        context: Context,
        graphDataType: GraphDataType
    ): SpannableString {
        return if (primarySelectorClicked != null) {
            // Adding extra whitespaces. One is replaced by the image, the rest act as padding
            SpannableString(context.getString(graphDataType.locString) + "   ").apply {
                val imageSpan = CenteredImageSpan(context, R.drawable.graph_type_arrow)
                setSpan(imageSpan, length - 1, length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            }
        } else {
            SpannableString(context.getString(graphDataType.locString))
        }
    }

    // AutoSizeTextMode requires known width. Using ImageSpan to show the arrow next to the text.
    // With compound drawables, the arrow would lie at the far edge of the TextView.
    private fun getRightNameSpannable(
        context: Context,
        graphDataType: GraphDataType
    ): SpannableString {
        return if (secondarySelectorClicked != null) {
            // Adding extra whitespaces. One is replaced by the image, the rest act as padding
            SpannableString("   " + context.getString(graphDataType.locString)).apply {
                val imageSpan = CenteredImageSpan(context, R.drawable.graph_type_arrow)
                setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            }
        } else {
            SpannableString(context.getString(graphDataType.locString))
        }
    }

    protected fun getUnitTextFromString(
        context: Context,
        stringRes: Int?
    ): String = stringRes?.let { resId ->
        context.getString(resId)
    } ?: ""

    open fun getUnitText(
        context: Context,
        graphDataType: GraphDataType,
        graphTimeRange: GraphTimeRange
    ): String = ""
}
