package com.stt.android.diary.progress.chart

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.ChartHighlighter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.utils.Utils
import com.stt.android.diary.common.DragToHighlightGraphHandler
import com.stt.android.diary.progress.Vo2MaxChartViewData
import com.stt.android.diary.progress.ext.colorRes
import com.stt.android.diary.progress.ext.maxValue
import com.stt.android.diary.progress.ext.minValue
import com.stt.android.diary.tss.chartrendering.FlexibleYAxisLabelRenderer
import com.stt.android.diary.tss.chartrendering.YAxisWithFixedLongestLabel
import com.stt.android.diary.tss.chartrendering.localDateXAxisRenderer
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.extensions.currentlyHighlightedIndex
import com.stt.android.extensions.disableGestures
import com.stt.android.extensions.drawHighlightForRegion
import com.stt.android.extensions.highlightIndex
import com.stt.android.extensions.setupXAxisAndYAxisOnRight
import com.stt.android.extensions.xForValue
import com.stt.android.extensions.yForValue
import com.stt.android.home.diary.R
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.Locale
import kotlin.math.abs
import kotlin.math.roundToInt
import com.stt.android.R as BR

internal class ProgressVo2MaxChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LineChart(context, attrs, defStyleAttr) {

    lateinit var chartData: Vo2MaxChartViewData.Loaded

    var indexToHighlight: Int? = null

    var onValueHighlighted: ((index: Int?) -> Unit)? = null

    private var boundChartData: Vo2MaxChartViewData.Loaded? = null

    private val vo2MaxStateBarPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val currentStateHighlightPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var hasSetupChart = false

    fun setup() {
        if (!hasSetupChart) {
            setupChart()
            hasSetupChart = true
        }

        setupXAxisRange(chartData.timeRange, chartData.startDate, chartData.endDate)
        axisRight.axisMinimum = chartData.yAxisRange.min
        axisRight.axisMaximum = chartData.yAxisRange.max

        if (boundChartData != chartData) {
            renderer = ProgressVo2MaxRenderer(
                context,
                this,
                animator,
                viewPortHandler,
                chartData.yAxisRange,
            )
            mXAxisRenderer = localDateXAxisRenderer(
                context = context,
                timeRange = chartData.timeRange,
                startDate = chartData.startDate,
                endDate = chartData.endDate,
                dayOfWeekField = chartData.calendarProvider.getWeekFields().dayOfWeek(),
                newProgressStyle = true,
                newLabelStyle = true,
            )
            mAxisRendererRight = FlexibleYAxisLabelRenderer(
                getYLabels(),
                viewPortHandler,
                axisRight,
                mRightAxisTransformer
            )

            data = LineData().apply {
                chartData.chartSets.forEach { addDataSet(createDataSet(it, chartData.timeRange)) }
            }

            addLimitLines()

            invalidate()

            boundChartData = chartData
        }

        if (currentlyHighlightedIndex != indexToHighlight) {
            highlightIndex(indexToHighlight)
        }
    }

    private fun addLimitLines() {
        axisRight.removeAllLimitLines()
        chartData.yAxisRange.limitValues.forEach { limitValue ->
            axisRight.addLimitLine(
                LimitLine(limitValue).apply {
                    lineColor = ContextCompat.getColor(context, BR.color.cloudy_grey)
                    lineWidth = 1f
                    val dashLength = Utils.convertDpToPixel(4f)
                    enableDashedLine(dashLength, dashLength / 2f, 0f)
                }
            )
        }
    }

    private fun setupChart() {
        disableGestures()
        mAxisRight =
            YAxisWithFixedLongestLabel(resources.getDimension(R.dimen.tss_graph_horizontal_space_for_labels))
        // Y axis renderer still keeps a reference to the stale YAxis object, but the renderer
        // will be replaced in setup().
        setupXAxisAndYAxisOnRight()
        axisRight.xOffset = 4f
        // Don't use grid, only limit lines. This allows custom positioning and styling.
        axisRight.setDrawGridLines(false)
        axisRight.setDrawLimitLinesBehindData(true)
        xAxis.axisLineWidth = 1f
        xAxis.axisLineColor = ContextCompat.getColor(context, BR.color.medium_grey)

        extraLeftOffset = Utils.convertPixelsToDp(extraLeftOffset) + LEFT_BAR_WIDTH

        setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry, h: Highlight) {
                val entryIndex = e.x.roundToInt()
                if (entryIndex != indexToHighlight) {
                    onValueHighlighted?.invoke(entryIndex)
                }
            }

            override fun onNothingSelected() {
                if (indexToHighlight != null) {
                    onValueHighlighted?.invoke(null)
                }
            }
        })

        setOnTouchListener(DragToHighlightGraphHandler(this))

        setHighlighter(object : ChartHighlighter<LineChart>(this) {
            override fun getDistance(x1: Float, y1: Float, x2: Float, y2: Float): Float {
                return abs(x1 - x2)
            }
        })
    }

    private fun setupXAxisRange(
        timeRange: GraphTimeRange,
        startDate: LocalDate,
        endDate: LocalDate
    ) {
        val days = ChronoUnit.DAYS.between(startDate, endDate)
        val (min, max) = when (timeRange) {
            GraphTimeRange.SIX_MONTHS -> {
                // note: the offset can be further optimized, since we display the data by weeks
                val offset = ChronoUnit.WEEKS.duration.toDays() / 2f
                (0f - offset) to (days + offset)
            }

            GraphTimeRange.CURRENT_YEAR -> {
                val offset = ChronoUnit.MONTHS.duration.toDays() / 2f
                (0f - offset) to (days - offset)
            }

            GraphTimeRange.EIGHT_YEARS -> {
                val offset = ChronoUnit.YEARS.duration.toDays() / 2f
                (0f - offset) to (days - offset)
            }

            // GraphTimeRange.CURRENT_WEEK,
            // GraphTimeRange.CURRENT_MONTH,
            // GraphTimeRange.SIX_WEEKS
            else -> {
                val offset = ChronoUnit.DAYS.duration.toDays().toFloat()
                (0f - offset) to (days + offset)
            }
        }
        xAxis.axisMinimum = min
        xAxis.axisMaximum = max
    }

    private fun getYLabels(): Map<Float, String> = chartData.yAxisRange.labelValues.associateWith {
        String.format(Locale.getDefault(), "%.0f", it)
    }

    private fun createDataSet(
        values: List<Entry>,
        graphTimeRange: GraphTimeRange,
    ) = LineDataSet(values, "").apply {
        lineWidth = 2f
        circleColors = values.map { it.data as Int }
        val drawCircles = when (graphTimeRange) {
            GraphTimeRange.CURRENT_WEEK,
            GraphTimeRange.CURRENT_MONTH,
            GraphTimeRange.CURRENT_YEAR,
            GraphTimeRange.SIX_MONTHS,
            GraphTimeRange.EIGHT_YEARS -> {
                circleRadius = 3f
                circleHoleRadius = 1.5f
                setDrawCircleHole(true)
                true
            }

            // GraphTimeRange.SIX_WEEKS
            else -> {
                // The design requires dot to be drawn instead of circle.
                // We use circle to emulate the dot for clarity.
                circleRadius = 1f
                setDrawCircleHole(false)
                values.size <= 1
            }
        }
        setDrawCircles(drawCircles)
        setDrawValues(false)
        setDrawFilled(false)
        setDrawHighlightIndicators(true)
        setDrawHorizontalHighlightIndicator(false)
        mode = LineDataSet.Mode.LINEAR
        axisDependency = YAxis.AxisDependency.RIGHT
    }

    override fun onDraw(canvas: Canvas) {
        val highlightedStates = listOf(Vo2MaxState.SUPERIOR, Vo2MaxState.EXCELLENT)
        val color = ContextCompat.getColor(context, highlightedStates[0].colorRes)
        currentStateHighlightPaint.color = Color.argb(
            (Color.alpha(color) * 0.1f).roundToInt(),
            Color.red(color),
            Color.green(color),
            Color.blue(color),
        )
        val ranges = chartData.yAxisRange.ranges
        val indexes = highlightedStates.map { state ->
            ranges.indexOfFirst { it.state == state }
        }
        drawHighlightForRegion(
            canvas,
            xAxis.axisMinimum,
            chartData.yAxisRange.minValue(indexes.min()),
            xAxis.axisMaximum,
            chartData.yAxisRange.maxValue(indexes.max()),
            currentStateHighlightPaint,
        )

        super.onDraw(canvas)

        drawLeftBar(canvas)

        if (valuesToHighlight()) {
            (renderer as ProgressVo2MaxRenderer).drawHighlightedFreely(canvas, mIndicesToHighlight)
        }
    }

    private fun drawLeftBar(canvas: Canvas) {
        val barRight = xForValue(xAxis.axisMinimum)
        val barLeft = barRight - Utils.convertDpToPixel(LEFT_BAR_WIDTH)
        val lineOffset = Utils.convertDpToPixel(0.5f) // axis line or limit line
        val ranges = chartData.yAxisRange.ranges
        ranges.indices.windowed(size = 2, step = 2, partialWindows = false) {
            val startIndex = it[0]
            val endIndex = it[1]
            canvas.drawRect(
                barLeft,
                yForValue(chartData.yAxisRange.maxValue(endIndex)) + lineOffset,
                barRight,
                yForValue(chartData.yAxisRange.minValue(startIndex)) - lineOffset,
                vo2MaxStateBarPaint.apply {
                    color = ContextCompat.getColor(context, ranges[startIndex].state.colorRes)
                }
            )
        }
    }

    private companion object {
        private const val LEFT_BAR_WIDTH = 4f
    }
}
