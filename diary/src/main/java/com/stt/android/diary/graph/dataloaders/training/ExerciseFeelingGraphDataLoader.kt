package com.stt.android.diary.graph.dataloaders.training

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.diary.graph.dataloaders.base.TrainingGraphDataLoader
import com.stt.android.domain.diary.models.ChartPoint
import com.stt.android.domain.diary.models.GraphData
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ExerciseFeelingGraphDataLoader @Inject constructor(
    getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
    currentUserController: CurrentUserController,
    private val summaryExtensionDao: SummaryExtensionDao
) : TrainingGraphDataLoader(getWorkoutHeadersForRangeUseCase, currentUserController) {

    private data class ExerciseFeelingGraphData(override val data: Map<GraphDataType, List<ChartPoint>>) :
        GraphData {
        companion object {
            fun create(timedFeelings: List<Pair<Long, Int>>): GraphData = ExerciseFeelingGraphData(
                mapOf(
                    GraphDataType.EXERCISE_FEEL to timedFeelings.map { (startTime, feeling) ->
                        ChartPoint(
                            startTime,
                            feeling.toFloat()
                        )
                    }
                )
            )
        }
    }

    override suspend fun createGraphData(headers: List<WorkoutHeader>): GraphData {
        val timedFeelings = headers.mapNotNull { header ->
            withContext(Dispatchers.IO) {
                summaryExtensionDao.findByIdAwait(header.id)
                    ?.feeling?.takeIf { it > 0 }
                    ?.run { header.startTime to this }
            }
        }

        return ExerciseFeelingGraphData.create(timedFeelings)
    }
}
