package com.stt.android.diary.statistics

import com.stt.android.common.ui.ViewPagerFragmentCreator
import com.stt.android.diary.DiaryFragmentInfo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class StatisticsModule {

    @Binds
    @Named("STATISTICS_FRAGMENT_CREATOR")
    abstract fun bindFragmentCreator(creator: StatisticsFragmentCreator): ViewPagerFragmentCreator

    @Binds
    @Named("STATISTICS_FRAGMENT_INFO")
    abstract fun bindStatisticsFragmentInfo(creator: StatisticsFragmentInfo): DiaryFragmentInfo
}
