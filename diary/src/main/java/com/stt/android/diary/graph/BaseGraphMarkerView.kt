package com.stt.android.diary.graph

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.withTranslation
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.TrendsAnalytics
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.diary.graph.data.ChartPage
import com.stt.android.domain.diary.models.AdditionalData
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphGranularity
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.TextFormatter
import java.util.Locale
import kotlin.math.roundToLong
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@SuppressLint("ViewConstructor")
abstract class BaseGraphMarkerView(
    context: Context,
    private val diaryPage: DiaryPage,
    private val chartPage: ChartPage,
    private val trendsAnalytics: TrendsAnalytics
) : MarkerView(context, R.layout.graph_marker_view) {
    private val primaryValue: TextView = findViewById(R.id.diaryGraphPrimaryValue)
    private val primaryLabel: TextView = findViewById(R.id.diaryGraphPrimaryLabel)
    private val secondaryValue: TextView = findViewById(R.id.diaryGraphSecondaryValue)
    private val secondaryLabel: TextView = findViewById(R.id.diaryGraphSecondaryLabel)
    private val markerTime: TextView = findViewById(R.id.diaryGraphTime)
    private val markerContainer: ConstraintLayout =
        findViewById(com.stt.android.R.id.marker_container)

    private val primaryGraphDataType get() = chartPage.primaryChartData.dataType
    private val secondaryGraphDataType get() = chartPage.secondaryChartData?.dataType
    protected val granularity get() = chartPage.primaryChartData.timeFrame.granularity

    private var analyticsEventSent = false
    protected val highlightLine: View = View(context)
    private val maxNarrowedWidth = resources.getDimensionPixelSize(CR.dimen.content_max_width)
    private val totalDisplayWidth = resources.displayMetrics.widthPixels

    override fun draw(canvas: Canvas, posX: Float, posY: Float) {
        // copied the logic from the library with a small change in canvas.translate(...)
        canvas.withTranslation(calculateCenteredPosX(), 0f) {
            // translate to the correct position and draw
            draw(canvas)
        }
    }

    override fun refreshContent(entry: Entry, highlight: Highlight) {
        (highlightLine.layoutParams as? LayoutParams)?.let {
            val dpInPixels = resources.displayMetrics.density
            it.topMargin = (dpInPixels * 8).toInt() // Align with topmost grid line
            it.width = (dpInPixels * 0.5).toInt()
            // xOffset is the space between screen edge and this marker view
            // i.e. the offset for the highlight line.
            val xOffset = (calculateCenteredPosX()).toInt()
            it.leftMargin = highlight.drawX.toInt() - (it.width / 2) - xOffset
            it.height =
                highlight.drawY.toInt() - it.topMargin + 1 // +1 to remove gap between the line and the bar
            highlightLine.layoutParams = it
        }

        setContent(entry)
        if (!analyticsEventSent) {
            analyticsEventSent = true
            trackAmplitudeEvent()
        }
        super.refreshContent(entry, highlight)
    }

    private fun calculateCenteredPosX(): Float {
        // If the view is narrowed, we use the max width as a reference
        val fullWidth = totalDisplayWidth.coerceAtMost(maxNarrowedWidth)
        return (fullWidth - markerContainer.width).toFloat() / 2
    }

    fun resetAnalyticsEventSent() {
        analyticsEventSent = false
    }

    private fun setContent(entry: Entry) {
        val additionalData = entry.data as AdditionalData
        setPointData(entry, additionalData)
        setPointPeriod(additionalData.timeStamp)
    }

    private fun setPointData(entry: Entry, additionalData: AdditionalData) {
        getFormattedValueWithLabel(primaryGraphDataType, entry.y, additionalData)
            ?.let { result ->
                setPrimaryData(result.valueWithUnit, result.label)
            }
        secondaryGraphDataType?.let {
            getFormattedValueWithLabel(
                it,
                additionalData.lineValue,
                additionalData
            )?.let { result ->
                setSecondaryDataVisible(true)
                setSecondaryData(result.valueWithUnit, result.label)
            }
        } ?: setSecondaryDataVisible(false)
    }

    protected fun getNapData(value: Float?): Pair<String, String> {
        val primaryLabel = context.getString(
            if (granularity == GraphGranularity.DAILY) {
                BaseR.string.diary_graph_highlight_nap
            } else {
                BaseR.string.diary_graph_highlight_average_nap
            }
        )
        val seconds = ((value ?: 0f) * 3600f).roundToLong()
        return "${TimeUtils.durationInSecondsToHoursAndMinutes(seconds, roundMinutes = true)} ${
            context.getString(CR.string.hour)
        }" to
            primaryLabel
    }

    private fun setPrimaryData(value: String, label: String?) {
        primaryValue.text = value
        primaryLabel.text = label
    }

    private fun setSecondaryData(value: String, label: String?) {
        secondaryValue.text = value
        secondaryLabel.text = label
    }

    private fun setSecondaryDataVisible(visible: Boolean) {
        val visibility = if (visible) View.VISIBLE else View.GONE
        secondaryValue.visibility = visibility
        secondaryLabel.visibility = visibility
    }

    private fun setPointPeriod(startTimestamp: Long) {
        markerTime.text = when (granularity) {
            GraphGranularity.DAILY -> {
                TextFormatter.formatDate(context, startTimestamp, true)
            }
            GraphGranularity.WEEKLY -> {
                val endTimestamp =
                    TimeUtils.epochToLocalZonedDateTime(startTimestamp).plusDays(6).toEpochMilli()
                getDateRangeText(startTimestamp, endTimestamp)
            }
            GraphGranularity.MONTHLY -> {
                val endTimestamp = TimeUtils.getEndDateOfMonth(startTimestamp).toEpochMilli()
                getDateRangeText(startTimestamp, endTimestamp)
            }
            GraphGranularity.YEARLY -> {
                val endTimestamp = TimeUtils.getEndDateOfYear(startTimestamp).toEpochMilli()
                getDateRangeText(startTimestamp, endTimestamp)
            }
        }
    }

    private fun getDateRangeText(startTimestamp: Long, endTimestamp: Long): String {
        val stringTemplate = if (diaryPage == DiaryPage.SLEEP) "%s\n- %s" else "%s - %s"
        return String.format(
            Locale.US,
            stringTemplate,
            TextFormatter.formatDate(context, startTimestamp, true),
            TextFormatter.formatDate(context, endTimestamp, true)
        )
    }

    private fun trackAmplitudeEvent() {
        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.SUUNTO_DIARY_GRAPH_GRANULARITY,
                getGranularityAnalyticsValue(granularity)
            )
            .put(AnalyticsEventProperty.GRAPH_TYPE, getGraphType(diaryPage))

        trendsAnalytics.trackDiaryGraphValueTapped(properties)
    }

    private fun getGranularityAnalyticsValue(granularity: GraphGranularity): String {
        return when (granularity) {
            GraphGranularity.DAILY -> AnalyticsPropertyValue.DiaryCalendarGranularity.DAILY
            GraphGranularity.WEEKLY -> AnalyticsPropertyValue.DiaryCalendarGranularity.WEEKLY
            GraphGranularity.MONTHLY -> AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY
            GraphGranularity.YEARLY -> AnalyticsPropertyValue.DiaryCalendarGranularity.YEARLY
        }
    }

    private fun getGraphType(diaryPage: DiaryPage): String {
        return when (diaryPage) {
            DiaryPage.TRAINING -> AnalyticsPropertyValue.SuuntoDiaryType.WORKOUTS
            DiaryPage.SCUBA_DIVING -> AnalyticsPropertyValue.SuuntoDiaryType.SCUBA_DIVING
            DiaryPage.FREE_DIVING -> AnalyticsPropertyValue.SuuntoDiaryType.FREE_DIVING
            DiaryPage.DAILY_ACTIVITY -> AnalyticsPropertyValue.SuuntoDiaryType.ACTIVITY
            DiaryPage.SLEEP -> AnalyticsPropertyValue.SuuntoDiaryType.SLEEP
            DiaryPage.PROGRESS -> AnalyticsPropertyValue.SuuntoDiaryType.PROGRESS
            DiaryPage.RECOVERY -> AnalyticsPropertyValue.SuuntoDiaryType.RECOVERY
            DiaryPage.OVERVIEW -> AnalyticsPropertyValue.SuuntoDiaryType.OVERVIEW
            DiaryPage.SUMMARY -> AnalyticsPropertyValue.SuuntoDiaryType.SUMMARY
            DiaryPage.STATISTICS -> AnalyticsPropertyValue.SuuntoDiaryType.STATISTICS
        }
    }

    data class GraphMarkerFormattingResult(
        val value: String,
        val unit: String?,
        val label: String?
    ) {
        val valueWithUnit = "$value ${unit.orEmpty()}"
    }

    abstract fun getFormattedValueWithLabel(
        graphDataType: GraphDataType,
        value: Float?,
        additionalData: AdditionalData?
    ): GraphMarkerFormattingResult?

    abstract fun getYValueFormatter(dataType: GraphDataType?, resources: Resources): ValueFormatter
}
