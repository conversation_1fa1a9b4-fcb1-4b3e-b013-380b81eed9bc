package com.stt.android.diary.summary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.summary.composables.DialogListItem
import com.stt.android.diary.summary.composables.Footer
import com.stt.android.diary.summary.composables.Header
import com.stt.android.diary.summary.composables.SearchBar
import com.stt.android.home.diary.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.core.R as CR

@AndroidEntryPoint
class ActivityPickerDialogFragment : DialogFragment() {
    private val activityPickerDialogViewModel: ActivityPickerDialogViewModel by viewModels()

    companion object {
        const val TAG = "ActivityPickerDialogFragment"
        const val BUNDLE_SELECTED_ACTIVITIES = "BUNDLE_SELECTED_ACTIVITIES"
        const val REQUEST_KEY_SELECTED_ACTIVITIES = "REQUEST_KEY_SELECTED_ACTIVITIES"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )
        setContent {
            AppTheme {
                val allGroups by activityPickerDialogViewModel.allGroups.collectAsState()
                val allActivities by activityPickerDialogViewModel.allDoneActivityTypes.collectAsState()
                val selectedIds by activityPickerDialogViewModel.selectedIds.collectAsState()
                val searchValue = activityPickerDialogViewModel.searchValue
                val isLoading = activityPickerDialogViewModel.isLoading
                val context = LocalContext.current
                val filteredActivities by remember(allActivities, searchValue) {
                    mutableStateOf(
                        allActivities.filter {
                            searchValue.isBlank() || context.getString(it.nameRes)
                                .contains(searchValue, ignoreCase = true)
                        }.sortedBy { context.getString(it.nameRes) }
                    )
                }
                val sortedGroups by remember(allGroups, searchValue) {
                    mutableStateOf(
                        allGroups.filter {
                            searchValue.isBlank() || context.getString(it.nameRes)
                                .contains(searchValue, ignoreCase = true)
                        }.sortedBy { context.getString(it.nameRes) }
                    )
                }
                Body(
                    selectedIds = selectedIds.toImmutableList(),
                    allGroups = sortedGroups.toImmutableList(),
                    isLoading = isLoading,
                    allActivities = filteredActivities.toImmutableList(),
                    onActivityClick = activityPickerDialogViewModel::onActivityClick,
                    onAllSelected = activityPickerDialogViewModel::onAllSelected,
                    onCancelClick = {
                        dismiss()
                    },
                    onOkClick = {
                        setFragmentResult(
                            REQUEST_KEY_SELECTED_ACTIVITIES,
                            bundleOf(
                                BUNDLE_SELECTED_ACTIVITIES to selectedIds.map { it }
                            )
                        )
                        dismiss()
                    },
                    searchValue = searchValue,
                    onSearchValueChange = { activityPickerDialogViewModel.searchValue = it },
                )
            }
        }
    }
}

@Composable
private fun Body(
    selectedIds: ImmutableList<Int>,
    allGroups: ImmutableList<CoreActivityGroup>,
    allActivities: ImmutableList<CoreActivityType>,
    isLoading: Boolean,
    onActivityClick: (ImmutableList<CoreActivityGrouping>, Boolean) -> Unit,
    onAllSelected: () -> Unit,
    onCancelClick: () -> Unit,
    onOkClick: () -> Unit,
    searchValue: String,
    onSearchValueChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Crossfade(targetState = isLoading, label = "crossFadeContent") { isLoadingState ->
        when (isLoadingState) {
            true -> {
                Box(
                    modifier = modifier
                        .fillMaxSize()
                        .background(color = Color.White),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }

            else -> {
                Column(
                    modifier = modifier
                        .fillMaxSize()
                        .background(color = Color.White)
                ) {
                    Header(
                        text = stringResource(
                            id = R.string.training_zone_summary_activity_picker_title,
                            if (selectedIds.size == 0) stringResource(R.string.training_zone_summary_filter_sports_all) else selectedIds.size.toString()
                        )
                    )
                    SearchBar(
                        text = searchValue,
                        onSearchValueChange = onSearchValueChange
                    )
                    ActivityList(
                        modifier = Modifier.weight(1f),
                        onActivityClick = onActivityClick,
                        selectedIds = selectedIds,
                        allGroups = allGroups,
                        allActivities = allActivities,
                        onAllSelected = onAllSelected,
                        searchValue = searchValue,
                    )
                    Footer(onCancelClick = onCancelClick, onOkClick = onOkClick)
                }
            }
        }
    }
}

@Composable
private fun ActivityList(
    selectedIds: ImmutableList<Int>,
    allGroups: ImmutableList<CoreActivityGroup>,
    allActivities: ImmutableList<CoreActivityType>,
    onActivityClick: (ImmutableList<CoreActivityGrouping>, isSelected: Boolean) -> Unit,
    onAllSelected: () -> Unit,
    searchValue: String,
    modifier: Modifier = Modifier
) {
    val showAll by remember(searchValue) {
        derivedStateOf { searchValue.isBlank() }
    }

    val isEmpty by remember(allActivities) {
        derivedStateOf { allActivities.isEmpty() }
    }

    LazyColumn(
        modifier = modifier.fillMaxSize()
    ) {
        if (isEmpty) {
            item {
                Text(
                    text = stringResource(CR.string.no_search_results),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.large)
                )
            }
        } else {
            if (showAll) {
                item {
                    DialogListItem(
                        iconRes = R.drawable.more_horizontal_outline,
                        nameRes = R.string.training_zone_summary_filter_sports_all,
                        isSelected = selectedIds.isEmpty(),
                        modifier = Modifier.clickable { onAllSelected() },
                    )
                }
            }
            items(
                items = allGroups,
                key = { it.id }
            ) { group ->
                val isSelected = group.activityTypes
                    .filter { it in allActivities } // Consider only the activities the user has done
                    .all { it.id in selectedIds } // The group is selected if all the "done activities" in that group are selected

                DialogListItem(
                    iconRes = group.icon,
                    nameRes = group.nameRes,
                    isSelected = isSelected,
                    modifier = Modifier.clickable {
                        onActivityClick(
                            persistentListOf(group),
                            !isSelected
                        )
                    },
                )
            }

            items(
                items = allActivities,
                key = { it.id }
            ) { coreActivityType ->
                val isSelected = coreActivityType.id in selectedIds
                DialogListItem(
                    iconRes = coreActivityType.icon,
                    nameRes = coreActivityType.nameRes,
                    isSelected = isSelected,
                    modifier = Modifier.clickable {
                        onActivityClick(
                            persistentListOf(
                                coreActivityType
                            ),
                            !isSelected
                        )
                    },
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    AppTheme {
        Body(
            selectedIds = persistentListOf(0, 4),
            onActivityClick = { _, _ -> },
            onCancelClick = {},
            onOkClick = {},
            onAllSelected = {},
            searchValue = "",
            onSearchValueChange = {},
            allGroups = CoreActivityGroup.entries.toImmutableList(),
            allActivities = CoreActivityType.entries.toImmutableList(),
            isLoading = false
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun EmptyPreview() {
    AppTheme {
        Body(
            selectedIds = persistentListOf(),
            onActivityClick = { _, _ -> },
            onCancelClick = {},
            onOkClick = {},
            onAllSelected = {},
            searchValue = "gggf",
            onSearchValueChange = {},
            allGroups = persistentListOf(),
            allActivities = persistentListOf(),
            isLoading = false
        )
    }
}
