package com.stt.android.diary.summary

import javax.inject.Inject

class TrainingZoneSummarySortingUseCase @Inject constructor() {
    operator fun invoke(
        groupedWorkouts: List<TableRowItem>,
        sorting: TrainingZoneSummarySortingUiState
    ): List<TableRowItem> {
        return groupedWorkouts
            .sortedWith(
                compareBy {
                    when (sorting.selectedColumn) {
                        TrainingZoneSummaryColumn.DATE -> it.timestamp
                        TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS -> it.numberOfWorkouts
                        TrainingZoneSummaryColumn.DURATION -> it.totalDuration
                        TrainingZoneSummaryColumn.DISTANCE -> it.totalDistance
                        TrainingZoneSummaryColumn.AVG_SPEED -> it.avgSpeed
                        TrainingZoneSummaryColumn.AVG_PACE -> it.avgPace
                        TrainingZoneSummaryColumn.AVG_HEART_RATE -> it.heartRateAverage
                        TrainingZoneSummaryColumn.ASCENT_ALTITUDE -> it.totalAscent
                        TrainingZoneSummaryColumn.TRAINING_STRESS_SCORE -> it.tss
                        TrainingZoneSummaryColumn.ENERGY -> it.energyConsumption
                        TrainingZoneSummaryColumn.EST_VO2PEAK -> it.estVo2Peak
                        TrainingZoneSummaryColumn.AVG_POWER -> it.avgPower
                        TrainingZoneSummaryColumn.NORMALIZED_POWER -> it.normalizedPower
                        TrainingZoneSummaryColumn.AVG_SWIM_PACE -> it.avgSwimPace
                    }
                }
            )
            .run {
                when (sorting.selectedOrder) {
                    TrainingZoneSummarySortingOrder.DESCENDING -> reversed()
                    TrainingZoneSummarySortingOrder.ASCENDING -> this
                }
            }
    }
}
