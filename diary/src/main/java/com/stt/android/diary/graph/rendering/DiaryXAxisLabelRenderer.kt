package com.stt.android.diary.graph.rendering

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.RectF
import androidx.core.graphics.withClip
import com.github.mikephil.charting.charts.CombinedChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.utils.MPPointF
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.data.TimeUtils
import com.stt.android.domain.diary.models.ChartData
import com.stt.android.domain.diary.models.GraphGranularity
import com.stt.android.domain.diary.models.GraphTimeFrame
import com.stt.android.extensions.convert
import java.time.temporal.ChronoUnit
import java.util.Locale
import com.stt.android.R as BaseR

class DiaryXAxisLabelRenderer(
    private val labels: Map<Int, String>,
    private val align: Align,
    viewPortHandler: ViewPortHandler,
    xAxis: XAxis,
    transformer: Transformer
) : XAxisRenderer(viewPortHandler, xAxis, transformer) {

    enum class Align {
        START,
        CENTER,
        END
    }

    override fun drawLabels(canvas: Canvas?, y: Float, anchor: MPPointF?) {
        for ((value, label) in labels) {
            val lines = label.split("\n")
            var yOffset = Utils.convertDpToPixel(2f)
            // Note: to support more than 2-lines, adjust the chart bottom offset larger in
            // DiaryChartBase.setupGraph()
            for (text in lines) {
                Utils.drawXAxisValue(
                    canvas,
                    text,
                    transformer.getPixelForValues(value.toFloat(), y).x.toFloat(),
                    y + yOffset,
                    mAxisLabelPaint,
                    when (align) {
                        Align.START -> MPPointF(0f, 0f)
                        Align.CENTER -> MPPointF(0.5f, 0f)
                        Align.END -> MPPointF(1f, 0f)
                    },
                    0f
                )
                yOffset += mAxisLabelPaint.textSize
            }
        }
    }

    /**
     * Modified from the base class to draw gridlines only to label positions.
     */
    override fun renderGridLines(c: Canvas) {
        if (!mXAxis.isDrawGridLinesEnabled || !mXAxis.isEnabled) return
        val entryCount = mAxis.mEntryCount
        c.withClip(gridClippingRect) {
            if (mRenderGridLinesBuffer.size != entryCount * 2) {
                mRenderGridLinesBuffer = FloatArray(entryCount * 2)
            }
            val positions = mRenderGridLinesBuffer
            run {
                var i = 0
                labels.entries.map { entry ->
                    positions[i] = entry.key.toFloat()
                    positions[i + 1] = entry.key.toFloat()
                    i += 2
                }
            }
            mTrans.pointValuesToPixel(positions)
            setupGridPaint()
            val gridLinePath = mRenderGridLinesPath
            gridLinePath.reset()
            var i = 0
            while (i < positions.size) {
                drawGridLine(c, positions[i], positions[i + 1], gridLinePath)
                i += 2
            }
        }
    }

    /**
     * Modified from the base class to allow drawing below the x-axis line.
     */
    override fun getGridClippingRect(): RectF {
        mGridClippingRect.set(mViewPortHandler.contentRect)
        mGridClippingRect.inset(
            -mAxis.gridLineWidth,
            -Utils.convertDpToPixel(X_AXIS_TICK_HEIGHT_IN_DP)
        )
        return mGridClippingRect
    }

    /**
     * Modified from the base class to draw the gridline down from the x-axis line
     * with length of X_AXIS_TICK_HEIGHT_IN_DP
     */
    override fun drawGridLine(c: Canvas, x: Float, y: Float, gridLinePath: Path) {
        gridLinePath.moveTo(x, mViewPortHandler.contentBottom())
        gridLinePath.lineTo(
            x,
            mViewPortHandler.contentBottom() + Utils.convertDpToPixel(X_AXIS_TICK_HEIGHT_IN_DP)
        )

        // draw a path because lines don't support dashing on lower android versions
        c.drawPath(gridLinePath, mGridPaint)
        gridLinePath.reset()
    }

    companion object {
        private const val X_AXIS_TICK_HEIGHT_IN_DP = 4f
    }
}

/**
 * Build a custom X axis renderer for the diary.
 *
 * Fixed formatting based on granularity
 * Daily - 4 date labels
 * Weekly - 4 month labels
 * Monthly - 13 month labels abbreviated to 1 letter
 * Yearly - 8 year labels
 */
fun CombinedChart.getDiaryXAxisRenderer(
    chartData: ChartData,
    timestamps: List<Long>,
    xAxisIndices: IntRange
): DiaryXAxisLabelRenderer {
    val granularity = chartData.timeFrame.granularity
    return DiaryXAxisLabelRenderer(
        getDiaryXAxisLabels(
            context = context,
            timeFrame = chartData.timeFrame,
            timestamps = timestamps,
            xAxisIndices = xAxisIndices
        ),
        if (granularity == GraphGranularity.DAILY || granularity == GraphGranularity.WEEKLY) {
            DiaryXAxisLabelRenderer.Align.END
        } else {
            DiaryXAxisLabelRenderer.Align.CENTER
        },
        viewPortHandler,
        xAxis,
        getTransformer(YAxis.AxisDependency.LEFT)
    )
}

internal fun getDiaryXAxisLabels(
    context: Context,
    timeFrame: GraphTimeFrame,
    timestamps: List<Long>,
    xAxisIndices: IntRange
): MutableMap<Int, String> {
    val startDate = timeFrame.localStartTime.toLocalDate()
    val endDate = timeFrame.localEndTime.toLocalDate()
    val granularity = timeFrame.granularity
    val labels = mutableMapOf<Int, String>()
    val numDays = ChronoUnit.DAYS.between(startDate, endDate)
    val dateFormatter = TimeUtils.dateWithoutYearFormatter()

    when (granularity) {
        GraphGranularity.DAILY -> {
            // 4 labels evenly on the x-axis
            val step = (timeFrame.totalDays / 8L).coerceAtLeast(1L)
            var date = startDate.plusDays(step)

            while (date < endDate) {
                val index = ChronoUnit.DAYS.between(startDate, date).toInt()
                labels[index] =
                    dateFormatter.format(date) + if (date.monthValue == 1 && date.dayOfMonth < 15) {
                        "\n${date.year}"
                    } else {
                        ""
                    }
                date = date.plusDays(2 * step)
            }
        }
        GraphGranularity.WEEKLY -> {
            // 4 labels evenly on the x-axis
            val step = timeFrame.totalDays / 8L
            var date = startDate.plusDays(step)
            val months = context.resources.getStringArray(BaseR.array.abbreviated_months)
            while (date < endDate) {
                val index = ChronoUnit.DAYS.between(startDate, date).toInt()
                val labelIndex = (0..numDays.toInt()).convert(index, xAxisIndices)
                val monthIndex = date.monthValue - 1
                labels[labelIndex] = months[monthIndex] + if (date.monthValue <= 2) {
                    "\n${date.year}"
                } else {
                    ""
                }
                date = date.plusDays(2 * step)
            }
        }
        GraphGranularity.MONTHLY -> {
            val isLanguageChinese = Locale.getDefault().language == Locale.CHINESE.language
            val months = context.resources.getStringArray(BaseR.array.abbreviated_months_shortest)
            timestamps.forEachIndexed { index, timestamp ->
                val localDateTime = TimeUtils.epochToLocalZonedDateTime(timestamp)
                val monthIndex = localDateTime.monthValue - 1
                // Chinese month labels for each month do not fit on all screens.
                // Show only every second label starting from Jan to show the year label.
                if (!isLanguageChinese || monthIndex % 2 == 0) {
                    labels[index] = // Adds year below january
                        months[monthIndex] + if (monthIndex == 0) "\n${localDateTime.year}" else ""
                }
            }
        }
        GraphGranularity.YEARLY -> {
            timestamps.forEachIndexed { index, timestamp ->
                val localDateTime = TimeUtils.epochToLocalZonedDateTime(timestamp)
                labels[index] = localDateTime.year.toString()
            }
        }
    }

    return labels
}
