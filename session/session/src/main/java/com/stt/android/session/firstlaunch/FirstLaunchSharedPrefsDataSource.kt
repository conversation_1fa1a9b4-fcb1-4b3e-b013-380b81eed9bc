package com.stt.android.session.firstlaunch

import android.content.Context
import android.content.SharedPreferences
import android.os.FileObserver
import androidx.core.content.edit
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.session.firstlaunch.FirstLaunchDataSource
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_FIRST_LAUNCH_TERMS_ACCEPTED
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_FIRST_LAUNCH_TERMS_ACCEPTED_MIGRATED_TO_FILE
import com.stt.android.utils.isMainProcess
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Uses an empty file in app's internal files directory to mark if the terms have been accepted,
 * main benefit over using SharedPreferences being that multiple processes can read and observe
 * the newest data
 */
@Singleton
class FirstLaunchSharedPrefsDataSource @Inject constructor(
    sharedPrefs: SharedPreferences,
    currentUserController: CurrentUserController,
    private val context: Context,
    private val dispatchers: CoroutinesDispatchers
) : FirstLaunchDataSource {
    init {
        if (
            context.isMainProcess &&
            !sharedPrefs.getBoolean(KEY_FIRST_LAUNCH_TERMS_ACCEPTED_MIGRATED_TO_FILE, false)
        ) {
            sharedPrefs.edit {
                putBoolean(KEY_FIRST_LAUNCH_TERMS_ACCEPTED_MIGRATED_TO_FILE, true)
            }

            internalSetFirstLaunchTermsAccepted(
                sharedPrefs.getBoolean(KEY_FIRST_LAUNCH_TERMS_ACCEPTED, false) || currentUserController.isLoggedIn
            )
        }
    }

    override fun isFirstLaunchTermsAcceptedSync(): Boolean =
        getTermsAcceptedFile().exists()

    override suspend fun isFirstLaunchTermsAccepted(): Boolean = withContext(dispatchers.io) {
        isFirstLaunchTermsAcceptedSync()
    }

    override suspend fun setFirstLaunchTermsAccepted(accepted: Boolean) {
        withContext(dispatchers.io) {
            internalSetFirstLaunchTermsAccepted(accepted)
        }
    }

    // We intentionally do the file writing on main thread during init, and later
    // calls are always on the IO dispatcher
    @Suppress("BlockingMethodInNonBlockingContext")
    private fun internalSetFirstLaunchTermsAccepted(accepted: Boolean) {
        try {
            val termsAcceptedFile = getTermsAcceptedFile()
            if (accepted) {
                termsAcceptedFile.createNewFile()
            } else {
                termsAcceptedFile.delete()
            }
        } catch (e: IOException) {
            Timber.e(e, "Could not edit file that marks first launch terms as accepted")
        }
    }

    override fun isFirstLaunchTermsAcceptedFlow(): Flow<Boolean> {
        return callbackFlow {
            send(isFirstLaunchTermsAccepted())

            val termsAcceptedFile = getTermsAcceptedFile()

            // The suggested constructor is available from api level 29 onwards.
            @Suppress("DEPRECATION")
            val fileObserver =
                object : FileObserver(termsAcceptedFile.parentFile!!.absolutePath) {
                    override fun onEvent(event: Int, path: String?) {
                        // The path parameter is relative to the path we're observing,
                        // which in this case is the directory the terms file is stored.
                        // Thus we're expecting the terms file's name as the path parameter
                        if (path == termsAcceptedFile.name) {
                            trySend(isFirstLaunchTermsAcceptedSync())
                        }
                    }
                }
            fileObserver.startWatching()

            awaitClose {
                fileObserver.stopWatching()
            }
        }
    }

    private fun getTermsAcceptedFile(): File =
        File(context.filesDir, TERMS_ACCEPTED_FILE_NAME)

    companion object {
        private const val TERMS_ACCEPTED_FILE_NAME = "first_launch_terms_accepted"
    }
}
