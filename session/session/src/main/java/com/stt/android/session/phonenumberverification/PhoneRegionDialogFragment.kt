package com.stt.android.session.phonenumberverification

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.navigation.fragment.navArgs
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.stt.android.session.R
import com.stt.android.utils.LocaleUtils
import com.stt.android.R as BaseR

class PhoneRegionDialogFragment : DialogFragment() {

    val args: PhoneRegionDialogFragmentArgs by navArgs()

    var onItemClickListener: ((Int) -> Unit) = {}

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AlertDialog.Builder(requireActivity()).apply {
            setTitle(R.string.phone_region)
            setPositiveButton(BaseR.string.cancel) { _, _ -> dismiss() }
            val list = PhoneNumberUtil.getInstance().toPhoneCountryList(context)
            setSingleChoiceItems(
                list.map { "${it.second}  (+${it.first})" }.toTypedArray(),
                getCheckedItemPosition(args.code, list)
            ) { _, position ->
                onItemClickListener(list[position].first)
                dismiss()
            }
        }.create()
    }

    private fun getCheckedItemPosition(
        code: Int,
        list: List<Pair<Int, String>>
    ): Int {
        list.forEachIndexed { index, phoneCountry ->
            if (phoneCountry.first == code) {
                return index
            }
        }
        return -1
    }

    companion object {
        const val HK = 852
        const val Macao = 853
        const val TW = 886
    }
}

fun PhoneNumberUtil.toPhoneCountryList(context: Context): List<Pair<Int, String>> =
    supportedCallingCodes.asSequence().map { code ->
        getRegionCodesForCountryCode(code)
            .mapNotNull { LocaleUtils.fromCountryCode(it) }
            .filter { it.displayCountry.isNotBlank() }
            .map { code to it.displayCountry }
    }.flatten().map {
        when (it.first) {
            PhoneRegionDialogFragment.HK -> it.copy(second = context.getString(BaseR.string.hong_kong_display_china))
            PhoneRegionDialogFragment.Macao -> it.copy(second = context.getString(BaseR.string.macao_display_china))
            PhoneRegionDialogFragment.TW -> it.copy(second = context.getString(BaseR.string.taiwan_display_china))
            else -> it
        }
    }.sortedBy { it.second }.toList()
