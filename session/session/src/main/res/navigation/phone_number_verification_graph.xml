<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/phone_number_verification_graph"
    app:startDestination="@id/phoneNumberConfirmation">

    <fragment
        android:id="@+id/phoneNumberConfirmation"
        android:name="com.stt.android.session.phonenumberverification.PhoneNumberConfirmationFragment"
        android:label="fragment_phone_number_verification"
        tools:layout="@layout/fragment_phone_number_confirmation">
        <action
            android:id="@+id/action_phoneNumberConfirmation_to_phoneNumberCodeVerification"
            app:destination="@id/phoneNumberCodeVerification"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_phoneNumberConfirmation_to_phoneRegionDialogFragment"
            app:destination="@id/phoneRegionDialogFragment">
            <argument
                android:name="code"
                app:argType="integer" />
        </action>
    </fragment>
    <fragment
        android:id="@+id/phoneNumberCodeVerification"
        android:name="com.stt.android.session.phonenumberverification.PhoneNumberCodeVerificationFragment"
        android:label="fragment_phone_number_code_verification"
        tools:layout="@layout/fragment_phone_number_code_verification" >
        <argument
            android:name="phone_number"
            app:argType="string" />
    </fragment>

    <include app:graph="@navigation/phone_number_region_dialog_graph" />
</navigation>
