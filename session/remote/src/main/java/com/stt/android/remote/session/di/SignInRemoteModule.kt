package com.stt.android.remote.session.di

import android.content.Context
import com.squareup.moshi.Moshi
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getNoAuthOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.remote.session.AuthRestApi
import com.stt.android.remote.session.SignatureParams
import com.stt.android.session.remote.R
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(SingletonComponent::class)
object SignInRemoteModule {

    @Provides
    fun provideSignatureParams(context: Context): SignatureParams = SignatureParams(
        context.resources.getString(R.string.signing_key_p1),
        "fN2AqIBc/IRAoNgshbxgnOGUVGlU3LC0xL0AuXXXXMXY",
        "RWQ4zIi0PWz4hekc1QGNTPlciNhEKV1teYSIkDGYY",
        context.packageName
    )

    @Provides
    fun provideAuthRestApi(
        @SharedOkHttpClient sharedClient: OkHttpClient,
        @BaseUrl baseUrl: String,
        @UserAgent userAgent: String,
        moshi: Moshi
    ): AuthRestApi {
        return buildRestApi(
            sharedClient,
            baseUrl,
            AuthRestApi::class.java,
            getNoAuthOkHttpConfig(userAgent),
            moshi
        )
    }
}
