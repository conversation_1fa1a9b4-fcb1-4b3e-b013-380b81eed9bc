package com.stt.android.data.session.status

import com.stt.android.data.session.toDomain
import com.stt.android.domain.session.EmailStatus
import com.stt.android.domain.session.PhoneNumberStatus
import com.stt.android.domain.session.UsernameStatus
import com.stt.android.domain.session.status.AccountStatusDataSource
import com.stt.android.remote.session.AuthRemoteApi
import javax.inject.Inject

class AccountStatusRepository @Inject constructor(
    private val authRemoteApi: AuthRemoteApi
) : AccountStatusDataSource {
    override suspend fun fetchEmailStatus(email: String): EmailStatus =
        authRemoteApi.fetchEmailStatus(email).toDomain()

    override suspend fun fetchUsernameStatus(username: String): UsernameStatus =
        authRemoteApi.fetchUsernameStatus(username).toDomain()

    override suspend fun fetchPhoneNumberStatus(phoneNumber: String): PhoneNumberStatus =
        authRemoteApi.fetchPhoneNumberStatus(phoneNumber).toDomain()
}
