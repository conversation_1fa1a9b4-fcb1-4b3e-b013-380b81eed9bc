package com.stt.android.watch.debug.ui.setlocation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.intentresolver.IntentKey
import com.stt.android.intentresolver.IntentResolver
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoCameraOptions
import com.stt.android.maps.SuuntoCameraUpdateNewPosition
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.watch.debug.domain.SetDebugLocationCoordinatesUseCase
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours

@AndroidEntryPoint
class SetDebugLocationActivity : AppCompatActivity() {

    private var _map: SuuntoMap? = null
    private var latitude: Double? by mutableStateOf(null)
    private var longitude: Double? by mutableStateOf(null)
    private var isWatchConnected by mutableStateOf(true)

    @Inject
    lateinit var debugLocationCoordinatesUseCase: SetDebugLocationCoordinatesUseCase

    @Inject
    lateinit var isWatchConnectedUseCase: IsWatchConnectedUseCase

    @Inject
    lateinit var lastKnownLocationUseCase: LastKnownLocationUseCase

    private val onMapClickListener = object : SuuntoMap.OnMapClickListener {
        override fun onMapClick(latLng: LatLng, placeName: String?) {
            latitude = latLng.latitude
            longitude = latLng.longitude
            _map?.clear()
            _map?.addMarker(
                SuuntoMarkerOptions()
                    .position(latLng)
                    .icon(
                        SuuntoBitmapDescriptorFactory(this@SetDebugLocationActivity).fromResource(
                            R.drawable.map_pin
                        )
                    )
                    .draggable(true)
                    .anchor(0.5f, 1f)
                    .zPriority(MarkerZPriority.TAPPED_LOCATION)
            )
            lifecycleScope.launch {
                debugLocationCoordinatesUseCase.set(
                    latitude = latLng.latitude,
                    longitude = latLng.longitude
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeWatchConnectionState()

        setContent {
            AppTheme {
                val snackbarHostState = remember { SnackbarHostState() }

                Scaffold(
                    scaffoldState = rememberScaffoldState(),
                    snackbarHost = {
                        SnackbarHost(hostState = snackbarHostState)
                    },
                ) { internalPadding ->
                    BackHandler {
                        finish()
                    }
                    Box(
                        modifier = Modifier
                            .padding(internalPadding)
                            .fillMaxSize()
                    ) {
                        SuuntoMap(
                            modifier = Modifier
                                .fillMaxSize(),
                            mapOptions = SuuntoMapOptions(uiAttribution = false)
                        ) { map ->
                            _map = map
                            // Move Mapbox logo to top right corner
                            map.getUiSettings().setLogoPosition(Gravity.TOP or Gravity.END)
                            showLastKnownLocation()
                            setListener()
                        }

                        setListener()

                        if (latitude != null && longitude != null) {
                            Column(
                                modifier = Modifier
                                    .background(Color.White)
                                    .padding(MaterialTheme.spacing.medium)
                                    .align(Alignment.TopEnd)
                            ) {
                                Text(
                                    text = String.format(Locale.US, "Latitude: %.7f", latitude),
                                    style = MaterialTheme.typography.bodyLargeBold
                                )
                                Text(
                                    text = String.format(Locale.US, "Longitude: %.7f", longitude),
                                    style = MaterialTheme.typography.bodyLargeBold
                                )
                            }
                        }
                        if (latitude != null && longitude != null) {
                            FloatingActionButton(
                                modifier = Modifier
                                    .align(Alignment.BottomEnd)
                                    .padding(MaterialTheme.spacing.medium),
                                onClick = {
                                    if (!isWatchConnected) return@FloatingActionButton
                                    latitude = null
                                    longitude = null
                                    _map?.clear()
                                    lifecycleScope.launch {
                                        debugLocationCoordinatesUseCase.clear()
                                    }
                                },
                            ) {
                                Icon(Icons.Filled.Clear, "Clear location")
                            }
                        }

                        if (!isWatchConnected) {
                            val message = stringResource(id = R.string.wifi_watch_is_not_connected)
                            LaunchedEffect(snackbarHostState) {
                                snackbarHostState.showSnackbar(
                                    message = message,
                                    duration = SnackbarDuration.Indefinite
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun setListener() {
        if (isWatchConnected) {
            _map?.addOnMapClickListener(onMapClickListener)
        } else {
            _map?.removeOnMapClickListener(onMapClickListener)
        }
    }

    private fun observeWatchConnectionState() {
        lifecycleScope.launch {
            isWatchConnectedUseCase.invoke()
                .catch { Timber.w(it, "Unable to get watch states.") }
                .collectLatest { isConnected ->
                    isWatchConnected = isConnected
                }
        }
    }

    private fun showLastKnownLocation() {
        lifecycleScope.launch {
            val latLng = lastKnownLocationUseCase.getLastKnownLocation(
                skipPassiveProvider = false,
                timeInMilliSecondsSinceEpoch = System.currentTimeMillis() - 1.hours.inWholeMilliseconds
            )
            if (latLng != null) {
                _map?.animateCamera(
                    SuuntoCameraUpdateNewPosition(
                        SuuntoCameraOptions.fromLatLngZoom(
                            latLng = latLng,
                            zoom = 13f
                        )
                    )
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        _map?.removeOnMapClickListener(onMapClickListener)
    }

    companion object {
        fun getDebugWatchLocationIntentResolver(): IntentResolver<IntentKey.DebugWatchLocation> {
            return object : IntentResolver<IntentKey.DebugWatchLocation> {
                override fun getIntent(
                    context: Context,
                    keyT: IntentKey.DebugWatchLocation
                ): Intent =
                    Intent(context, SetDebugLocationActivity::class.java)
            }
        }
    }
}
