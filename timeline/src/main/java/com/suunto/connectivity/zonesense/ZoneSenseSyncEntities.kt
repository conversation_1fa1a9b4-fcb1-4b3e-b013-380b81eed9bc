package com.suunto.connectivity.zonesense

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

// Spec: https://bitbucket.org/suunto/nextgen/src/develop/nea/wbstorage/config/resources/ddfa_baseline.yaml
data class ZoneSenseSyncData(
    val baselineValues: ZoneSenseBaselineValuesSyncData,
)

@JsonClass(generateAdapter = true)
data class ZoneSenseBaselineValuesSyncData(
    @<PERSON><PERSON>(name = "running") val running: Float,
    @<PERSON><PERSON>(name = "cycling") val cycling: Float,
    @<PERSON><PERSON>(name = "swimming") val swimming: Float,
    @<PERSON><PERSON>(name = "skiing") val skiing: Float,
    @<PERSON><PERSON>(name = "rowing") val rowing: Float,
)
