package com.suunto.connectivity.deviceid

import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

internal class SuuntoRaceSCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.SuuntoRaceS
    override val isWhiteboard = true
    override val isWatch = true

    override fun hasGpsSensor(): <PERSON>olean {
        return true
    }

    override fun supportsBarometricAltitude(): Boolean {
        return true
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): <PERSON><PERSON>an {
        return true
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): <PERSON><PERSON>an {
        return true
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsSystemEvents(): Boolean {
        return true
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return true
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        return true
    }

    override fun toString(): String {
        return SuuntoDeviceType.SuuntoRaceS.toString()
    }
}
