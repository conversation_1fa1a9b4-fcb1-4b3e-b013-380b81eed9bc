package com.suunto.connectivity.deviceid

import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

/**
 * Device capabilities for TraverseAlpha
 *
 *
 * Created by <PERSON><PERSON> on 9/1/2016.
 */
internal class SuuntoTraverseAlphaCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.TraverseAlpha
    override val isWhiteboard = false
    override val isWatch = true

    override fun hasGpsSensor(): Boolean {
        return true
    }

    override fun supportsBarometricAltitude(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSystemEvents(): Boolean {
        return false
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return false
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        return false
    }

    override fun toString(): String {
        return SuuntoDeviceType.TraverseAlpha.toString()
    }
}
