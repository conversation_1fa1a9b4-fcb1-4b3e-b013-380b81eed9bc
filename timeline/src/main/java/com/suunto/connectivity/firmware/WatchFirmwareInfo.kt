package com.suunto.connectivity.firmware

import org.json.JSONObject

/**
 * Contains info about the latest available firmware
 */
data class WatchFirmwareInfo(
    val deviceName: String,
    val firmwareUploadDate: String,
    val latestFirmwareURI: String,
    val latestFirmwareVersion: String,
    val version: String,
    val versionLog: String,
    val releaseType: String?,
    val forceUpdate: Boolean = false
) {

    fun forceUpdateToContract(): String {
        return JSONObject().apply { put("Tag", forceUpdate) }.toString()
    }
}
