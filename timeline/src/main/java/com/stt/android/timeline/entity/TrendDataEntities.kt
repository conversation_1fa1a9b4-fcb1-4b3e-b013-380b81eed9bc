package com.stt.android.timeline.entity

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import java.time.ZonedDateTime

@JsonClass(generateAdapter = true)
data class TrendDataSampleSmlHeaderData(
    @Json(name = "Duration") val duration: Float,
    @Json(name = "DateTime") val dateTime: ZonedDateTime
)

@JsonClass(generateAdapter = true)
data class TrendDataSampleSmlSampleData(
    @Json(name = "EnergyConsumption") val energy: Float,
    @Json(name = "StepCount") val steps: Int,
    @Json(name = "HR") val hr: Float?,
    @Json(name = "HRExt") val hrMinMax: MinMaxFloat?,
    @Json(name = "SpO2") val spo2: Float?,
    @Json(name = "Altitude") val altitude: Float?
)

@JsonClass(generateAdapter = true)
data class MinMaxFloat(
    @Json(name = "Min") val min: Float?,
    @Json(name = "Max") val max: Float?
)

@JsonClass(generateAdapter = true)
data class TrendDataSampleSmlHeader(
    @Json(name = "Header") val header: TrendDataSampleSmlHeaderData
)

@JsonClass(generateAdapter = true)
data class TrendDataSampleSmlSample(
    @Json(name = "Sample") val sample: TrendDataSampleSmlSampleData
)

data class TrendDataTimelineWithAttributesSample(
    val trendDataAttributesSample: TrendDataSampleAttributesSample,
    val source: String,
    val timeISO8601: ZonedDateTime
)

@JsonClass(generateAdapter = true)
data class TrendDataSampleAttributesHeader(
    @Json(name = "suunto/sml/247") val smlHeader: TrendDataSampleSmlHeader
)

@JsonClass(generateAdapter = true)
data class TrendDataSampleAttributesSample(
    @Json(name = "suunto/sml/247") val smlSample: TrendDataSampleSmlSample
)
