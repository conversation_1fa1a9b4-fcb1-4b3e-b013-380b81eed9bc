package com.stt.android.sportmode.datascreen.fields

import android.content.res.Resources
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.R
import com.stt.android.sportmode.datascreen.DataOptionFields
import com.stt.android.sportmode.datascreen.DataScreen
import com.stt.android.sportmode.datascreen.DataScreenComponent
import com.stt.android.sportmode.modesetting.sportDrawable

object DataOptionsFieldsResource {

    val fieldsList = DataOptionFields.entries.toList()

    fun iconRes(fields: DataOptionFields) = when (fields) {
        DataOptionFields.FIELD_1 -> sportDrawable.data_screen_fields_1
        DataOptionFields.FIELD_2 -> sportDrawable.data_screen_fields_2
        DataOptionFields.FIELD_3 -> sportDrawable.data_screen_fields_3
        DataOptionFields.FIELD_4 -> sportDrawable.data_screen_fields_4
        DataOptionFields.FIELD_MANUAL_LAP -> sportDrawable.data_screen_fields_manual_lap
    }

    fun title(resources: Resources, fields: DataOptionFields) = when (fields) {
        DataOptionFields.FIELD_1 -> resources.getQuantityString(R.plurals.sport_mode_display_fields_count, 1, 1)
        DataOptionFields.FIELD_2 -> resources.getQuantityString(R.plurals.sport_mode_display_fields_count, 2, 2)
        DataOptionFields.FIELD_3 -> resources.getQuantityString(R.plurals.sport_mode_display_fields_count, 3, 3)
        DataOptionFields.FIELD_4 -> resources.getQuantityString(R.plurals.sport_mode_display_fields_count, 4, 4)
        DataOptionFields.FIELD_MANUAL_LAP -> resources.getString(R.string.sport_mode_manual_lap)
    }

    fun DataScreen.getFields(): DataOptionFields? = if (isManualLap) {
        DataOptionFields.FIELD_MANUAL_LAP
    } else {
        when (items.size) {
            1 -> DataOptionFields.FIELD_1
            2 -> DataOptionFields.FIELD_2
            3 -> DataOptionFields.FIELD_3
            4 -> DataOptionFields.FIELD_4
            else -> null
        }
    }

    fun DataOptionFields.getFieldCount(): Int = when (this) {
        DataOptionFields.FIELD_1 -> 1
        DataOptionFields.FIELD_2 -> 2
        DataOptionFields.FIELD_3 -> 3
        DataOptionFields.FIELD_4 -> 4
        DataOptionFields.FIELD_MANUAL_LAP -> 2
    }

    val defaultManualLapFields = listOf(
        DataScreenComponent.fromMapping(DataOptionMapping.MANUAL_LAP_DURATION),
        DataScreenComponent.fromMapping(DataOptionMapping.MANUAL_LAP_AVG_HEART_RATE),
    )

    val DataOptionMapping.isManualLap
        get() = valueId.contains("_1")
}
