package com.stt.android.sportmode.datasource.mapper

import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.DataScreen
import com.stt.android.sportmode.datascreen.DataScreenComponent
import com.stt.android.sportmode.datascreen.DataScreenList
import com.stt.android.sportmode.datascreen.MAX_DATA_SCREEN_SIZE
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_FIXED
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_MANUAL_LAP
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_NORMAL
import com.stt.android.sportmode.modesetting.Mapper
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.DATA_OPTION_SEPARATOR
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.GROUP_SEPARATOR
import javax.inject.Inject

class DataScreenInMapper @Inject constructor(
    private val measurementUnit: MeasurementUnit,
) : Mapper<GetDataScreenResponse, DataScreenList> {
    override suspend fun invoke(response: GetDataScreenResponse): DataScreenList {
        val split = response.dsData.split(GROUP_SEPARATOR)
        val dsTypeList = response.dsTypeList ?: split.mapIndexed { index, _ ->
            if (index == 0) {
                DATA_SCREEN_FIELD_FIXED
            } else {
                DATA_SCREEN_FIELD_NORMAL
            }
        }
        return DataScreenList(
            dataScreens = split.mapIndexed { index, idStr ->
                val ids = idStr.split(DATA_OPTION_SEPARATOR)
                val dsType = dsTypeList[index]
                DataScreen(
                    fixed = dsType == DATA_SCREEN_FIELD_FIXED,
                    isManualLap = dsType == DATA_SCREEN_FIELD_MANUAL_LAP,
                    editing = false,
                    items = ids.mapNotNull { id ->
                        DataOptionMapping.entries.find { it.valueId == id }?.let {
                            DataScreenComponent.fromMapping(
                                it,
                                isImperial = measurementUnit == MeasurementUnit.IMPERIAL
                            )
                        }
                    }
                )
            } + if (split.size < MAX_DATA_SCREEN_SIZE) {
                listOf(DataScreen(editing = false, items = emptyList()))
            } else {
                emptyList()
            }
        )
    }
}
