package com.stt.android.sportmode.datascreen.options

import androidx.lifecycle.ViewModel
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsResource.isManualLap
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class DataOptionsViewModel @Inject constructor() : ViewModel() {
    lateinit var initialDataOptions: EditDataOptions

    private val _dataOptionsFlow by lazy {
        MutableStateFlow(initialDataOptions)
    }
    val dataOptionsFlow by lazy {
        _dataOptionsFlow.asStateFlow()
    }

    private val dataOptionMappingMap = mutableMapOf<String, List<DataOptionMapping>>()
    private val dataOptionMappingGroupNames: List<String>
        get() = dataOptionMappingMap.keys.toList()

    fun initial(dataOptions: EditDataOptions) {
        dataOptionMappingMap.clear()
        dataOptionMappingMap.putAll(
            DataOptionMapping.entries
                .filter { dataOptions.activityId in it.activities }
                .filter { !dataOptions.isManualLap || it.isManualLap }
                .groupBy { it.groupName }
        )
        val selectedMapping = dataOptions.selectedMapping()
        val selectedGroupIndex = dataOptionMappingGroupNames.indexOf(selectedMapping.groupName)
        initialDataOptions = dataOptions.copy(
            focusedHeaderIndex = selectedGroupIndex
        )
    }

    val dataOptionsDisplayDataFlow: Flow<DataOptionsDisplayData> by lazy {
        dataOptionsFlow.map { dataOption ->
            Timber.d("$dataOption")
            val selectedMapping = dataOption.selectedMapping()
            DataOptionsDisplayData(
                headerList = dataOptionMappingMap.keys.mapIndexed { index, key ->
                    DataOptionHeaderDisplayItem(
                        focused = dataOption.focusedHeaderIndex == index,
                        checked = selectedMapping.groupName == key,
                        textRes = DataOptionTitleMapping.getTextResByName(key),
                    )
                },
                contentList = dataOptionContentDisplayItems(dataOption, selectedMapping)
            )
        }
    }

    private fun dataOptionContentDisplayItems(
        dataOption: EditDataOptions,
        selectedMapping: DataOptionMapping
    ) = run {
        val mappedList = dataOptionMappingMap.getOrElse(dataOptionMappingMap.keys.toList()[dataOption.focusedHeaderIndex]) { emptyList() }
            .map {
                DataOptionContentDisplayItem(
                    id = it.valueId,
                    checked = it == selectedMapping,
                    textRes = DataOptionTitleMapping.getTextResByName(it.valuePhrase),
                    childGroup = it.childGroupId,
                )
            }
            .groupBy { it.childGroup }

        val finalList = mappedList.entries.flatMapIndexed { index, entry ->
            if (index == mappedList.entries.size - 1) { // No divider for last group
                entry.value
            } else { // Add divider
                entry.value + DataOptionContentDisplayItem(isDivider = true)
            }
        }

        finalList.toList()
    }

    fun focusHeaderIndex(index: Int) {
        _dataOptionsFlow.value = _dataOptionsFlow.value.copy(
            focusedHeaderIndex = index
        )
    }

    fun checkContentIndex(id: String) {
        _dataOptionsFlow.value = _dataOptionsFlow.value.copy(
            dataOptionId = id,
        )
    }

    private val DataOptionMapping.childGroupId: Int
        get() = if (!valueId.contains("_")) 0 else valueId.split("_")[1].toInt()
}
