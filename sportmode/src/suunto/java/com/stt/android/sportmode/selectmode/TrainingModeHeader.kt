package com.stt.android.sportmode.selectmode

import android.os.Parcelable
import androidx.annotation.DrawableRes
import com.stt.android.sportmode.modesetting.Reducer
import kotlinx.parcelize.Parcelize

@Parcelize
data class TrainingModeHeader(
    val id: Int = -1,
    val baseModeId: Int = -1,
    @DrawableRes val iconResId: Int = 0,
    val title: String = "",
    val subTitle: String = "",
    val isDefault: Boolean = false,
    val state: TrainingModeHeaderState = TrainingModeHeaderState.IDLE,
) : Parcelable

enum class TrainingModeHeaderState {
    IDLE,
    EDITING,
    HIDDEN,
}

interface ModeHeaderListReducer : Reducer<List<TrainingModeHeader>>
