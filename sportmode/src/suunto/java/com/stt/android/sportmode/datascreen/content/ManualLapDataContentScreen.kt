@file:Suppress("ktlint:compose:modifier-reused-check")

package com.stt.android.sportmode.datascreen.content

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.sportmode.datascreen.DataScreen
import com.stt.android.sportmode.datascreen.DataScreenComponent
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.NUM_HEIGHT_DP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.NUM_WIDTH_DP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.SCREEN_SIZE
import com.stt.android.sportmode.datascreen.testManualLapDataScreens
import com.stt.android.sportmode.modesetting.sportDrawable

private val LAP_SCREEN_VALUE_TEXT_SIZE = 13.92.dp
private val LAP_SCREEN_UNIT_TEXT_SIZE = 8.77.dp
private val TEXT_HORIZONTAL_PADDING = 5.dp
private val SELECTED_BG_COLOR = Color(0xFF3FF07F)
private val UNSELECTED_BG_COLOR = Color(0xFF252525)
private val PROGRESS_COLOR = Color(0xFF353535)
private val PROGRESS_HEIGHT = 2.5.dp

@Composable
fun ManualLapDataContentScreen(
    dataScreen: DataScreen,
    modifier: Modifier = Modifier,
    editing: Boolean = true,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier.fillMaxSize(),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
            modifier = Modifier
                .wrapContentSize()
                .padding(bottom = 10.dp),
        ) {
            Row(modifier = Modifier.wrapContentSize()) {
                SerialNumberView()

                dataScreen.items.forEachIndexed { index, item ->
                    ComponentItemView(
                        item = item,
                        selected = index == 0,
                    )
                }
            }
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(PROGRESS_HEIGHT))
                    .width(87.dp)
                    .height(PROGRESS_HEIGHT)
                    .background(PROGRESS_COLOR)
            ) {
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(PROGRESS_HEIGHT))
                        .width(61.5.dp)
                        .height(PROGRESS_HEIGHT)
                        .background(SELECTED_BG_COLOR)
                )
            }
        }

        if (editing) {
            Icon(
                painter = painterResource(id = sportDrawable.data_screen_num_1),
                tint = Color.Unspecified,
                contentDescription = null,
                modifier = Modifier
                    .size(NUM_WIDTH_DP.dp, NUM_HEIGHT_DP.dp)
                    .graphicsLayer {
                        translationX = -33f.dp.toPx()
                        translationY = 16f.dp.toPx()
                    },
            )
            Icon(
                painter = painterResource(id = sportDrawable.data_screen_num_2),
                tint = Color.Unspecified,
                contentDescription = null,
                modifier = Modifier
                    .size(NUM_WIDTH_DP.dp, NUM_HEIGHT_DP.dp)
                    .graphicsLayer {
                        translationX = 50f.dp.toPx()
                        translationY = 14f.dp.toPx()
                    },
            )
        }
    }
}

@Composable
private fun SerialNumberView(
    modifier: Modifier = Modifier,
    numbers: List<Int> = listOf(11, 12, 13, 14),
) {
    val localDensity = LocalDensity.current
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
        modifier = modifier,
    ) {
        numbers.forEach { num ->
            Text(
                text = num.toString(),
                style = MaterialTheme.typography.bodyBold.copy(
                    fontSize = dpToSp(localDensity, LAP_SCREEN_VALUE_TEXT_SIZE),
                    color = MaterialTheme.colors.surface,
                    fontFamily = valueFontFamily(),
                ),
                modifier = Modifier
                    .padding(
                        horizontal = TEXT_HORIZONTAL_PADDING,
                        vertical = MaterialTheme.spacing.xxxsmall
                    )
                    .padding(top = MaterialTheme.spacing.xxsmall),
            )
        }
    }
}

@Composable
private fun ComponentItemView(
    item: DataScreenComponent,
    modifier: Modifier = Modifier,
    selected: Boolean = false,
) {
    val localDensity = LocalDensity.current
    val textColor = if (selected) MaterialTheme.colors.onSurface else MaterialTheme.colors.surface
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
        modifier = modifier,
    ) {
        (0..3).forEach { index ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
                modifier = Modifier
                    .background(
                        if (selected) SELECTED_BG_COLOR else UNSELECTED_BG_COLOR
                    )
                    .padding(
                        horizontal = TEXT_HORIZONTAL_PADDING,
                        vertical = MaterialTheme.spacing.xxxsmall
                    ),
            ) {
                Text(
                    text = item.value,
                    style = MaterialTheme.typography.bodyBold.copy(
                        fontSize = dpToSp(localDensity, LAP_SCREEN_VALUE_TEXT_SIZE),
                        color = textColor,
                        fontFamily = valueFontFamily(),
                    ),
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xxsmall),
                )

                if (item.unit.isNotBlank()) {
                    Text(
                        text = item.unit,
                        style = MaterialTheme.typography.body.copy(
                            fontSize = dpToSp(localDensity, LAP_SCREEN_UNIT_TEXT_SIZE),
                            color = textColor,
                            fontFamily = unitFontFamily()
                        ),
                    )
                }
            }
        }
    }
}

@Preview(
    widthDp = SCREEN_SIZE,
    heightDp = SCREEN_SIZE,
    showBackground = true,
    backgroundColor = 0xFF000000,
)
@Composable
private fun ManualLapDataScreenPreview2() {
    val list = DataScreen(
        items = testManualLapDataScreens[0].items,
        editing = true,
    )
    ManualLapDataContentScreen(list)
}
