@file:Suppress("ktlint:compose:modifier-reused-check")

package com.stt.android.sportmode.datascreen.content

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathOperation
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.roundToIntRect
import androidx.compose.ui.unit.toSize
import com.stt.android.sportmode.R
import com.stt.android.sportmode.datascreen.DataScreen
import com.stt.android.sportmode.datascreen.DataScreenComponent
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Bottom
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.BottomIn3
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.BottomLeft
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.BottomRight
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.DIVIDER_COLOR
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.ICON_SIDE_TEXT_SIZE
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.ICON_SIZE_DP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.LABEL_COLOR
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.LARGE_UNIT_TEXT_SIZE
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.NUM_HEIGHT_DP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.NUM_WIDTH_DP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.PADDING
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.SCREEN_SIZE
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.TWO_ROWS_BOTTOM
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.TWO_ROWS_TOP
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.UNIT_TEXT_SIZE
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.VALUE_TEXT_SIZE_SECONDARY
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Companion.VALUE_TEXT_WIDTH_RATIO
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Direction
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Single
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.Top
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.TopLeft
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.TopLeftIn3
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.TopRight
import com.stt.android.sportmode.datascreen.content.DataScreenContentContract.TopRightIn3
import com.stt.android.sportmode.datascreen.testDataScreens
import com.stt.android.sportmode.datascreen.testManualLapDataScreens
import com.stt.android.sportmode.modesetting.baseDrawable
import com.stt.android.sportmode.modesetting.sportDrawable
import kotlin.math.roundToInt

@Composable
fun DataScreenScreen(
    list: DataScreen,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        if (list.isManualLap) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize().clip(CircleShape)
            ) {
                ManualLapDataContentScreen(
                    dataScreen = list,
                    editing = list.editing,
                )
            }

            DrawBackground(Modifier.fillMaxSize(), isManualLap = true)
        } else {
            DrawBackground(Modifier.fillMaxSize())

            val size = list.items.size
            val directions = when (size) {
                1 -> listOf(Single)
                2 -> listOf(Top, Bottom)
                3 -> listOf(TopLeftIn3, TopRightIn3, BottomIn3)
                4 -> listOf(TopLeft, TopRight, BottomLeft, BottomRight)
                else -> return
            }
            list.items.forEachIndexed { index, watchDataItem ->
                DataScreenItemView(
                    item = watchDataItem,
                    direction = directions[index],
                    editing = list.editing,
                    modifier = Modifier.fillMaxSize(),
                )
            }
            if (directions.size == 4) {
                Canvas(modifier = Modifier.fillMaxSize()) {
                    drawLine(
                        color = DIVIDER_COLOR,
                        start = center.copy(y = this.size.height * TWO_ROWS_TOP),
                        end = center.copy(y = this.size.height * TWO_ROWS_BOTTOM),
                        strokeWidth = 0.61f * density,
                    )
                }
            }
        }
    }
}

@Composable
private fun DrawBackground(
    modifier: Modifier = Modifier,
    isManualLap: Boolean = false,
) {
    val textMeasurer = rememberTextMeasurer()
    val clockText = "11:36 AM"
    val clockTextStyle = TextStyle(
        color = Color.White,
        fontSize = dpToSp(LocalDensity.current, 10f.dp),
        fontFamily = labelFontFamily(),
        fontWeight = FontWeight.W700,
    )
    val clockTextLayoutResult =
        textMeasurer.measure(AnnotatedString(clockText), style = clockTextStyle)
    val durationText = if (isManualLap) "0:00'00" else "0:32'06"
    val durationTextStyle = TextStyle(
        color = Color.White,
        fontSize = dpToSp(LocalDensity.current, VALUE_TEXT_SIZE_SECONDARY),
        fontFamily = valueFontFamily(),
        fontWeight = FontWeight.W600,
    )
    val durationTextLayoutResult =
        textMeasurer.measure(AnnotatedString(durationText), style = durationTextStyle)
    val img = ImageBitmap.imageResource(sportDrawable.data_screen_watch_circle)
    val img1 = ImageBitmap.imageResource(sportDrawable.data_screen_watch_duration_bg)
    Canvas(modifier = modifier) {
        val rect = Rect(
            0f,
            0f,
            this.size.width,
            this.size.height,
        )
        val padding = density.roundToInt()
        val rect1 = rect.copy(
            left = rect.left - padding,
            top = rect.top - padding,
            right = rect.right + padding,
            bottom = rect.bottom + padding,
        )
        clipPath(
            Path().apply {
                addOval(rect)
            }
        ) {
            drawImage(
                img1,
                dstOffset = IntOffset(
                    0,
                    rect.height.roundToInt() - (rect.width * img1.height / img1.width).toInt()
                ),
                srcSize = IntSize(img1.width, img1.height),
                dstSize = IntSize(
                    rect.width.roundToInt(),
                    (rect.width * img1.height / img1.width).toInt()
                ),
            )
        }
        drawSuuntoUiNextText(
            clockTextLayoutResult,
            topLeft = rect.topCenter.minus(
                Offset(
                    clockTextLayoutResult.size.width / 2f,
                    -clockTextLayoutResult.size.height.toFloat() - 2 * padding,
                )
            )
        )

        drawSuuntoUiNextText(
            durationTextLayoutResult,
            topLeft = rect.bottomCenter.minus(
                Offset(
                    durationTextLayoutResult.size.width / 2f,
                    durationTextLayoutResult.size.height.toFloat() + 5 * padding,
                )
            )
        )

        drawImage(
            img,
            dstOffset = IntOffset(-padding, -padding),
            srcSize = IntSize(img.width, img.height),
            dstSize = IntSize(
                rect1.width.roundToInt(),
                (rect1.width * rect1.height / rect1.width).toInt()
            ),
        )
    }
}

@Composable
fun DataScreenItemView(
    item: DataScreenComponent,
    direction: Direction,
    modifier: Modifier = Modifier,
    editing: Boolean = true,
) {
    val localDensity = LocalDensity.current
    val rectPath = remember { Path() }
    val circlePath = remember { Path() }
    val combinePath = remember { Path() }
    val textMeasurer = rememberTextMeasurer()
    val numImage = ImageBitmap.imageResource(direction.number)
    val iconImage = if (item.icon != 0) {
        ImageBitmap.imageResource(item.icon)
    } else {
        ImageBitmap(1, 1)
    }
    val unitText = item.unit
    val textForIcon = item.textForIcon

    Canvas(modifier = modifier) {
        rectPath.reset()
        circlePath.reset()
        combinePath.reset()

        val rect = Rect(
            direction.rect.left * size.width,
            direction.rect.top * size.height,
            direction.rect.right * size.width,
            direction.rect.bottom * size.height,
        )

        if (editing) {
            drawEditingOverlay(rectPath, circlePath, combinePath, rect, direction)
        }

        val valueText = item.value
        val valueTextStyle = getValueTextStyle(
            valueText,
            textMeasurer,
            localDensity,
            direction,
            textForIcon.isNotEmpty()
        )
        val valueTextLayoutResult =
            textMeasurer.measure(AnnotatedString(valueText), style = valueTextStyle)
        if (textForIcon.isNotEmpty()) {
            val finalTextForIcon = when (item.labels.size) {
                1 -> "${item.labels[0]} $textForIcon"
                2 -> "${item.labels[0]} $textForIcon ${item.labels[1]}"
                else -> textForIcon
            }
            drawTextForIcon(
                localDensity = localDensity,
                textMeasurer = textMeasurer,
                textForIcon = finalTextForIcon,
                direction = direction,
                rect = rect,
            )
        } else {
            val iconElements = mutableListOf<Any>()
            iconElements.add(iconImage)
            if (item.labels.size == 1) {
                iconElements.add(0, item.labels[0])
            } else if (item.labels.size == 2) {
                iconElements.add(0, item.labels[0])
                iconElements.add(item.labels[1])
            }
            drawIconElements(
                elements = iconElements,
                rect = rect,
                textMeasurer = textMeasurer,
                direction = direction,
                valueTextWidth = valueTextLayoutResult.size.width.toFloat(),
                localDensity = localDensity,
            )
        }

        if (direction is Single) {
            val unitTextStyle = TextStyle(
                color = Color.White,
                fontSize = dpToSp(localDensity, LARGE_UNIT_TEXT_SIZE),
                fontFamily = unitFontFamily(),
                fontWeight = FontWeight.W400,
            )
            val unitTextLayoutResult =
                textMeasurer.measure(AnnotatedString(unitText), style = unitTextStyle)

            val totalWidth = valueTextLayoutResult.size.width + unitTextLayoutResult.size.width

            val textRect = rect.copy(
                top = rect.top + 4 * PADDING.toDp().toPx()
            )

            val textOffset =
                textRect.center.minus(
                    Offset(
                        totalWidth / 2f,
                        valueTextLayoutResult.size.height / 2f
                    )
                )

            drawSuuntoUiNextText(
                textLayoutResult = valueTextLayoutResult,
                topLeft = textOffset
            )

            drawSuuntoUiNextText(
                textLayoutResult = unitTextLayoutResult,
                topLeft = textRect.center.plus(
                    Offset(
                        valueTextLayoutResult.size.width / 2f - unitTextLayoutResult.size.width / 2f,
                        -unitTextLayoutResult.size.height / 2f
                    )
                ).applyUnitPadding(density = density)
            )
        } else {
            val textRect = if (textForIcon.isNotEmpty()) {
                rect.copy(
                    top = rect.top + 4 * PADDING.toDp().toPx()
                )
            } else {
                rect
            }
            drawSuuntoUiNextText(
                textLayoutResult = valueTextLayoutResult,
                topLeft = getTextTopLeft(
                    textRect,
                    valueTextLayoutResult.size.toSize(),
                    direction,
                    density
                )
            )

            if (direction is Top || direction is Bottom || direction is BottomIn3) {
                drawUnitText(
                    localDensity,
                    unitText,
                    textRect,
                    textMeasurer,
                    valueTextLayoutResult.size.toSize(),
                )
            }
        }

        if (editing) {
            drawEditingNumber(rect, numImage, direction)
        }
    }
}

private fun DrawScope.drawEditingOverlay(
    rectPath: Path,
    circlePath: Path,
    combinePath: Path,
    rect: Rect,
    direction: Direction
) {
    rectPath.addRect(
        rect.copy(
            direction.rect.left * size.width - PADDING.dp.value,
            direction.rect.top * size.height - PADDING.dp.value,
            direction.rect.right * size.width + PADDING.dp.value,
            direction.rect.bottom * size.height + PADDING.dp.value,
        )
    )
    circlePath.addOval(Rect(0f, 0f, size.width, size.height))
    combinePath.op(path1 = rectPath, path2 = circlePath, operation = PathOperation.Intersect)

    drawPath(
        path = combinePath,
        color = Color.DarkGray
    )
}

private fun DrawScope.drawEditingNumber(
    rect: Rect,
    numImage: ImageBitmap,
    direction: Direction
) {
    val newWidth = NUM_WIDTH_DP.dp.toPx().roundToInt()
    val newHeight = NUM_HEIGHT_DP.dp.toPx().roundToInt()

    drawImage(
        image = numImage,
        srcSize = IntSize(numImage.width, numImage.height),
        dstSize = IntSize(newWidth, newHeight),
        dstOffset = if (direction is TopRight || direction is TopRightIn3 || direction is BottomRight) {
            rect.roundToIntRect().topRight
                .minus(IntOffset(newWidth, 0))
                .minus(
                    IntOffset(PADDING, 0)
                )
        } else {
            rect.roundToIntRect().topLeft
                .plus(IntOffset(PADDING, 0))
        }
    )
}

private fun getValueTextStyle(
    text: String,
    textMeasurer: TextMeasurer,
    localDensity: Density,
    direction: Direction,
    hasTextForIcon: Boolean,
) = run {
    val maxWidth =
        direction.rect.width * VALUE_TEXT_WIDTH_RATIO * SCREEN_SIZE * localDensity.density
    var textStyle = TextStyle(
        color = Color.White,
        fontSize = if (hasTextForIcon && direction !is Single) {
            dpToSp(localDensity, VALUE_TEXT_SIZE_SECONDARY)
        } else {
            dpToSp(localDensity, direction.valueTextSize)
        },
        fontFamily = valueFontFamily(),
        fontWeight = FontWeight.W600,
    )
    var textLayoutResult =
        textMeasurer.measure(AnnotatedString(text), style = textStyle)
    var textWidth = textLayoutResult.size.width
    while (textWidth > maxWidth) {
        textStyle = textStyle.merge(fontSize = textStyle.fontSize.times(0.98))
        textLayoutResult =
            textMeasurer.measure(AnnotatedString(text), style = textStyle)
        textWidth = textLayoutResult.size.width
    }
    textStyle
}

private fun DrawScope.drawIconElements(
    elements: List<Any>,
    rect: Rect,
    textMeasurer: TextMeasurer,
    direction: Direction,
    valueTextWidth: Float,
    localDensity: Density,
) {
    val density = localDensity.density
    val textStyle = TextStyle(
        color = LABEL_COLOR,
        fontSize = dpToSp(localDensity, ICON_SIDE_TEXT_SIZE),
        fontFamily = labelFontFamily(),
        fontWeight = FontWeight.W700,
    )
    val newIconSize = ICON_SIZE_DP.dp.toPx().roundToInt()
    val elementSizes = mutableListOf<IntSize>()
    elements.forEach {
        if (it is ImageBitmap) {
            elementSizes.add(IntSize(newIconSize, newIconSize))
        } else if (it is String) {
            val textLayoutResult =
                textMeasurer.measure(AnnotatedString(it), style = textStyle)
            val textWidth = textLayoutResult.size.width
            val textHeight = textLayoutResult.size.height
            elementSizes.add(IntSize(textWidth, textHeight))
        }
    }
    val isVertical = direction is Top || direction is Bottom || direction is BottomIn3
    val totalWidth = if (isVertical) {
        elementSizes.maxOf { it.width }
    } else {
        elementSizes.sumOf { it.width }
    }
    val totalHeight = if (isVertical) {
        elementSizes.sumOf { it.height }
    } else {
        elementSizes.maxOf { it.height }
    }
    val totalRect = if (isVertical) {
        Rect(
            left = rect.center.x - valueTextWidth / 2 - totalWidth,
            top = rect.center.y - totalHeight / 2,
            right = rect.center.x - valueTextWidth / 2,
            bottom = rect.center.y + totalHeight / 2,
        )
    } else {
        when (direction) {
            is Single -> Rect(
                left = rect.center.x - totalWidth / 2,
                top = rect.top,
                right = rect.center.x + totalWidth / 2,
                bottom = rect.top + totalHeight,
            )

            is TopLeft, is TopLeftIn3, is BottomLeft -> Rect(
                left = rect.right - totalWidth,
                top = rect.top,
                right = rect.right,
                bottom = rect.top + totalHeight,
            )

            else -> Rect(
                left = rect.left,
                top = rect.top,
                right = rect.left + totalWidth,
                bottom = rect.top + totalHeight,
            )
        }.applyIconPadding(density)
    }
    var drawnWidth = 0
    var drawnHeight = 0
    elements.forEach {
        if (it is ImageBitmap) {
            val iconBitmap: ImageBitmap = it
            val iconWidth = iconBitmap.width
            val iconHeight = iconBitmap.height
            val horizontalPadding = if (isVertical) {
                (totalRect.width - newIconSize) / 2
            } else 0f
            drawImage(
                image = iconBitmap,
                dstOffset = IntOffset(
                    (totalRect.left + drawnWidth + horizontalPadding).roundToInt(),
                    (totalRect.top + drawnHeight).roundToInt(),
                ),
                srcSize = IntSize(iconWidth, iconHeight),
                dstSize = IntSize(newIconSize, newIconSize),
            )
            if (isVertical) {
                drawnHeight += newIconSize
            } else {
                drawnWidth += newIconSize
            }
        } else if (it is String) {
            val text: String = it
            val textLayoutResult =
                textMeasurer.measure(AnnotatedString(text), style = textStyle)
            val textWidth = textLayoutResult.size.width.toFloat()
            val textHeight = textLayoutResult.size.height.toFloat()
            val horizontalPadding = if (isVertical) {
                (totalRect.width - textWidth) / 2
            } else 0f
            val verticalPadding = if (!isVertical) {
                (totalRect.height - textHeight) / 2
            } else 0f
            val textTopLeft = Offset(
                totalRect.left + drawnWidth + horizontalPadding,
                totalRect.top + drawnHeight + verticalPadding,
            )

            drawSuuntoUiNextText(
                textLayoutResult = textLayoutResult,
                topLeft = textTopLeft,
            )
            if (isVertical) {
                drawnHeight += textHeight.roundToInt()
            } else {
                drawnWidth += textWidth.roundToInt()
            }
        }
    }
}

/**
 * The Suunto UI Next font is a little bit on the upper side when displayed, so adjust it manually
 */
private fun DrawScope.drawSuuntoUiNextText(
    textLayoutResult: TextLayoutResult,
    color: Color = Color.Unspecified,
    topLeft: Offset = Offset.Zero,
) {
    drawText(
        textLayoutResult = textLayoutResult,
        color = color,
        topLeft = topLeft.plus(Offset(0f, textLayoutResult.size.height / 10f)),
    )
}

private fun DrawScope.drawTextForIcon(
    localDensity: Density,
    textMeasurer: TextMeasurer,
    textForIcon: String,
    direction: Direction,
    rect: Rect
) {
    val textForIconStyle = TextStyle(
        color = LABEL_COLOR,
        fontSize = dpToSp(localDensity, ICON_SIDE_TEXT_SIZE),
        fontFamily = labelFontFamily(),
        fontWeight = FontWeight.W700
    )

    val textForIconLayoutResult = textMeasurer.measure(
        AnnotatedString(textForIcon),
        style = textForIconStyle
    )

    val availableWidth =
        if (direction is TopLeft || direction is TopLeftIn3 || direction is TopRight || direction is TopRightIn3) {
            rect.width * 4 / 5 // TopLeft/Right layout can't show full text
        } else {
            rect.width
        }

    val textForIconWidth = textForIconLayoutResult.size.width.toFloat()
    val textForIconHeight = textForIconLayoutResult.size.height.toFloat()

    val finalTextForIcon = if (textForIconWidth > availableWidth) {
        var truncatedText = textForIcon
        var truncatedTextLayoutResult =
            textMeasurer.measure(AnnotatedString(truncatedText), style = textForIconStyle)

        while (truncatedTextLayoutResult.size.width > availableWidth && truncatedText.isNotEmpty()) {
            truncatedText = truncatedText.dropLast(1)
            truncatedTextLayoutResult = textMeasurer.measure(
                AnnotatedString("$truncatedText..."),
                style = textForIconStyle
            )
        }
        "$truncatedText..."
    } else {
        textForIcon
    }

    val finalTextForIconLayoutResult =
        textMeasurer.measure(AnnotatedString(finalTextForIcon), style = textForIconStyle)

    val verticalPadding = (ICON_SIZE_DP.dp.toPx() - textForIconHeight) / 2
    val topLeft = when (direction) {
        is TopRight, is TopRightIn3, is BottomRight -> rect.topLeft

        is TopLeft, is TopLeftIn3, is BottomLeft -> rect.topRight.minus(
            Offset(
                finalTextForIconLayoutResult.size.width.toFloat(),
                0f,
            )
        )

        else -> rect.topCenter.minus(
            Offset(
                finalTextForIconLayoutResult.size.width / 2f,
                0f,
            )
        )
    }
    drawSuuntoUiNextText(
        textLayoutResult = finalTextForIconLayoutResult,
        topLeft = topLeft
    )
}

private fun getTextTopLeft(
    rect: Rect,
    textSize: Size,
    direction: Direction,
    density: Float,
): Offset = when (direction) {
    is TopLeft, is BottomLeft -> rect.bottomRight.minus(
        Offset(
            textSize.width,
            textSize.height - 2 * density
        )
    )

    is TopRight, is BottomRight -> rect.bottomLeft.minus(Offset(0f, textSize.height - 2 * density))
    is TopLeftIn3 -> rect.bottomRight.minus(Offset(textSize.width, textSize.height - 3 * density))
    is TopRightIn3 -> rect.bottomLeft.minus(Offset(0f, textSize.height - 3 * density))
    else -> rect.center.minus(Offset(textSize.width / 2, textSize.height / 2))
}

private fun DrawScope.drawUnitText(
    localDensity: Density,
    unitText: String,
    rect: Rect,
    textMeasurer: TextMeasurer,
    textSize: Size,
) {
    val density = localDensity.density
    val unitTextStyle = TextStyle(
        color = Color.White,
        fontSize = dpToSp(localDensity, UNIT_TEXT_SIZE),
        fontFamily = unitFontFamily(),
        fontWeight = FontWeight.W400,
    )
    val unitTextLayoutResult =
        textMeasurer.measure(AnnotatedString(unitText), style = unitTextStyle)

    drawSuuntoUiNextText(
        textLayoutResult = unitTextLayoutResult,
        topLeft = rect.center.minus(
            Offset(
                -textSize.width / 2,
                unitTextLayoutResult.size.toSize().height / 2
            )
        ).applyUnitPadding(density = density)
    )
}

internal fun dpToSp(localDensity: Density, dp: Dp) = with(localDensity) { dp.toSp() }

internal fun valueFontFamily() = FontFamily(
    Font(R.font.suunto_ui_next_display),
)

private fun labelFontFamily() = FontFamily(
    Font(R.font.suunto_ui_next_bold)
)

internal fun unitFontFamily() = FontFamily(
    Font(R.font.suunto_ui_next_regular)
)

private fun Rect.applyIconPadding(density: Float): Rect {
    return translate(Offset(0f, -1.5f * density))
}

private fun Offset.applyUnitPadding(density: Float): Offset {
    return plus(
        Offset(2 * density, 0f)
    )
}

@Preview(widthDp = SCREEN_SIZE, heightDp = SCREEN_SIZE)
@Composable
private fun WatchDataScreenPreview1() {
    val list = DataScreen(
        items = testDataScreens[0].items,
    )
    DataScreenScreen(list)
}

@Preview(widthDp = SCREEN_SIZE, heightDp = SCREEN_SIZE)
@Composable
private fun WatchDataScreenPreview2() {
    val list = DataScreen(
        editing = true,
        items = testDataScreens[1].items,
    )
    DataScreenScreen(list)
}

@Preview(widthDp = SCREEN_SIZE, heightDp = SCREEN_SIZE)
@Composable
private fun WatchDataScreenPreview3() {
    val list = DataScreen(
        items = testDataScreens[2].items,
    )
    DataScreenScreen(list)
}

@Preview
@Composable
private fun WatchDataScreenPreview4() {
    val list = DataScreen(
        items = testDataScreens[3].items,
    )
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.background(color = Color.Black),
    ) {
        Image(
            painter = painterResource(baseDrawable.watch_activity_suunto_run),
            contentDescription = null,
            modifier = Modifier.size(460.dp / 1.9f, 618.dp / 1.9f)
        )
        DataScreenScreen(
            list,
            modifier = Modifier.size(SCREEN_SIZE.dp, SCREEN_SIZE.dp)
        )
    }
}

@Preview
@Composable
private fun WatchDataScreenPreview5() {
    val list = DataScreen(
        items = testManualLapDataScreens[0].items,
        isManualLap = true,
    )
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.background(color = Color.Black),
    ) {
        Image(
            painter = painterResource(baseDrawable.watch_activity_suunto_run),
            contentDescription = null,
            modifier = Modifier.size(460.dp / 1.9f, 618.dp / 1.9f)
        )
        DataScreenScreen(
            list,
            modifier = Modifier.size(SCREEN_SIZE.dp, SCREEN_SIZE.dp)
        )
    }
}
