package com.stt.android.sportmode.entity

object EntityMapperConstants {
    const val STATUS_HIDE = 0
    const val STATUS_OFF = 1
    const val STATUS_ON = 2

    const val INTENSITY_TYPE_HEART = 1
    const val INTENSITY_TYPE_PACE = 2
    const val INTENSITY_TYPE_SPEED = 3
    const val INTENSITY_TYPE_CADENCE = 4
    const val INTENSITY_TYPE_POWER = 5

    const val AUTOLAP_TYPE_DURATION = 2
    const val AUTOLAP_TYPE_DISTANCE = 1
    const val AUTOLAP_TYPE_LOCATION = 3

    const val REMINDER_TYPE_HYDRATION = 1
    const val REMINDER_TYPE_FUELING = 2
    const val REMINDER_TYPE_RETURN = 3

    const val INTENSITY_ZONE_TYPE_HEART = 1
    const val INTENSITY_ZONE_TYPE_PACE = 2
    const val INTENSITY_ZONE_TYPE_POWER = 3

    const val PACE_RUNNER_STATUS_HIDE = 0
    const val PACE_RUNNER_STATUS_SHOW = 1

    const val DATA_SCREEN_FIELD_FIXED = 0
    const val DATA_SCREEN_FIELD_NORMAL = 1
    const val DATA_SCREEN_FIELD_MANUAL_LAP = 2
}
