package com.stt.android.sportmode.home

import android.os.Parcelable
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.domain.workout.ActivityType
import com.stt.android.sportmode.entity.SportTag
import com.stt.android.sportmode.modesetting.Reducer
import com.stt.android.utils.isPositive
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

data class SportModeHomeState(
    val retrieving: Boolean = true,
    val sportHeaderList: List<SportHeader> = emptyList(),
)

interface SportModeHomeReducer : Reducer<SportModeHomeState>

@Parcelize
data class SportHeader(
    val id: Int,
    @DrawableRes
    val iconId: Int,
    @ColorRes
    val colorId: Int,
    @StringRes
    val titleId: Int,
    val modeCount: Int,
    val sportTag: Int? = null,
) : Parcelable {
    @IgnoredOnParcel
    val activityType: ActivityType =
        ActivityType.values().find { it.id == id } ?: ActivityType.DEFAULT
}

fun ActivityType.mapSportHeader(modeCount: Int = 3, tag: Int? = null): SportHeader {
    val sportTag = if (!tag.isPositive()) null else SportTag.fromKey(tag)
    return SportHeader(
        id = id,
        iconId = sportTag?.icon ?: iconId,
        colorId = sportTag?.activityType?.colorId ?: colorId,
        titleId = sportTag?.title ?: localizedStringId,
        modeCount = modeCount,
        sportTag = sportTag?.key,
    )
}
