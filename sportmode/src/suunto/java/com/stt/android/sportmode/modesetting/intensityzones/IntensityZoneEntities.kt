package com.stt.android.sportmode.modesetting.intensityzones

import android.content.Context
import android.os.Parcelable
import androidx.annotation.StringRes
import com.stt.android.domain.workout.ActivityType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.sportmode.entity.EntityMapperConstants
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_ZONE_TYPE_HEART
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_ZONE_TYPE_PACE
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_ZONE_TYPE_POWER
import com.stt.android.sportmode.modesetting.ModeSetting
import com.stt.android.sportmode.modesetting.ModeSettingReducer
import com.stt.android.sportmode.modesetting.baseStr
import com.stt.android.sportmode.modesetting.resString
import com.stt.android.sportmode.modesetting.sportStr
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import com.suunto.connectivity.runsportmodes.entities.IntensityZone as WatchIntensityZone

interface IntensityZoneReducer : ModeSettingReducer<IntensityZone>

@Parcelize
data class IntensityZone(
    val enable: Boolean = false,
    val activityType: ActivityType = ActivityType.RUNNING,
    val zoneType: ZoneType = ZoneType.HeartRate,
    val visibleSubItem: String = "",
) : ModeSetting {
    @IgnoredOnParcel
    override val nameRes: Int = sportStr.intensity_zones

    override fun summary(context: Context, infoModelFormatter: InfoModelFormatter): String =
        summary(context)

    fun summary(context: Context): String =
        if (!enable) {
            baseStr.off.resString(context)
        } else {
            zoneType.nameResId.resString(context)
        }

    companion object {
        fun fromEntity(intensityZone: WatchIntensityZone, activityType: ActivityType) =
            IntensityZone(
                enable = intensityZone.status == EntityMapperConstants.STATUS_ON,
                activityType = activityType,
                zoneType = ZoneType.fromType(intensityZone.zone),
                visibleSubItem = intensityZone.visibleSubItem
            )
    }
}

sealed interface ZoneType : Parcelable {
    val zoneId: Int

    @get:StringRes
    val nameResId: Int

    @Parcelize
    data object HeartRate : ZoneType {
        @IgnoredOnParcel
        override val zoneId: Int = INTENSITY_ZONE_TYPE_HEART

        @IgnoredOnParcel
        override val nameResId: Int = sportStr.intensity_zones_heart_rate
    }

    @Parcelize
    data object Pace : ZoneType {
        @IgnoredOnParcel
        override val zoneId: Int = INTENSITY_ZONE_TYPE_PACE

        @IgnoredOnParcel
        override val nameResId: Int = sportStr.intensity_zones_pace
    }

    @Parcelize
    data object Power : ZoneType {
        @IgnoredOnParcel
        override val zoneId: Int = INTENSITY_ZONE_TYPE_POWER

        @IgnoredOnParcel
        override val nameResId: Int = sportStr.intensity_zones_power
    }

    companion object {
        fun fromType(type: Int) = when (type) {
            INTENSITY_ZONE_TYPE_HEART -> HeartRate
            INTENSITY_ZONE_TYPE_PACE -> Pace
            else -> Power
        }
    }
}
