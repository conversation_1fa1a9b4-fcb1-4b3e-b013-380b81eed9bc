package com.stt.android.sportmode.composables

import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.res.stringResource

@Composable
fun WatchStateSnackbar(
    reason: Int?,
    snackBarHostState: SnackbarHostState
) {
    reason?.let {
        val message = stringResource(id = reason)
        LaunchedEffect(snackBarHostState) {
            snackBarHostState.showSnackbar(
                message = message,
                duration = SnackbarDuration.Indefinite
            )
        }
    }
}
