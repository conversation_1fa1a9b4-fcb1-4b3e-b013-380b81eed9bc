package com.stt.android.sportmode.datascreen

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_DATA_SCREENS
import com.stt.android.sportmode.datascreen.DataScreenEditActivity.Companion.EXTRA_SPORT_ID
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsResource
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsResource.defaultManualLapFields
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsResource.getFieldCount
import com.stt.android.sportmode.datascreen.fields.DataOptionsFieldsResource.getFields
import com.stt.android.sportmode.datascreen.options.EditDataOptions
import com.stt.android.sportmode.datasource.RunSportModesApi
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class DataScreenEditViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val measurementUnit: MeasurementUnit,
    private val runSportModesApi: RunSportModesApi,
) : ViewModel() {

    private var initialDataScreenList: DataScreenList = DataScreenList()

    private val _dataScreensFlow by lazy {
        MutableStateFlow(initialDataScreenList)
    }

    internal val dataScreensFlow by lazy {
        _dataScreensFlow.asStateFlow()
    }

    private val _dataScreenEditEvent = MutableSharedFlow<DataScreenEditEvent>(
        replay = 0,
        extraBufferCapacity = 1,
    )
    internal val dataScreenEditEvent = _dataScreenEditEvent.asSharedFlow()

    private val _intentChannel = Channel<DataScreenEditIntent>(Channel.UNLIMITED)
    private val intentFlow = _intentChannel.consumeAsFlow()

    private val _supportedFieldsFlow = MutableStateFlow(
        DataOptionsFieldsResource.fieldsList.dropLast(1)
    )
    val supportedFieldsFlow: StateFlow<List<DataOptionFields>> = _supportedFieldsFlow.asStateFlow()

    private var activityId = ActivityType.RUNNING.id
    private var selectedFieldsAvailable = false
    private var currentChangeIndex = -1
    private var currentChangeData: DataScreenComponent? = null

    var currentEditDataOptions: EditDataOptions? = null
        private set

    init {
        val dataScreenList = savedStateHandle.get<DataScreenList>(EXTRA_DATA_SCREENS)
            ?: run {
                Timber.w("no data screen list in intent")
                sendEvent(DataScreenEditEvent.Exit)
                DataScreenList()
            }
        activityId = savedStateHandle.get<Int>(EXTRA_SPORT_ID)
            ?: run {
                Timber.w("no sport id in intent")
                sendEvent(DataScreenEditEvent.Exit)
                -1
            }
        initialDataScreenList = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(editing = true)
            }
        )
        _dataScreensFlow.update { initialDataScreenList }
        handleIntents()
        checkSupportManualLapScreen()
    }

    private fun checkSupportManualLapScreen() {
        viewModelScope.launch {
            runSuspendCatching {
                val isManualLapSupported = runSportModesApi.supportsCustomizeLapDataScreen()
                if (isManualLapSupported) {
                    _supportedFieldsFlow.update { DataOptionsFieldsResource.fieldsList }
                }
            }.onFailure {
                Timber.i(it, "Failed to check manual lap support")
            }
        }
    }

    fun checkLaunchedRoute() {
        if (initialDataScreenList.selectedDataScreen.getFields() == null) {
            sendEvent(DataScreenEditEvent.EditFields)
        } else {
            selectedFieldsAvailable = true
        }
    }

    private fun updateFields(fields: DataOptionFields) {
        selectedFieldsAvailable = true
        reduce {
            reduceByFields(fields)
        }
        sendEvent(DataScreenEditEvent.ExitEditingFields)
    }

    private fun cancelEditFields() {
        if (!selectedFieldsAvailable) {
            sendEvent(DataScreenEditEvent.Exit)
        } else {
            sendEvent(DataScreenEditEvent.ExitEditingFields)
        }
    }

    private fun DataScreenList.reduceByFields(fields: DataOptionFields): DataScreenList {
        if (selectedDataScreen.getFields() == fields) return this
        val isManualLap = fields == DataOptionFields.FIELD_MANUAL_LAP
        val updated = copy(
            dataScreens = dataScreens.map { watchData ->
                if (watchData === selectedDataScreen) {
                    val currentSize = watchData.items.size
                    watchData.copy(
                        isManualLap = isManualLap,
                        items = if (isManualLap) {
                            defaultManualLapFields
                        } else {
                            val fieldCount = fields.getFieldCount()
                            if (fieldCount < currentSize) {
                                watchData.items.subList(0, fieldCount)
                            } else if (fieldCount > currentSize) {
                                watchData.items + (1..fieldCount - currentSize).map {
                                    watchData.items.lastOrNull()
                                        ?: DataScreenComponent.fromMapping(DataOptionMapping.BATTERY)
                                }
                            } else {
                                watchData.items
                            }
                        },
                    )
                } else {
                    watchData
                }
            }
        )
        return if (updated.dataScreens.none { it.items.none() } && updated.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            updated.copy(
                dataScreens = updated.dataScreens + DataScreen(editing = true)
            )
        } else {
            updated
        }
    }

    private fun updateSelectedIndex(index: Int) {
        viewModelScope.launch {
            reduce {
                copy(selectedIndex = index)
            }
        }
    }

    private fun changeWatchDataItem(changeIndex: Int, data: DataScreenComponent, isManualLap: Boolean) {
        currentChangeIndex = changeIndex
        currentChangeData = data
        currentEditDataOptions = EditDataOptions(
            activityId = activityId,
            dataOptionId = data.id,
            isManualLap = isManualLap,
        ).also {
            sendEvent(DataScreenEditEvent.EditOptions(it))
        }
    }

    private fun updateDataOptions(updatedDataOptions: EditDataOptions) {
        val updatedWatchDataItem =
            updatedDataOptions.mapComponent(isImperial = measurementUnit == MeasurementUnit.IMPERIAL)
        reduce {
            copy(
                dataScreens = dataScreens.map { watchData ->
                    if (watchData === selectedDataScreen) {
                        watchData.copy(
                            items = watchData.items.mapIndexed { index, watchDataItem ->
                                if (index == currentChangeIndex) {
                                    updatedWatchDataItem
                                } else {
                                    watchDataItem
                                }
                            }
                        )
                    } else {
                        watchData
                    }
                }
            )
        }
        sendEvent(DataScreenEditEvent.ExitEditingOptions)
    }

    private fun delete() {
        val size = _dataScreensFlow.value.dataScreens.size
        reduce {
            val updatedDataScreens = (dataScreens - selectedDataScreen).toMutableList()
            if (updatedDataScreens.none { it.items.none() }) {
                updatedDataScreens += DataScreen(editing = true)
            }
            copy(
                dataScreens = updatedDataScreens.toList(),
                selectedIndex = selectedIndex.coerceAtMost(size - 2)
            )
        }
    }

    fun generateDataScreenListToSave(dataScreenList: DataScreenList): DataScreenList {
        // make editing false
        var toSave = dataScreenList.copy(
            dataScreens = dataScreenList.dataScreens.map {
                it.copy(
                    editing = false
                )
            }
        )
        // new data screen created
        if (toSave.dataScreens.find { it.items.none() } == null && toSave.dataScreens.size < MAX_DATA_SCREEN_SIZE) {
            toSave = toSave.copy(
                dataScreens = toSave.dataScreens + DataScreen(editing = false)
            )
        }
        return toSave
    }

    private fun reduce(reduce: DataScreenList.() -> DataScreenList) {
        _dataScreensFlow.update { it.reduce() }
    }
    
    private fun sendEvent(event: DataScreenEditEvent) {
        _dataScreenEditEvent.tryEmit(event)
    }

    fun handleIntent(intent: DataScreenEditIntent) {
        viewModelScope.launch {
            _intentChannel.send(intent)
        }
    }

    private fun handleIntents() {
        viewModelScope.launch {
            intentFlow.collect { intent ->
                when (intent) {
                    is DataScreenEditIntent.UpdateSelectedIndex -> updateSelectedIndex(intent.index)
                    is DataScreenEditIntent.ChangeWatchDataItem -> changeWatchDataItem(intent.changeIndex, intent.data, intent.isManualLap)
                    is DataScreenEditIntent.Delete -> delete()
                    is DataScreenEditIntent.UpdateDataOptions -> updateDataOptions(intent.editDataOptions)
                    is DataScreenEditIntent.CancelEditFields -> cancelEditFields()
                    is DataScreenEditIntent.UpdateFields -> updateFields(intent.fields)
                }
            }
        }
    }
}
