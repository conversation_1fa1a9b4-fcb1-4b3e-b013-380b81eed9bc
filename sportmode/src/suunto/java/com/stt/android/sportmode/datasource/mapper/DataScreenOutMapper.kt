package com.stt.android.sportmode.datasource.mapper

import com.stt.android.sportmode.datascreen.DataScreenList
import com.stt.android.sportmode.datasource.RunSportModesApi
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_FIXED
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_MANUAL_LAP
import com.stt.android.sportmode.entity.EntityMapperConstants.DATA_SCREEN_FIELD_NORMAL
import com.stt.android.sportmode.modesetting.Mapper
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.DATA_OPTION_SEPARATOR
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse.Companion.GROUP_SEPARATOR
import javax.inject.Inject

class DataScreenOutMapper @Inject constructor(
    private val runSportModesApi: RunSportModesApi,
) : Mapper<DataScreenList, GetDataScreenResponse> {
    override suspend fun invoke(dataScreenList: DataScreenList): GetDataScreenResponse {
        val supportsManualLap = runSportModesApi.supportsCustomizeLapDataScreen()
        val dsData = dataScreenList.dataScreens.filter { it.items.any() }
            .joinToString(separator = GROUP_SEPARATOR) { dataScreen ->
                dataScreen.items.joinToString(separator = DATA_OPTION_SEPARATOR) { it.id }
            }
        val dsTypeList = if (supportsManualLap) {
            dataScreenList.dataScreens.filter { it.items.any() }
                .map {
                    if (it.fixed) {
                        DATA_SCREEN_FIELD_FIXED
                    } else if (it.isManualLap) {
                        DATA_SCREEN_FIELD_MANUAL_LAP
                    } else {
                        DATA_SCREEN_FIELD_NORMAL
                    }
                }
        } else null
        return GetDataScreenResponse(
            dsData = dsData,
            dsTypeList = dsTypeList,
        )
    }
}
