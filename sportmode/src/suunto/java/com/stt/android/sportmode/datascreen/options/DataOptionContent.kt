package com.stt.android.sportmode.datascreen.options

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.sportmode.modesetting.baseColor
import com.stt.android.sportmode.modesetting.baseDrawable
import com.stt.android.sportmode.modesetting.sportStr

data class DataOptionHeaderDisplayItem(
    @StringRes
    val textRes: Int = sportStr.data_options_heart_rate,
    val focused: Boolean = false,
    val checked: Boolean = false,
)

data class DataOptionContentDisplayItem(
    val id: String = "",
    @StringRes
    val textRes: Int = sportStr.data_options_heart_rate,
    val checked: Boolean = false,
    val childGroup: Int = 0,
    val isDivider: Boolean = false,
)

data class DataOptionsDisplayData(
    val headerList: List<DataOptionHeaderDisplayItem>,
    val contentList: List<DataOptionContentDisplayItem>,
)

@Composable
fun DataOptionsContent(
    dataOptionsDisplayData: DataOptionsDisplayData,
    onHeaderItemClick: (index: Int) -> Unit,
    onContentItemClick: (id: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        DataOptionHeaderList(list = dataOptionsDisplayData.headerList, onItemClick = {
            onHeaderItemClick(it)
        })
        DataOptionContentList(list = dataOptionsDisplayData.contentList, onItemClick = {
            onContentItemClick(it)
        })
    }
}

@Composable
fun DataOptionContentItem(
    item: DataOptionContentDisplayItem,
    modifier: Modifier = Modifier,
) {
    if (item.isDivider) {
        Divider(thickness = 10.dp)
        return
    }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .height(MaterialTheme.spacing.xxxlarge)
    ) {
        Text(
            text = stringResource(item.textRes),
            color = MaterialTheme.colors.nearBlack,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Start,
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.medium)
                .weight(1f)
        )
        if (item.checked) {
            Icon(
                painter = painterResource(baseDrawable.ic_checkmark_white),
                contentDescription = "checked",
                tint = colorResource(baseColor.suunto_blue),
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }
    }
}

@Composable
fun DataOptionHeaderItem(
    item: DataOptionHeaderDisplayItem,
    onItemClick: (DataOptionHeaderDisplayItem) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .wrapContentWidth()
            .height(MaterialTheme.spacing.xlarge)
            .clip(RoundedCornerShape(MaterialTheme.spacing.xlarge))
            .background(color = if (item.focused) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey)
            .clickableThrottleFirst {
                onItemClick(item)
            }
            .padding(horizontal = 11.dp)
    ) {
        val tint = if (item.focused) Color.White else MaterialTheme.colors.nearBlack
        if (item.checked) {
            Icon(
                painter = painterResource(baseDrawable.ic_checkmark_white),
                contentDescription = "checked",
                tint = tint
            )
            Text(
                text = stringResource(item.textRes),
                color = tint,
                style = MaterialTheme.typography.body,
                modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall)
            )
        } else {
            Text(
                text = stringResource(item.textRes),
                color = tint,
                style = MaterialTheme.typography.body,
            )
        }
    }
}

@Composable
fun DataOptionHeaderList(
    list: List<DataOptionHeaderDisplayItem>,
    onItemClick: (index: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (list.none()) return
    val listState = remember { LazyListState() }
    LaunchedEffect(Unit) {
        listState.scrollToItem(list.indexOfFirst { it.focused }.coerceAtLeast(0))
    }
    LazyRow(
        state = listState,
        contentPadding = PaddingValues(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = modifier
    ) {
        items(list, key = { it.textRes }) {
            DataOptionHeaderItem(
                item = it,
                onItemClick = { item -> onItemClick(list.indexOf(item)) },
            )
        }
    }
}

@Composable
fun DataOptionContentList(
    list: List<DataOptionContentDisplayItem>,
    onItemClick: (id: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (list.none()) return
    val listState = remember { LazyListState() }
    LaunchedEffect(Unit) {
        listState.scrollToItem(list.indexOfFirst { it.checked }.coerceAtLeast(0))
    }
    LazyColumn(
        state = listState,
        modifier = modifier
    ) {
        items(list) {
            Column {
                DataOptionContentItem(it, modifier = Modifier.clickableThrottleFirst {
                    onItemClick(it.id)
                })
                Divider()
            }
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 400)
@Composable
private fun DataOptionHeaderItemPreview() {
    AppTheme {
        Column {
            DataOptionHeaderItem(
                item = DataOptionHeaderDisplayItem(focused = true),
                onItemClick = {},
            )
            DataOptionHeaderItem(
                item = DataOptionHeaderDisplayItem(focused = true, checked = true),
                onItemClick = {},
            )
            DataOptionHeaderItem(
                item = DataOptionHeaderDisplayItem(checked = true),
                onItemClick = {},
            )
            DataOptionHeaderItem(
                item = DataOptionHeaderDisplayItem(),
                onItemClick = {},
            )
            DataOptionContentItem(
                item = DataOptionContentDisplayItem(checked = true)
            )
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 400)
@Composable
private fun DataOptionItemListPreview() {
    val parentList = listOf(
        DataOptionHeaderDisplayItem(textRes = sportStr.data_options_heart_rate),
        DataOptionHeaderDisplayItem(textRes = sportStr.data_options_environment, checked = true),
        DataOptionHeaderDisplayItem(textRes = sportStr.data_options_temperature, focused = true),
        DataOptionHeaderDisplayItem(textRes = sportStr.data_options_physiology, checked = true, focused = true),
    )
    val list = listOf(
        DataOptionContentDisplayItem(textRes = sportStr.data_options_heart_rate),
        DataOptionContentDisplayItem(textRes = sportStr.data_options_pace),
        DataOptionContentDisplayItem(textRes = sportStr.data_options_duration, checked = true),
        DataOptionContentDisplayItem(textRes = sportStr.data_options_distance),
        DataOptionContentDisplayItem(textRes = sportStr.data_options_carbohydrate_expenditure),
    )

    AppTheme {
        DataOptionsContent(
            DataOptionsDisplayData(
                headerList = parentList,
                contentList = list,
            ),
            onHeaderItemClick = {},
            onContentItemClick = {},
        )
    }
}
