package com.stt.android.sportmode.datasource.mapper

import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_TYPE_CADENCE
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_TYPE_HEART
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_TYPE_PACE
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_TYPE_POWER
import com.stt.android.sportmode.entity.EntityMapperConstants.INTENSITY_TYPE_SPEED
import com.stt.android.sportmode.entity.EntityMapperConstants.PACE_RUNNER_STATUS_SHOW
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_HIDE
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_ON
import com.stt.android.sportmode.modesetting.Mapper
import com.stt.android.sportmode.modesetting.ModeSetting
import com.stt.android.sportmode.modesetting.autolap.Autolap
import com.stt.android.sportmode.modesetting.intensitytarget.IntensityTarget
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType.Cadence
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType.HeartRate
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType.Pace
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType.Power
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType.Speed
import com.stt.android.sportmode.modesetting.intensityzones.IntensityZone
import com.stt.android.sportmode.modesetting.metronome.Metronome
import com.stt.android.sportmode.modesetting.pacerunner.PaceRunner
import com.stt.android.sportmode.modesetting.reminder.Reminder
import com.stt.android.sportmode.modesetting.supportsModeType
import com.suunto.connectivity.runsportmodes.entities.Intensity
import com.suunto.connectivity.runsportmodes.entities.TrainingModeData
import javax.inject.Inject

class ModeSettingInMapper @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val activityTypeToGroupMapper: ActivityTypeToGroupMapper,
) : Mapper<TrainingModeData, List<ModeSetting>> {
    internal lateinit var activityType: ActivityType
    override suspend fun invoke(data: TrainingModeData): List<ModeSetting> {
        val result = mutableListOf<ModeSetting>()

        data.paceRunner?.let { paceRunner ->
            if (paceRunner.status == PACE_RUNNER_STATUS_SHOW) {
                result += PaceRunner.fromEntity(paceRunner)
            }
        }

        data.intensity?.let { intensity ->
            if (intensity.status > STATUS_HIDE) {
                result += IntensityTarget(
                    enable = intensity.status == STATUS_ON,
                    zoneTypes = listOfNotNull(
                        HeartRate(checked = intensity.intensityEnum == INTENSITY_TYPE_HEART || intensity.intensityEnum == 0)
                            .reduce(intensity),
                        Pace(checked = intensity.intensityEnum == INTENSITY_TYPE_PACE).reduce(
                            intensity
                        ),
                        Speed(checked = intensity.intensityEnum == INTENSITY_TYPE_SPEED).reduce(
                            intensity
                        ),
                        Cadence(checked = intensity.intensityEnum == INTENSITY_TYPE_CADENCE).reduce(
                            intensity
                        ),
                        Power(checked = intensity.intensityEnum == INTENSITY_TYPE_POWER).reduce(
                            intensity
                        ),
                    ),
                )
            }
        }

        data.autoLap?.let { autoLap ->
            if (autoLap.status > STATUS_HIDE) {
                result += Autolap.fromEntity(autoLap)
            }
        }

        data.reminder?.let { reminder ->
            if (reminder.status > STATUS_HIDE) {
                result += Reminder.fromEntity(
                    reminder,
                    userSettingsController.settings.measurementUnit
                )
            }
        }

        data.metronome?.let { metronome ->
            if (metronome.status > STATUS_HIDE) {
                result += Metronome.fromEntity(metronome)
            }
        }

        data.intensityZone?.let { intensityZone ->
            if (intensityZone.status > STATUS_HIDE) {
                result += IntensityZone.fromEntity(intensityZone, activityType)
            }
        }

        return result.toList()
    }

    private fun ZoneType.reduce(intensity: Intensity): ZoneType? {
        if (!intensity.visibleSubItem.supportsModeType(zoneTypeId)) return null
        return reduceByUserSettings(
            userSettingsController.settings,
            activityType,
            activityTypeToGroupMapper
        )
            .reduceByEntity(intensity, userSettingsController.settings.measurementUnit)
    }
}
