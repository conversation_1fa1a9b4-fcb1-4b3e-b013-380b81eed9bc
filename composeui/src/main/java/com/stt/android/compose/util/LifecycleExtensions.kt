package com.stt.android.compose.util

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver

/**
 * Observe current Lifecycle.State as Compose state
 *
 * Modified from https://stackoverflow.com/a/69061897
 */
@Composable
fun Lifecycle.observeAsState(): State<Lifecycle.State> {
    val state = remember { mutableStateOf(currentState) }
    DisposableEffect(this) {
        val observer = LifecycleEventObserver { _, event ->
            state.value = event.targetState
        }
        <EMAIL>(observer)
        onDispose {
            <EMAIL>(observer)
        }
    }
    return state
}
