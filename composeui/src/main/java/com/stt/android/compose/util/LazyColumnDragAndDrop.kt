package com.stt.android.compose.util

import android.content.Context
import android.os.Build
import android.view.WindowManager
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.gestures.scrollBy
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.LazyListItemInfo
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.zIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.android.awaitFrame
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch
import kotlin.math.sign

// Compose doesn't have great support for drag & drop, and as of 28.4.2022 its on
// Google's backlog: https://developer.android.com/jetpack/androidx/compose-roadmap
//
// Google's IssueTracker page about the drag and drop: https://issuetracker.google.com/issues/181282427
//
// These helper Composables and class are modified from a sample available in AOSP linked in the IssueTracker.
// The sample had issues with scrolling the list by dragging an item to top or bottom, which have been fixed.
// A callback for checking if particular item can be moved has been added.
// The original sample doesn't have any comments, so all comments are related to the fixes & adjustments.

@Composable
fun rememberDragDropState(
    lazyListState: LazyListState,
    canMove: (Int) -> Boolean,
    onMove: (Int, Int) -> Unit,
    canMoveOver: (Int) -> Boolean = { true },
    onDrop: (from: Int, to: Int) -> Unit = { _, _ -> }
): DragDropState {
    val scope = rememberCoroutineScope()
    val state = remember(lazyListState) {
        DragDropState(
            state = lazyListState,
            canMove = canMove,
            onMove = onMove,
            scope = scope,
            canMoveOver = canMoveOver,
            onDrop = onDrop
        )
    }

    val context = LocalContext.current
    val refreshRate = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        context.display?.refreshRate
    } else {
        (context.getSystemService(Context.WINDOW_SERVICE) as? WindowManager)?.defaultDisplay?.refreshRate
    } ?: 60f

    LaunchedEffect(state) {
        var scrollJob: Job? = null
        while (true) {
            // The original sample scrolls only on one frame per value in scrollChannel,
            // which means that the user would need to keep moving the dragged item to send
            // new values and scroll the list. Here it has been adjusted that to keep scrolling
            // a bit every frame until 0f is received from the channel.

            val scrollRateMultiplier = 16f // for adjusting scroll speed
            val scrollRate = state.scrollChannel.receive() * scrollRateMultiplier
            scrollJob?.cancel()
            if (scrollRate != 0f) {
                scrollJob = launch {
                    // First 2 frames scroll by target frame time before we're up to speed with
                    // actual frame times. This way we don't need to keep listening to frame times
                    // all the time or delay starting the scroll. If we delayed the animation we'd
                    // also need to consider the case of scrollRate changing every frame.
                    var frameTimeSeconds = (1000f / refreshRate).div(1000)
                    var previousFrameNanos: Long? = null
                    while (true) {
                        lazyListState.scrollBy(scrollRate * frameTimeSeconds)
                        val frameNanos = awaitFrame()
                        frameTimeSeconds = previousFrameNanos?.let {
                            (frameNanos - it).toFloat().div(1_000_000_000)
                        } ?: frameTimeSeconds
                        previousFrameNanos = frameNanos
                    }
                }
            }
        }
    }
    return state
}

class DragDropState internal constructor(
    private val state: LazyListState,
    private val scope: CoroutineScope,
    private val canMove: (Int) -> Boolean,
    private val onMove: (Int, Int) -> Unit,
    private val canMoveOver: (Int) -> Boolean,
    private val onDrop: (from: Int, to: Int) -> Unit
) {
    var draggingItemIndex by mutableStateOf<Int?>(null)
        private set

    private var lastMovedItemKey: Any? = null
    private var lastItemMoveSign: Float = 0f

    internal val scrollChannel = Channel<Float>(capacity = 1)

    private var draggingItemDraggedDelta by mutableFloatStateOf(0f)
    private var draggingItemInitialOffset by mutableIntStateOf(0)
    internal val draggingItemOffset: Float
        get() = draggingItemLayoutInfo?.let { item ->
            draggingItemInitialOffset + draggingItemDraggedDelta - item.offset
        } ?: 0f

    private val draggingItemLayoutInfo: LazyListItemInfo?
        get() = state.layoutInfo.visibleItemsInfo
            .firstOrNull { it.index == draggingItemIndex }

    internal var originalIndexOfDraggedItem by mutableStateOf<Int?>(null)
        private set
    internal var previousIndexOfDraggedItem by mutableStateOf<Int?>(null)
        private set
    internal var previousItemOffset = Animatable(0f)
        private set

    internal fun onDragStart(offset: Offset) {
        state.layoutInfo.visibleItemsInfo
            .firstOrNull { item ->
                offset.y.toInt() in item.offset..(item.offset + item.size)
            }?.also {
                if (!canMove(it.index)) return@also

                draggingItemIndex = it.index
                originalIndexOfDraggedItem = it.index
                draggingItemInitialOffset = it.offset
            }
    }

    internal fun onDragInterrupted() {
        if (draggingItemIndex != null) {
            previousIndexOfDraggedItem = draggingItemIndex
            val startOffset = draggingItemOffset
            scope.launch {
                previousItemOffset.snapTo(startOffset)
                previousItemOffset.animateTo(
                    0f,
                    spring(
                        stiffness = Spring.StiffnessMediumLow,
                        visibilityThreshold = 1f
                    )
                )
                previousIndexOfDraggedItem = null
            }

            val originalIndex = originalIndexOfDraggedItem
            val currentIndex = draggingItemIndex
            if (originalIndex != null && currentIndex != null) {
                onDrop(originalIndex, currentIndex)
            }
        }
        draggingItemDraggedDelta = 0f
        draggingItemIndex = null
        draggingItemInitialOffset = 0
        lastItemMoveSign = 0f
        originalIndexOfDraggedItem = null
        scrollChannel.trySend(0f)
    }

    internal fun onDrag(offset: Offset) {
        draggingItemDraggedDelta += offset.y

        val draggingItem = draggingItemLayoutInfo ?: return
        val startOffset = draggingItem.offset + draggingItemOffset
        val endOffset = startOffset + draggingItem.size
        val middleOffset = startOffset + (endOffset - startOffset) / 2f

        val targetItem = state.layoutInfo.visibleItemsInfo.find { item ->
            middleOffset.toInt() in item.offset..item.offsetEnd &&
                draggingItem.index != item.index
        }

        // Originally the sample wouldn't send event to scrollChannel and swap items on same onDrag event,
        // instead when it found an item to swap with it would try to scroll the list in a different manner
        // to keep the dragged item visible, and then on next onDrag event send a regular one and start
        // twitching in a way that could even undo the item swap, causing more twitching.
        // The logic has been modified to allow both swaps and regular scrolling happen on same event,
        // and to make sure that some race condition doesn't undo the swap we make sure that the dragging
        // direction has to change before we can swap with the same item again.
        if (targetItem != null &&
            canMoveOver(targetItem.index) &&
            (lastMovedItemKey != targetItem.key || offset.y.sign != lastItemMoveSign)
        ) {
            if (
                draggingItem.index == state.firstVisibleItemIndex ||
                targetItem.index == state.firstVisibleItemIndex
            ) {
                state.requestScrollToItem(
                    state.firstVisibleItemIndex,
                    state.firstVisibleItemScrollOffset
                )
            }
            lastMovedItemKey = targetItem.key
            lastItemMoveSign = offset.y.sign
            onMove.invoke(draggingItem.index, targetItem.index)
            draggingItemIndex = targetItem.index
        } else {
            val overscroll =
                when {
                    draggingItemDraggedDelta > 0 ->
                        (endOffset - state.layoutInfo.viewportEndOffset).coerceAtLeast(0f)
                    draggingItemDraggedDelta < 0 ->
                        (startOffset - state.layoutInfo.viewportStartOffset).coerceAtMost(0f)
                    else -> 0f
                }
            if (overscroll != 0f) {
                scrollChannel.trySend(overscroll)
            }
        }
    }

    private val LazyListItemInfo.offsetEnd: Int
        get() = this.offset + this.size

    private val offsetFromInitItem: Float
        get() = draggingItemInitialOffset + draggingItemDraggedDelta

    internal val canDragItem: Boolean
        get() = when {
            draggingItemIndex == null -> false
            // drag up to the first item
            draggingItemIndex == 0 && draggingItemDraggedDelta < 0 -> offsetFromInitItem > 0
            // drag down to the last item
            draggingItemIndex == state.layoutInfo.totalItemsCount - 1 && draggingItemDraggedDelta > 0 -> offsetFromInitItem < (draggingItemLayoutInfo?.offset
                ?: 0)

            else -> true
        }
}

fun Modifier.dragContainer(
    dragDropState: DragDropState,
    onDragStarted: () -> Unit = {},
    onDragInterrupted: () -> Unit = {}
): Modifier {
    return pointerInput(dragDropState) {
        detectDragGesturesAfterLongPress(
            onDrag = { change, offset ->
                change.consume()
                dragDropState.onDrag(offset = offset)
            },
            onDragStart = { offset ->
                dragDropState.onDragStart(offset)
                onDragStarted()
            },
            onDragEnd = {
                dragDropState.onDragInterrupted()
                onDragInterrupted()
            },
            onDragCancel = {
                dragDropState.onDragInterrupted()
                onDragInterrupted()
            }
        )
    }
}

@Composable
fun Modifier.dragHandle(
    dragDropState: DragDropState,
    listPositionInRoot: Offset,
    onDragStarted: () -> Unit = {},
    onDragInterrupted: () -> Unit = {}
): Modifier {
    var handleSize by remember { mutableStateOf(IntSize.Zero) }
    var handlePositionInRoot by remember { mutableStateOf(Offset.Zero) }
    val offsetRef by rememberUpdatedState { handlePositionInRoot - listPositionInRoot }
    return onGloballyPositioned {
        handleSize = it.size
        handlePositionInRoot = it.positionInRoot()
    }.pointerInput(dragDropState, handleSize) {
        detectDragGestures(
            onDrag = { change, offset ->
                change.consume()
                dragDropState.onDrag(offset = offset)
            },
            onDragStart = { offset ->
                if (0 < offset.y && offset.y < handleSize.height) {
                    dragDropState.onDragStart(offsetRef() + offset)
                    onDragStarted()
                }
            },
            onDragEnd = {
                dragDropState.onDragInterrupted()
                onDragInterrupted()
            },
            onDragCancel = {
                dragDropState.onDragInterrupted()
                onDragInterrupted()
            }
        )
    }
}

@Composable
fun LazyItemScope.DraggableItem(
    dragDropState: DragDropState,
    index: Int,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.(isDragging: Boolean) -> Unit
) {
    val dragging = index == dragDropState.draggingItemIndex
    val draggingModifier = if (dragging) {
        Modifier
            .zIndex(1f)
            .graphicsLayer {
                if (dragDropState.canDragItem)
                    translationY = dragDropState.draggingItemOffset
            }
    } else if (index == dragDropState.previousIndexOfDraggedItem) {
        Modifier
            .zIndex(1f)
            .graphicsLayer {
                if (dragDropState.canDragItem)
                    translationY = dragDropState.previousItemOffset.value
            }
    } else {
        Modifier.animateItem(fadeInSpec = null, fadeOutSpec = null)
    }
    Column(modifier = modifier.then(draggingModifier)) {
        content(dragging)
    }
}
