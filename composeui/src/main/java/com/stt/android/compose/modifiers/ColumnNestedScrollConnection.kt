package com.stt.android.compose.modifiers

import androidx.compose.foundation.ScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource

@Composable
fun rememberColumnNestScrollConnection(
    listState: ScrollState
) = remember {
    object : NestedScrollConnection {
        override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
            return if ((available.y < 0 && listState.canScrollForward) || (available.y > 0 && listState.canScrollBackward)) {
                listState.dispatchRawDelta(-available.y)
                available
            } else {
                Offset.Zero
            }
        }
    }
}
