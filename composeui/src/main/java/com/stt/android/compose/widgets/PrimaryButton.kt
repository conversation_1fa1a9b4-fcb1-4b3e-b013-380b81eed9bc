package com.stt.android.compose.widgets

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.isMaterial3
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.core.R
import java.util.Locale
import androidx.compose.material3.MaterialTheme as M3Theme

@Composable
fun PrimaryButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    buttonHeight: Dp = dimensionResource(R.dimen.height_elevated_button),
    enabled: Boolean = true,
    backgroundColor: Color = if (isMaterial3()) {
        M3Theme.colorScheme.primary
    } else {
        MaterialTheme.colors.primary
    },
    textColor: Color = if (isMaterial3()) {
        M3Theme.colorScheme.onPrimary
    } else {
        MaterialTheme.colors.onPrimary
    },
) {
    PrimaryButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        backgroundColor = backgroundColor,
        buttonHeight = buttonHeight,
    ) {
        Text(
            text = text.uppercase(Locale.getDefault()),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = textColor,
        )
    }
}

@Composable
fun PrimaryButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    buttonHeight: Dp = dimensionResource(R.dimen.height_elevated_button),
    enabled: Boolean = true,
    backgroundColor: Color = if (isMaterial3()) {
        M3Theme.colorScheme.primary
    } else {
        MaterialTheme.colors.primary
    },
    throttleClicks: Boolean = false,
    content: @Composable (RowScope.() -> Unit),
) {
    val eventThrottler = if (throttleClicks) rememberEventThrottler() else null
    Button(
        enabled = enabled,
        modifier = modifier.height(buttonHeight),
        onClick = { eventThrottler?.processEvent { onClick() } ?: onClick() },
        colors = ButtonDefaults.buttonColors(backgroundColor = backgroundColor),
        shape = RoundedCornerShape(8.dp)
    ) {
        content()
    }
}

@Preview
@Composable
private fun PrimaryButtonPreview() {
    Column {
        AppTheme {
            PrimaryButton(
                text = "Do the thing",
                onClick = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }

        M3AppTheme {
            PrimaryButton(
                text = "Do the thing",
                onClick = {},
                modifier = Modifier.padding(M3Theme.spacing.medium)
            )
        }
    }
}

@Preview
@Composable
private fun DisabledPrimaryButtonPreview() {
    Column {
        AppTheme {
            PrimaryButton(
                text = "Cannot do the thing",
                onClick = {},
                enabled = false,
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
            )
        }

        M3AppTheme {
            PrimaryButton(
                text = "Cannot do the thing",
                onClick = {},
                enabled = false,
                modifier = Modifier.padding(M3Theme.spacing.medium),
            )
        }
    }
}

@Preview
@Composable
private fun ColorfulPrimaryButtonPreview() {
    Column {
        AppTheme {
            PrimaryButton(
                text = "Colorful example",
                onClick = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                backgroundColor = Color(0xFF0057B7),
                textColor = Color(0xFFFFD700),
            )
        }

        M3AppTheme {
            AppTheme {
                PrimaryButton(
                    text = "Colorful example",
                    onClick = {},
                    modifier = Modifier.padding(M3Theme.spacing.medium),
                    backgroundColor = Color(0xFF0057B7),
                    textColor = Color(0xFFFFD700),
                )
            }
        }
    }
}

@Preview
@Composable
private fun PrimaryButtonWithCustomContentPreview() {
    Column {
        AppTheme {
            PrimaryButton(
                onClick = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(32.dp),
                    progress = 0.75f,
                    color = MaterialTheme.colors.onPrimary,
                )
            }
        }

        M3AppTheme {
            PrimaryButton(
                onClick = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(32.dp),
                    progress = 0.75f,
                    color = M3Theme.colorScheme.onPrimary,
                )
            }
        }
    }
}
