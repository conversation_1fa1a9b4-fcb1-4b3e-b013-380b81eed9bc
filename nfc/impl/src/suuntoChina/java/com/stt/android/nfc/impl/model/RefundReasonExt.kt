package com.stt.android.nfc.impl.model

import androidx.annotation.StringRes
import com.stt.android.nfc.impl.R

val RefundReason.resId: Int
    @StringRes get() = when (this) {
        RefundReason.ALREADY_HAVE_CARD -> R.string.transit_card_refund_reason_already_have_card
        RefundReason.DO_NOT_LIKE_IT -> R.string.transit_card_refund_reason_do_not_like_it
        RefundReason.DO_NOT_USE_ENOUGH -> R.string.transit_card_refund_reason_do_not_use_enough
        RefundReason.MOVED_TO_ANOTHER_CITY -> R.string.transit_card_refund_reason_moved_to_another_city
        RefundReason.SWITCHED_TO_UNSUPPORTED_WATCH -> R.string.transit_card_refund_reason_switched_to_unsupported_watch
        RefundReason.OTHER -> R.string.transit_card_refund_reason_other
    }
