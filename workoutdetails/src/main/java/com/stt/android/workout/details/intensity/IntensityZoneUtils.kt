package com.stt.android.workout.details.intensity

import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.intensityzone.IntensityZone
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.intensityzone.ZoneRange
import com.stt.android.intensityzone.ZoneRangeWithColor
import com.stt.android.workout.details.HrGraphData

object IntensityZoneUtils {
    fun getMainGraphZoneLimits(
        mainGraphType: GraphType,
        graphDataMaxYValue: Float,
        graphDataMinYValue: Float,
        measurementUnit: MeasurementUnit,
        hrGraphData: HrGraphData?,
        intensityExtension: IntensityExtension?
    ): IntensityZoneLimits? {
        return when (mainGraphType) {
            GraphType.Summary(SummaryGraph.PACE) -> {
                val originalSpeedZones = intensityExtension?.intensityZones?.speed
                getZoneLimits(
                    graphDataMaxYValue,
                    graphDataMinYValue,
                    originalSpeedZones?.toPaceUnit(measurementUnit),
                    true
                )
            }

            GraphType.Summary(SummaryGraph.POWER) -> {
                getZoneLimits(
                    graphDataMaxYValue,
                    graphDataMinYValue,
                    intensityExtension?.intensityZones?.power,
                    false
                )
            }

            GraphType.Summary(SummaryGraph.HEARTRATE), GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> {
                hrGraphData?.zoneLimits
            }

            GraphType.Summary(SummaryGraph.AEROBICZONE) -> {
                IntensityZoneLimits.createAerobicZoneIntensityZoneLimits()
            }

            else -> null
        }
    }

    private fun getZoneLimits(
        graphDataMaxYValue: Float,
        graphDataMinYValue: Float,
        zones: IntensityZonesData?,
        isInverted: Boolean
    ): IntensityZoneLimits? {
        return zones?.let {
            val firstValue =
                if (isInverted) graphDataMaxYValue.coerceAtLeast(it.zone2LowerLimit) else 0.0f

            val lastValue =
                if (isInverted) graphDataMinYValue.coerceAtMost(it.zone5LowerLimit) else graphDataMaxYValue.coerceAtLeast(
                    it.zone5LowerLimit
                )

            val limits = listOf(
                firstValue,
                it.zone2LowerLimit,
                it.zone3LowerLimit,
                it.zone4LowerLimit,
                it.zone5LowerLimit,
                lastValue
            )

            return IntensityZoneLimits(
                listOf(
                    ZoneRange(IntensityZone.WARMUP, limits.first(), limits[1]),
                    ZoneRange(IntensityZone.ENDURANCE, limits[1], limits[2]),
                    ZoneRange(IntensityZone.AEROBIC, limits[2], limits[3]),
                    ZoneRange(IntensityZone.ANAEROBIC, limits[3], limits[4]),
                    ZoneRange(IntensityZone.PEAK, limits[4], limits[5])
                )
            )
        }
    }

    private fun IntensityZonesData.toPaceUnit(measurementUnit: MeasurementUnit): IntensityZonesData {
        fun Float.toPaceUnit() = measurementUnit.toPaceUnit(this.toDouble()).toFloat()

        return this.copy(
            zone2LowerLimit = zone2LowerLimit.toPaceUnit(),
            zone3LowerLimit = zone3LowerLimit.toPaceUnit(),
            zone4LowerLimit = zone4LowerLimit.toPaceUnit(),
            zone5LowerLimit = zone5LowerLimit.toPaceUnit()
        )
    }

    fun getColorForValue(
        value: Float,
        isInverted: Boolean,
        zoneRangesWithColors: List<ZoneRangeWithColor>,
        graphType: GraphType
    ): Int? {
        val zoneRange = if (graphType == GraphType.Summary(SummaryGraph.AEROBICZONE)) {
            fun isWithinRange(value: Float, start: Float, end: Float): Boolean {
                return if (start <= end) {
                    value in start..end
                } else {
                    value in end..start
                }
            }

            zoneRangesWithColors.firstOrNull {
                isWithinRange(value, it.zoneRange.start, it.zoneRange.end)
            }
        } else {
            zoneRangesWithColors.firstOrNull {
                checkIntensityZone(
                    value = value,
                    zoneRange = it.zoneRange,
                    inverted = isInverted
                )
            }
        }
        return zoneRange?.color
    }

    private fun checkIntensityZone(
        value: Float,
        zoneRange: ZoneRange,
        inverted: Boolean
    ): Boolean {
        val start = zoneRange.start
        val end = zoneRange.end

        return when (zoneRange.intensityZone) {
            IntensityZone.PEAK -> if (inverted) value <= end else value >= start
            IntensityZone.WARMUP -> if (inverted) value > end else value < end
            else -> if (inverted) value <= start && value > end else value >= start && value < end
        }
    }
}
