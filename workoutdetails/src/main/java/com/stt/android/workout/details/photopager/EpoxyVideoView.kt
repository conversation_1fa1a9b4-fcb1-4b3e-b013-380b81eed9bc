package com.stt.android.workout.details.photopager

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageButton
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.stt.android.domain.user.VideoInformation
import com.stt.android.multimedia.video.ExoPlayerHelper
import com.stt.android.workout.details.CoverImage
import com.stt.android.workout.details.R
import timber.log.Timber
import com.stt.android.R as BaseR

@ModelView
class EpoxyVideoView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr),
    DefaultLifecycleObserver {

    private var currentPlayer: ExoPlayer?
        get() {
            return playerView.player as ExoPlayer?
        }
        set(value) {
            playerView.player = value
        }

    private var enablePlayOnStart = false

    @ModelProp
    lateinit var video: CoverImage.VideoCoverImage

    @ModelProp(ModelProp.Option.DoNotHash)
    lateinit var viewLifecycle: Lifecycle

    @ModelProp(ModelProp.Option.DoNotHash)
    lateinit var onMuteButtonClicked: OnClickListener

    private val playerView: StyledPlayerView by lazy { findViewById(R.id.video_player) }
    private val muteButton: ImageButton by lazy { findViewById(R.id.mute_video) }

    @AfterPropsSet
    fun bind() {
        muteButton.setOnClickListener(onMuteButtonClicked)
        val icon = if (video.isMuted) {
            BaseR.drawable.ic_speaker_off
        } else {
            BaseR.drawable.ic_speaker_full
        }
        muteButton.setImageDrawable(
            AppCompatResources.getDrawable(muteButton.context, icon)
        )
        viewLifecycle.addObserver(this)
        currentPlayer?.muteVideo(video.isMuted)
    }

    fun onVisible() {
        currentPlayer?.playWhenReady = true
    }

    fun onInvisible() {
        currentPlayer?.playWhenReady = false
    }

    override fun onStart(owner: LifecycleOwner) {
        Timber.d("ON_START event: create and setup the player.")
        createAndSetupPlayer(enablePlayOnStart)
        enablePlayOnStart = false
    }

    override fun onStop(owner: LifecycleOwner) {
        Timber.d("ON_STOP event: Stop and release the player.")
        enablePlayOnStart = currentPlayer?.playWhenReady ?: false
        stopAndReleasePlayer()
    }

    private fun createAndSetupPlayer(playWhenReady: Boolean) {
        if (currentPlayer == null) {
            Timber.d("Create and setup player")
            currentPlayer = createPlayer(playerView, video)
            currentPlayer?.playWhenReady = playWhenReady
        }
    }

    private fun stopAndReleasePlayer() {
        Timber.d("Stopping player playback and disposing player")
        currentPlayer?.apply {
            stop()
            release()
        }
        currentPlayer = null
    }

    private fun createPlayer(
        playerView: StyledPlayerView,
        videoCoverImage: CoverImage.VideoCoverImage
    ): ExoPlayer? {
        return if (playerView.player == null) {
            val videoInformation = VideoInformation.fromVideo(videoCoverImage.video)
            val context = playerView.context
            val uri = videoInformation.getUri(context) ?: return null
            Timber.d("Creating player and loading video from [uri: $uri]")
            ExoPlayerHelper.createPlayer(context, videoCoverImage.userAgent).apply {
                playerView.player = this
                ExoPlayerHelper.setMedia(this, uri, true)
                prepare()
                playWhenReady = true
            }
        } else {
            Timber.d("Using existing player")
            playerView.player as ExoPlayer
        }.apply {
            val muted = videoCoverImage.isMuted
            Timber.d("Mute player = $muted")
            muteVideo(muted)
        }
    }

    private fun ExoPlayer.muteVideo(muted: Boolean) {
        volume = if (muted) 0.0f else 1.0f
    }
}
