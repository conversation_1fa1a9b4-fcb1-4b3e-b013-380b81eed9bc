package com.stt.android.workout.details.aerobiczone

import androidx.annotation.StringRes
import androidx.compose.runtime.Immutable

@Immutable
data class AerobicZoneInfoUiState(
    val maxHr: ZoneData?,
    val anaerobicThreshold: ZoneData,
    val measuredAnaerobicThreshold: ZoneData? = null,
    val zone4Minimum: ZoneData,
    val aerobicThreshold: ZoneData,
    val measuredAerobicThreshold: ZoneData? = null,
    val zone2Minimum: ZoneData,
    @StringRes val unit: Int,
) {
    val showShouldUpdateZones: Boolean get() =
        !measuredAnaerobicThreshold?.value.isNullOrEmpty() ||
            !measuredAerobicThreshold?.value.isNullOrEmpty()
}

@Immutable
data class ZoneData(
    val value: String,
    val percentage: Int? = null,
)
