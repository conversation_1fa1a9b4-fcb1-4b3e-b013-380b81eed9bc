package com.stt.android.workout.details.share.colortrack

import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.ui.map.MapHelper

class HeartRateColorTrackGenerator(
    val heartRateEvents: List<WorkoutHrEvent>,
) : ColorTrackGenerator {
    override fun generate(geoPoints: List<WorkoutGeoPoint>, sml: Sml?): ColorTrackDescriptor {
        if (heartRateEvents.isEmpty()) return ColorTrackDescriptor.from(geoPoints)

        val buckets = Bucket.createBucketsFromList(
            heartRateEvents,
            value = { heartRate.toDouble() },
            timestamp = { timestamp },
            pauseResumeTimes = with(MapHelper) {
                sml?.streamData?.findPauseResumeTimePairs()
            },
        )
        return ColorTrackDescriptor.from(geoPoints, buckets)
    }
}
