package com.stt.android.divecustomization.customization.logic

import com.soy.algorithms.divemodecustomization.entities.DeviceName
import com.soy.algorithms.divemodecustomization.entities.DiveDeviceConfig
import com.soy.algorithms.divemodecustomization.entities.Mode
import com.soy.algorithms.divemodecustomization.entities.validationrules.domain.SmlEditor
import com.stt.android.common.viewstate.ViewState
import com.stt.android.divecustomization.customization.barToPa
import com.stt.android.divecustomization.customization.entities.CustomizationModeWithAvailableOptions
import com.stt.android.divecustomization.customization.entities.DepthUnits
import com.stt.android.divecustomization.customization.entities.PressureUnits
import com.stt.android.divecustomization.customization.feetToMeters
import com.stt.android.divecustomization.customization.getAvailableValuesList
import com.stt.android.divecustomization.customization.getNearestValue
import com.stt.android.divecustomization.customization.metersToFeet
import com.stt.android.divecustomization.customization.paToBar
import com.stt.android.divecustomization.customization.paToPsi
import com.stt.android.divecustomization.customization.psiToPa
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.StateFlow
import java.math.RoundingMode

interface CustomizationModeParent {
    fun getCurrentMode(): Mode
    fun getDeviceConfig(): DiveDeviceConfig
    fun setDeviceConfig(deviceConfig: DiveDeviceConfig, triggerValidation: Boolean = false)
    fun setCurrentMode(mode: Mode)
    fun getDeviceName(): DeviceName
    fun getDeviceSerial(): String
    fun getDeviceHwVersion(): String
    fun getDeviceFwVersion(): String
    fun isNewMode(): Boolean
    val currentModeFlow: StateFlow<ViewState<CustomizationModeWithAvailableOptions>>

    /**
     * Method for converting the depth values from device units back into the
     * SI units that validator uses.
     */
    fun deviceToSiUnitsDepth(meterValue: Double): Double {
        val depthUnits = DepthUnits.from(getDeviceConfig().units.depth.value)
        return if (depthUnits == DepthUnits.FEET) {
            feetToMeters(meterValue)
        } else {
            meterValue
        }
    }

    fun depthValuesFromConditions(editor: SmlEditor?): ImmutableList<Double> {
        return getAvailableValuesList(editor).map {
            it.toBigDecimal().setScale(1, RoundingMode.HALF_UP).toDouble()
        }.toImmutableList()
    }

    /**
     * Method for getting the selected depth value according to the units of the device.
     * Due to the loss of precision during conversion, there's a chance that we couldn't
     * find the selected value in the list of available values. In such a case, we select
     * the closest value.
     */
    fun selectedDepthValue(depth: Double, depthValues: List<Double>): Double {
        val depthUnits = DepthUnits.from(getDeviceConfig().units.depth.value)
        return if (depthUnits == DepthUnits.FEET) {
            // We get the nearest value since Double matching can be imprecise
            getNearestValue(depthValues, metersToFeet(depth))
        } else {
            getNearestValue(depthValues, depth)
        }
    }

    fun pressureValuesFromConditions(editor: SmlEditor?): ImmutableList<Double> {
        // with the updated data, we don't need to do the conversion, data should be in the right units
        return getAvailableValuesList(editor).map {
            it.toBigDecimal().setScale(1, RoundingMode.HALF_UP).toDouble()
        }.toImmutableList()
    }

    /**
     * Method for getting the selected pressure value according to the units of the device.
     * Due to the loss of precision during conversion, there's a chance that we couldn't
     * find the selected value in the list of available values. In such a case, we select
     * the closest value.
     */
    fun selectedPressureValue(pressure: Double, pressureValues: List<Double>): Double {
        val pressureUnits = PressureUnits.from(getDeviceConfig().units.tankPressure.value)
        return if (pressureUnits == PressureUnits.BAR) {
            getNearestValue(pressureValues, paToBar(pressure))
        } else {
            getNearestValue(pressureValues, paToPsi(pressure))
        }
    }

    /**
     * Method for converting the pressure values from device units back into the
     * SI units that validator uses.
     */
    fun deviceToSiUnitsPressure(pressureValue: Double): Double {
        val pressureUnits = PressureUnits.from(getDeviceConfig().units.tankPressure.value)
        return if (pressureUnits == PressureUnits.BAR) {
            barToPa(pressureValue)
        } else {
            psiToPa(pressureValue)
        }
    }
}
