package com.stt.android.divecustomization.customization.ui.displays.switchablefieldsselection

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.Divider
import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.entities.displays.DiveDisplayFieldsMultiSelectionListContent
import com.stt.android.divecustomization.customization.ui.common.DiveGenericSnackBar
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun DiveDisplayFieldsList(
    content: DiveDisplayFieldsMultiSelectionListContent,
    onCheckedChange: (checked: Boolean, value: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val snackbarHostState = remember { SnackbarHostState() }
    Card(modifier = modifier) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            content.optionsList.forEach { data ->
                val option = data.option
                Row(
                    modifier = Modifier
                        .height(56.dp)
                        .fillMaxWidth()
                        .clickable(
                            enabled = data.isEnabled,
                            onClick = {
                                onCheckedChange(!option.isSelected(), option.optionType.value)
                            }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = option.getString(),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(dimensionResource(BaseR.dimen.size_spacing_small)),
                        color = if (!data.isEnabled) {
                            colorResource(BaseR.color.dive_disable_text_color)
                        } else if (option.isSelected()) {
                            colorResource(BaseR.color.newAccent)
                        } else {
                            colorResource(CR.color.black)
                        }
                    )

                    Checkbox(
                        modifier = Modifier.wrapContentWidth(unbounded = true),
                        checked = option.isSelected(),
                        enabled = data.isEnabled,
                        colors = CheckboxDefaults.colors(checkedColor = colorResource(BaseR.color.newAccent)),
                        onCheckedChange = {
                            onCheckedChange(it, option.optionType.value)
                        }
                    )
                }
                Divider(color = Color.LightGray, thickness = 1.dp)
            }

            if (content.isMaxFieldLimitReached) {
                val actionLabel = stringResource(BaseR.string.ok)
                val maxFieldCount =
                    stringResource(R.string.dive_max_fields_reached, content.maxAllowedFieldsCount)
                LaunchedEffect(snackbarHostState) {
                    snackbarHostState.showSnackbar(
                        message = maxFieldCount,
                        actionLabel = actionLabel,
                        duration = SnackbarDuration.Indefinite
                    )
                }
            }
        }
        DiveGenericSnackBar(snackbarHostState = snackbarHostState)
    }
}
