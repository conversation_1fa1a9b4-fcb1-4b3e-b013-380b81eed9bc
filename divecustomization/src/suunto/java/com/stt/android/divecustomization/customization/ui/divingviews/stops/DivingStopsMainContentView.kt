package com.stt.android.divecustomization.customization.ui.divingviews.stops

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import com.stt.android.common.viewstate.ViewState
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.entities.settings.DiveCustomizationStopsContent
import com.stt.android.divecustomization.customization.logic.DivingStops
import com.stt.android.divecustomization.customization.ui.common.DiveSingleSelectionDialog
import com.stt.android.divecustomization.customization.ui.common.DiveTimePickerDialog
import com.stt.android.divecustomization.customization.ui.common.LoadingView
import com.stt.android.divecustomization.customization.ui.common.ShowMessage
import com.stt.android.divecustomization.customization.ui.divingviews.stops.DivingStopsListItemType.DIVE_DECO_PROFILE
import com.stt.android.divecustomization.customization.ui.divingviews.stops.DivingStopsListItemType.DIVE_LAST_DECO_STOP_DEPTH
import com.stt.android.divecustomization.customization.ui.divingviews.stops.DivingStopsListItemType.DIVE_SAFETY_STOP_TIME

@Composable
fun DiveStopsMainContentView(
    diveSettingsStopsContent: ViewState<DiveCustomizationStopsContent>,
    diveStopsController: DivingStops
) {
    var clickedListItem by rememberSaveable { mutableStateOf<DivingStopsListItemType?>(null) }

    when (diveSettingsStopsContent) {
        is ViewState.Error -> {
            ShowMessage(
                messageStringResource = diveSettingsStopsContent.errorEvent.errorStringRes
            )
        }
        is ViewState.Loaded -> {
            val data = diveSettingsStopsContent.data!!
            DiveSettingsStopsList(
                diveSettingsStopsContent = data,
                onListItemClicked = { itemType ->
                    clickedListItem = itemType
                },
                onDeepStopCheckChange = {
                    diveStopsController.setDeepStopEnabled(it)
                }
            )

            data.diveSafetyStopsTime?.run {
                DiveTimePickerDialog(
                    showDialog = clickedListItem == DIVE_SAFETY_STOP_TIME,
                    title = stringResource(R.string.dive_modes_safety_stop_title),
                    possibleValues = values,
                    selectedValue = selectedValue,
                    onDoneClicked = {
                        diveStopsController.setSafetyStopTime(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.lastDecoStopDepth?.run {
                DiveSingleSelectionDialog(
                    dialogTitle = stringResource(R.string.dive_modes_last_deco_stop_title),
                    availableOptions = optionsList,
                    showDialog = clickedListItem == DIVE_LAST_DECO_STOP_DEPTH,
                    onValueChanged = {
                        diveStopsController.setLastDecoStopDepth(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }

            data.decoProfile?.run {
                DiveSingleSelectionDialog(
                    dialogTitle = stringResource(R.string.dive_modes_deco_profile),
                    availableOptions = optionsList,
                    showDialog = clickedListItem == DIVE_DECO_PROFILE,
                    onValueChanged = {
                        diveStopsController.setDecoProfile(it)
                        clickedListItem = null
                    },
                    onDismiss = {
                        clickedListItem = null
                    }
                )
            }
        }
        is ViewState.Loading -> {
            LoadingView()
        }
    }
}
