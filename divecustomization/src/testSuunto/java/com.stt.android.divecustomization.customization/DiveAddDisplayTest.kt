package com.stt.android.divecustomization.customization

import com.soy.algorithms.divemodecustomization.entities.Mode
import com.stt.android.divecustomization.customization.logic.displays.DiveAddDisplay
import com.stt.android.domain.divecustomization.Success
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class DiveAddDisplayTest : DiveCustomizationTestBase() {

    private lateinit var diveAddDisplay: DiveAddDisplay

    @Before
    override fun setup() = runTest {
        super.setup()
        diveAddDisplay = viewModel

        whenever(
            diveDeviceCreateViewUseCase.createNewView(
                deviceConfig = diveAddDisplay.getDeviceConfig(),
                deviceName = DEVICE_NAME,
                mode = diveAddDisplay.getCurrentMode()
            )
        ).thenReturn(
            Success(newViewResponse)
        )
    }

    @Test
    fun `check if dive layouts flow returns correct values when adding a view`() = runTest {
        val diveLayoutsFlow = diveAddDisplay.getDiveLayoutsFlow(-1).first()
        val response = diveLayoutsFlow.data

        // new view response has 4 layouts
        assertThat(response?.layouts?.size).isEqualTo(4)
        assertThat(response?.layouts?.get(0)?.value).isEqualTo("GraphicDive")
        assertThat(response?.layouts?.get(1)?.value).isEqualTo("GraphicCompass")
        assertThat(response?.layouts?.get(2)?.value).isEqualTo("GraphicTank")
        assertThat(response?.layouts?.get(3)?.value).isEqualTo("GraphicTimer")
    }

    @Test
    fun `adding a view should call validator with correct change`() = runTest {
        // Get a new flow before adding the display
        diveAddDisplay.getNewView(-1)
        diveAddDisplay.addDisplay("GraphicCompass", -1)

        val argumentCaptor = argumentCaptor<Mode>()
        verify(diveDeviceModeUseCase, times(1)).validateCustomizationMode(
            any(),
            argumentCaptor.capture(),
            any()
        )

        // There should be 3 views now with the added one at the third index
        assertThat(argumentCaptor.firstValue.views.view.size).isEqualTo(3)
        assertThat(argumentCaptor.firstValue.views.view[2].layout.value).isEqualTo("GraphicCompass")
    }

    @Test
    fun `check if dive layouts flow returns correct values when changing a view`() = runTest {
        // changing the first view
        val diveLayoutsFlow = diveAddDisplay.getDiveLayoutsFlow(0).first()
        val response = diveLayoutsFlow.data

        // new view response has only 1 layout layouts
        assertThat(response?.layouts?.size).isEqualTo(1)
        assertThat(response?.layouts?.get(0)?.value).isEqualTo("RoundGraphicalDaily")
    }

    @Test
    fun `changing a view should call validator with correct change`() = runTest {
        // passing index of first view to indicate edit mode
        diveAddDisplay.getNewView(0)
        diveAddDisplay.addDisplay("GraphicCompass", 0)

        val argumentCaptor = argumentCaptor<Mode>()
        verify(diveDeviceModeUseCase, times(1)).validateCustomizationMode(
            any(),
            argumentCaptor.capture(),
            any()
        )

        // There should still be just two views since we edited the first view
        assertThat(argumentCaptor.firstValue.views.view.size).isEqualTo(2)
        assertThat(argumentCaptor.firstValue.views.view[0].layout.value).isEqualTo("GraphicCompass")
    }

    @Test
    fun `changing the first view to a different style should remove added views`() = runTest {
        // passing index of first view to indicate edit mode
        diveAddDisplay.getNewView(0)
        diveAddDisplay.addDisplay("ProminentDive", 0)

        val argumentCaptor = argumentCaptor<Mode>()
        verify(diveDeviceModeUseCase, times(1)).validateCustomizationMode(
            any(),
            argumentCaptor.capture(),
            any()
        )

        // Now there should be just one view since rest should have been removed
        assertThat(argumentCaptor.firstValue.views.view.size).isEqualTo(1)
        assertThat(argumentCaptor.firstValue.views.view[0].layout.value).isEqualTo("ProminentDive")
    }
}
