package com.stt.android.data.source.local.menstrualcycle

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_MENSTRUAL_CYCLE
import kotlinx.coroutines.flow.Flow

@Dao
abstract class MenstrualCycleDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(menstrualCycle: LocalMenstrualCycle)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(menstrualCycle: List<LocalMenstrualCycle>)

    @Update
    abstract suspend fun update(menstrualCycles: List<LocalMenstrualCycle>)

    @Update
    abstract suspend fun update(menstrualCycle: LocalMenstrualCycle)

    @Delete
    abstract suspend fun delete(menstrualCycle: LocalMenstrualCycle)

    @Delete
    abstract suspend fun delete(menstrualCycle: List<LocalMenstrualCycle>)

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE startDate <= :endDate 
        AND endDate >= :startDate
        """
    )
    abstract suspend fun fetchInRange(
        startDate: Long,
        endDate: Long
    ): List<LocalMenstrualCycle>

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE menstrualCycleType = :menstrualCycleType
        """
    )
    abstract fun fetchAsFlow(menstrualCycleType: String): Flow<List<LocalMenstrualCycle>>

    @Query(
        """
        DELETE FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE menstrualCycleType = :menstrualCycleType
        """
    )
    abstract suspend fun delete(menstrualCycleType: String)

    @Query(
        """
        DELETE FROM $TABLE_MENSTRUAL_CYCLE
        """
    )
    abstract fun deleteAll()

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE (
            (startDate <= :dateMillis AND endDate >= :dateMillis)
            OR (endDate <= :dateMillis AND (endDate + :endDateRangeMillis) >= :dateMillis)
        ) 
        AND menstrualCycleType = :menstrualCycleType
        """
    )
    abstract suspend fun fetchIncludeDateOrInEndDateRange(
        dateMillis: Long,
        endDateRangeMillis: Long,
        menstrualCycleType: String
    ): List<LocalMenstrualCycle>

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE (
            (startDate <= :dateMillis AND endDate >= :dateMillis) 
            OR (startDate >= :dateMillis AND (startDate - :startDateRangeMillis) <= :dateMillis)
        ) 
        AND menstrualCycleType = :menstrualCycleType
        """
    )
    abstract suspend fun fetchIncludeDateOrInStartDateRange(
        dateMillis: Long,
        startDateRangeMillis: Long,
        menstrualCycleType: String
    ): List<LocalMenstrualCycle>

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE startDate <= :endDateMillis 
        AND menstrualCycleType = :menstrualCycleType 
        ORDER BY startDate
        """
    )
    abstract suspend fun fetchBeforeAt(
        endDateMillis: Long,
        menstrualCycleType: String
    ): List<LocalMenstrualCycle>

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE startDate >= :startDateMillis 
        AND endDate <= :endDateMillis 
        AND menstrualCycleType = :menstrualCycleType
        """
    )
    abstract suspend fun fetchContained(
        startDateMillis: Long,
        endDateMillis: Long,
        menstrualCycleType: String
    ): List<LocalMenstrualCycle>

    @Query(
        """
        SELECT * FROM $TABLE_MENSTRUAL_CYCLE 
        WHERE startDate <= :dateMillis 
        AND endDate >= :dateMillis 
        AND menstrualCycleType = :menstrualCycleType
        """
    )
    abstract suspend fun fetchByDate(
        dateMillis: Long,
        menstrualCycleType: String
    ): List<LocalMenstrualCycle>
}
