package com.stt.android.data.source.local.marketing

import android.app.Application
import android.content.Context
import androidx.core.content.edit
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.data.MARKETING_BANNER_PREFS_NAME_PREFIX
import javax.inject.Inject

class MarketingBannerSharedPrefStorage @Inject constructor(
    private val application: Application,
    private val moshi: <PERSON><PERSON>,
) {
    private val bannerListAdapter: JsonAdapter<List<LocalMarketingBannerInfo>>
        get() = Types.newParameterizedType(List::class.java, LocalMarketingBannerInfo::class.java)
            .let { moshi.adapter<List<LocalMarketingBannerInfo>>(it) }

    fun setMarketingBanners(username: String, banners: List<LocalMarketingBannerInfo>) {
        getPrefByUsername(username).edit {
            putString(KEY_MARKETING_BANNERS, bannerListAdapter.toJson(banners))
        }
    }

    fun getMarketingBanners(username: String): List<LocalMarketingBannerInfo> {
        return getPrefByUsername(username).getString(KEY_MARKETING_BANNERS, null)
            ?.let { bannerListAdapter.fromJson(it) }
            ?: emptyList()
    }

    fun setUpdateMillis(username: String, updateMillis: Long) {
        getPrefByUsername(username).edit { putLong(KEY_UPDATE_MILLIS, updateMillis) }
    }

    fun getUpdateMillis(username: String): Long {
        return getPrefByUsername(username).getLong(KEY_UPDATE_MILLIS, 0)
    }

    fun setHiddenBannerIds(username: String, hiddenBannerIds: Set<String>) {
        getPrefByUsername(username).edit { putStringSet(KEY_HIDDEN_BANNER_IDS, hiddenBannerIds) }
    }

    fun getHiddenBannerIds(username: String): Set<String> {
        return getPrefByUsername(username).getStringSet(KEY_HIDDEN_BANNER_IDS, null) ?: emptySet()
    }

    private fun getPrefByUsername(username: String) = application.getSharedPreferences(
        "$MARKETING_BANNER_PREFS_NAME_PREFIX$username",
        Context.MODE_PRIVATE,
    )

    companion object {
        private const val KEY_MARKETING_BANNERS = "key_marketing_banners"
        private const val KEY_HIDDEN_BANNER_IDS = "key_hidden_banner_ids"
        private const val KEY_UPDATE_MILLIS = "key_update_millis"
    }
}
