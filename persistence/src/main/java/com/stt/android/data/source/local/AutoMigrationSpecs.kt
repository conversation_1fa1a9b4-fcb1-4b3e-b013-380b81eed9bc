package com.stt.android.data.source.local

import androidx.room.DeleteColumn
import androidx.room.RenameColumn
import androidx.room.migration.AutoMigrationSpec

class AutoMigrationSpecs {
    @RenameColumn(tableName = TABLE_USER_TAG, fromColumnName = "id", toColumnName = "userTagId")
    @RenameColumn(tableName = TABLE_USER_TAG, fromColumnName = "key", toColumnName = "userTagKey")
    @DeleteColumn(tableName = TABLE_USER_TAG, columnName = "lastAddedDate")
    @DeleteColumn(tableName = TABLE_USER_TAG, columnName = "lastModifiedDate")
    class AutoMigrationSpecFom49To50Spec : AutoMigrationSpec

    @RenameColumn(tableName = TABLE_MENSTRUAL_CYCLE, fromColumnName = "mcType", toColumnName = "menstrualCycleType")
    class AutoMigrationSpecFom74To75Spec : AutoMigrationSpec

    @RenameColumn(tableName = TABLE_SONGS, fromColumnName = "id", toColumnName = "index")
    class AutoMigrationSpecFom81To82Spec : AutoMigrationSpec

    @DeleteColumn(tableName = TABLE_SUMMARY_EXTENSION, columnName = "lacticThHr")
    @DeleteColumn(tableName = TABLE_SUMMARY_EXTENSION, columnName = "lacticThPace")
    class AutoMigrationSpecFom90To91Spec : AutoMigrationSpec

    @DeleteColumn(tableName = TABLE_SUMMARY_EXTENSION, columnName = "competitionResult")
    @DeleteColumn(tableName = TABLE_WORKOUT_HEADERS, columnName = "competition_result")
    class AutoMigrationSpecFom92To93Spec : AutoMigrationSpec
}
