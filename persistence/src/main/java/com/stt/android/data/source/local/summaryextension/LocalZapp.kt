package com.stt.android.data.source.local.summaryextension

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class LocalZapp(
    @Json(name = "id") val id: String?,
    @<PERSON><PERSON>(name = "name") val name: String,
    @<PERSON><PERSON>(name = "authorId") val authorId: String?,
    @Json(name = "externalId") val externalId: String?,
    @Json(name = "summaryOutputs") val summaryOutputs: List<LocalZappSummaryOutput>?,
    @<PERSON><PERSON>(name = "channels") val channels: List<LocalZappChannel>?
)

@JsonClass(generateAdapter = true)
data class LocalZappSummaryOutput(
    @Json(name = "format")
    val format: String?,
    @Json(name = "id")
    val id: String?,
    @<PERSON><PERSON>(name = "name")
    val name: String,
    @<PERSON><PERSON>(name = "postfix")
    val postfix: String?,
    @<PERSON><PERSON>(name = "summaryValue")
    val summaryValue: Double
)

@JsonClass(generateAdapter = true)
data class LocalZappChannel(
    @<PERSON><PERSON>(name = "channelId") val channelId: Int,
    @<PERSON><PERSON>(name = "format") val format: String?,
    @Json(name = "inverted") val inverted: Boolean?,
    @Json(name = "name") val name: String,
    @Json(name = "variableId") val variableId: String
)
