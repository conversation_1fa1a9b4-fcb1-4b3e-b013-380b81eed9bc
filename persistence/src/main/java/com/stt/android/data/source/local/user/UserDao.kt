package com.stt.android.data.source.local.user

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_USER

@Dao
abstract class UserDao {
    @Query(
        """
        SELECT *
        FROM $TABLE_USER
        WHERE id = :id
        """
    )
    abstract suspend fun findById(id: Int): LocalUser?

    @Query(
        """
        SELECT *
        FROM $TABLE_USER
        WHERE username = :username
        """
    )
    abstract suspend fun findByUsername(username: String): LocalUser?

    @Query(
        """
        SELECT *
        FROM $TABLE_USER
        WHERE username IN (:usernames)
        """
    )
    abstract suspend fun findByUsernames(usernames: Set<String>): List<LocalUser>

    @Query(
        """
        SELECT *
        FROM $TABLE_USER
        WHERE id = $CURRENT_USER_DEFAULT_ID
        """
    )
    abstract suspend fun findCurrentUser(): LocalUser?

    @Query(
        """
            DELETE
            FROM $TABLE_USER
            WHERE id != $CURRENT_USER_DEFAULT_ID
        """
    )
    abstract suspend fun deleteOtherUsers()

    @Query(
        """
            DELETE
            FROM $TABLE_USER
        """
    )
    abstract fun deleteAll()

    @Query(
        """
            SELECT *
            FROM $TABLE_USER
            WHERE id != $CURRENT_USER_DEFAULT_ID
        """
    )
    abstract suspend fun getAllOtherUsers(): List<LocalUser>

    @Query(
        """
            SELECT username
            FROM $TABLE_USER
            WHERE id != $CURRENT_USER_DEFAULT_ID
        """
    )
    abstract suspend fun getAllOtherUsernames(): List<String>

    @Insert
    abstract suspend fun insert(users: List<LocalUser>)

    @Insert
    abstract suspend fun insert(user: LocalUser)

    @Update
    abstract suspend fun update(user: LocalUser)

    @Transaction
    open suspend fun upsert(user: LocalUser) {
        if (findById(user.id) == null) {
            insert(user)
        } else {
            update(user)
        }
    }

    // Insert or update a list of users in a single transaction.
    // Used for database migration from Ormlite to Room.
    @Transaction
    open suspend fun upsert(users: List<LocalUser>) {
        for (user in users) {
            if (findById(user.id) == null) {
                insert(user)
            } else {
                update(user)
            }
        }
    }

    @Transaction
    open suspend fun replaceOtherUsers(users: List<LocalUser>) {
        deleteOtherUsers()
        insert(users)
    }

    companion object {
        const val CURRENT_USER_DEFAULT_ID = 1
    }
}
