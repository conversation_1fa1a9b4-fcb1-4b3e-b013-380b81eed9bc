package com.stt.android.data.source.local.workouts

import android.content.SharedPreferences
import androidx.core.content.edit
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

const val KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP = "CURRENT_USER_WORKOUTS_LAST_MODIFIED"
const val KEY_FOLLOWEES_WORKOUTS_LAST_MODIFIED_TIMESTAMP = "FOLLOWEES_WORKOUTS_LAST_MODIFIED"
const val DEFAULT_SINCE_TIMESTAMP = 0L

class WorkoutsLastFetchTimestampStore
@Inject constructor(
    private val sharedPreferences: SharedPreferences
) {
    suspend fun getLastFetchTimestampForOwnWorkouts(): Long = withContext(IO) {
        sharedPreferences.getLong(KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP, DEFAULT_SINCE_TIMESTAMP)
    }

    fun setLastFetchTimestampForOwnWorkouts(timestamp: Long) {
        sharedPreferences.edit {
            putLong(KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP, timestamp)
        }
    }

    suspend fun getLastFetchTimestampForFolloweesWorkouts(): Long = withContext(IO) {
        sharedPreferences.getLong(KEY_FOLLOWEES_WORKOUTS_LAST_MODIFIED_TIMESTAMP, DEFAULT_SINCE_TIMESTAMP)
    }

    fun setLastFetchTimestampForFolloweesWorkouts(timestamp: Long) {
        sharedPreferences.edit {
            putLong(KEY_FOLLOWEES_WORKOUTS_LAST_MODIFIED_TIMESTAMP, timestamp)
        }
    }

    fun clearOwnWorkoutsFetchedTimestamp() =
        sharedPreferences.edit {
            remove(KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP)
        }

    fun clearFolloweesWorkoutsFetchedTimestamp() =
        sharedPreferences.edit {
            remove(KEY_FOLLOWEES_WORKOUTS_LAST_MODIFIED_TIMESTAMP)
        }
}
