package com.stt.android.data.source.local.gear

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.stt.android.data.source.local.TABLE_GEAR

@Entity(tableName = TABLE_GEAR)
data class LocalGear(
    @ColumnInfo(name = SERIAL_NUMBER) @PrimaryKey val serialNumber: String,
    @ColumnInfo(name = MANUFACTURER) val manufacturer: String,
    @ColumnInfo(name = NAME) val name: String,
    @ColumnInfo(name = SOFTWARE_VERSION) val softwareVersion: String?,
    @ColumnInfo(name = HARDWARE_VERSION) val hardwareVersion: String?,
    @ColumnInfo(name = LAST_SYNC_DATE) val lastSyncDate: Long?,
    @ColumnInfo(name = FIRST_SYNC_DATE) val firstSyncDate: Long?
) {
    companion object {
        const val SERIAL_NUMBER = "serialNumber"
        const val MANUFACTURER = "manufacturer"
        const val NAME = "name"
        const val SOFTWARE_VERSION = "softwareVersion"
        const val HARDWARE_VERSION = "hardwareVersion"
        const val LAST_SYNC_DATE = "lastSyncDate"
        const val FIRST_SYNC_DATE = "firstSyncDate"
    }
}
