package com.stt.android.maps.amap.delegate

import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptor
import com.stt.android.maps.amap.toAMap
import com.stt.android.maps.amap.toAMapRotation
import com.stt.android.maps.amap.toGoogle
import com.stt.android.maps.delegate.MarkerDelegate

class AMapMarkerDelegate(private val marker: Marker) : MarkerDelegate {

    override fun getAlpha(): Float = marker.alpha

    override fun getPosition(): com.google.android.gms.maps.model.LatLng {
        val amapLatLng = LatLng(marker.position.latitude, marker.position.longitude)
        return amapLatLng.toGoogle()
    }

    override fun setAlpha(alpha: Float) {
        marker.alpha = alpha
    }

    override fun setIcon(iconDescriptor: SuuntoBitmapDescriptor) {
        marker.setIcon(iconDescriptor.toAMap())
    }

    override fun setIconScale(scale: Float) {
        // Not supported
    }

    override fun setPosition(latLng: com.google.android.gms.maps.model.LatLng) {
        marker.position = latLng.toAMap()
    }

    override fun setZPriority(priority: MarkerZPriority) {
        marker.zIndex = priority.zIndex
    }

    override fun setRotation(rotation: Float) {
        marker.rotateAngle = rotation.toAMapRotation()
    }

    override fun isVisible(): Boolean = marker.isVisible

    override fun remove() {
        marker.remove()
    }

    override fun setVisible(visible: Boolean) {
        marker.isVisible = visible
    }
}
