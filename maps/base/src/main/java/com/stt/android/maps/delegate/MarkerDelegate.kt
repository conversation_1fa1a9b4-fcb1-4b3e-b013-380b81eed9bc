package com.stt.android.maps.delegate

import com.google.android.gms.maps.model.LatLng
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptor

interface MarkerDelegate : AnnotationDelegate {

    fun getAlpha(): Float

    fun getPosition(): LatLng?

    fun setAlpha(alpha: Float)

    fun setIcon(iconDescriptor: SuuntoBitmapDescriptor)

    fun setIconScale(scale: Float)

    fun setPosition(latLng: LatLng)

    fun setZPriority(priority: MarkerZPriority)

    fun setRotation(rotation: Float)
}
