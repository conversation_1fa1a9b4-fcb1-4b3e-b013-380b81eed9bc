package com.stt.android.maps.location

import android.Manifest.permission.ACCESS_COARSE_LOCATION
import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Looper
import androidx.annotation.RequiresPermission
import timber.log.Timber
import java.util.Collections

/**
 * Location source using Android LocationManager.
 */
class AndroidLocationSource(
    private val locationManager: LocationManager
) : SuuntoLocationSource {

    private val listenerMap =
        Collections.synchronizedMap(mutableMapOf<SuuntoLocationListener, LocationListener>())

    @RequiresPermission(anyOf = [ ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION ])
    override fun getLastKnownLocation(callback: SuuntoLocationCallback) {
        val location = try {
            locationManager.getProviders(true)
                .asSequence()
                .map { locationManager.getLastKnownLocation(it) }
                .filterNotNull()
                .sortedByDescending { it.time }
                .firstOrNull()
        } catch (e: Exception) {
            callback.onFailure(e)
            return
        }

        if (location != null) {
            callback.onSuccess(location)
        } else {
            callback.onFailure(LocationNotAvailableException())
        }
    }

    @RequiresPermission(anyOf = [ ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION ])
    override fun requestLocationUpdates(request: SuuntoLocationRequest, listener: SuuntoLocationListener, looper: Looper) {
        removeLocationUpdates(listener)

        val providers = getFusedProviders(request.priority, getBestProvider(request.priority))
        val locationListener =
            createLocationListener(providers, listener, this@AndroidLocationSource)
        for (provider in providers) {
            try {
                locationManager.requestLocationUpdates(
                    provider,
                    request.interval,
                    request.smallestDisplacement,
                    locationListener,
                    looper
                )
            } catch (e: Exception) {
                Timber.w(e, "Failed to request location updates from provider $provider")
            }
        }
        listenerMap[listener] = locationListener
    }

    @RequiresPermission(anyOf = [ ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION ])
    override fun removeLocationUpdates(listener: SuuntoLocationListener) {
        listenerMap.remove(listener)?.let {
            locationManager.removeUpdates(it)
        }
    }

    private fun createLocationListener(
        providers: List<String>,
        suuntoLocationListener: SuuntoLocationListener,
        source: SuuntoLocationSource
    ): LocationListener {
        return object : LocationListener {
            override fun onLocationChanged(location: Location) {
                suuntoLocationListener.onLocationChanged(location, source)
            }

            @Deprecated("Deprecated in Java")
            override fun onStatusChanged(provider: String, status: Int, extras: Bundle?) {
                // No action
            }

            override fun onProviderEnabled(provider: String) {
                suuntoLocationListener.onLocationAvailability(isLocationEnabled(), source)
            }

            override fun onProviderDisabled(provider: String) {
                suuntoLocationListener.onLocationAvailability(isLocationEnabled(), source)
            }

            private fun isLocationEnabled() = providers.any { locationManager.isProviderEnabled(it) }
        }
    }

    private fun getBestProvider(priority: Int) =
        when (priority) {
            SuuntoLocationRequest.PRIORITY_NO_POWER -> LocationManager.PASSIVE_PROVIDER
            else -> locationManager.getBestProvider(getCriteria(priority), false)
        } ?: LocationManager.PASSIVE_PROVIDER

    private fun getFusedProviders(priority: Int, bestProvider: String): List<String> {
        return if ((
                priority == SuuntoLocationRequest.PRIORITY_HIGH_ACCURACY ||
                    priority == SuuntoLocationRequest.PRIORITY_BALANCED_POWER_ACCURACY
                ) &&
            bestProvider == LocationManager.GPS_PROVIDER &&
            locationManager.getProviders(false).contains(LocationManager.NETWORK_PROVIDER)
        ) {
            listOf(bestProvider, LocationManager.NETWORK_PROVIDER)
        } else {
            listOf(bestProvider)
        }
    }

    companion object {
        private fun getCriteria(priority: Int) = Criteria().apply {
            accuracy = priorityToAccuracy(priority)
            powerRequirement = priorityToPowerRequirement(priority)
            isCostAllowed = true
        }

        private fun priorityToAccuracy(priority: Int) =
            when (priority) {
                SuuntoLocationRequest.PRIORITY_HIGH_ACCURACY,
                SuuntoLocationRequest.PRIORITY_BALANCED_POWER_ACCURACY -> Criteria.ACCURACY_FINE
                else -> Criteria.ACCURACY_COARSE
            }

        private fun priorityToPowerRequirement(priority: Int) =
            when (priority) {
                SuuntoLocationRequest.PRIORITY_HIGH_ACCURACY -> Criteria.POWER_HIGH
                SuuntoLocationRequest.PRIORITY_BALANCED_POWER_ACCURACY -> Criteria.POWER_MEDIUM
                else -> Criteria.POWER_LOW
            }
    }
}
