<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.stt.android.mapssample">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity
            android:label="@string/app_name"
            android:exported="true"
            android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".DualMapActivity"
            android:parentActivityName=".MainActivity"
            android:label="@string/dual_map" />

        <activity
            android:name=".LocationActivity"
            android:parentActivityName=".MainActivity"
            android:label="@string/map_with_location" />

        <activity
            android:name=".OverlayActivity"
            android:parentActivityName=".MainActivity"
            android:label="@string/map_with_overlay" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_map_v2_key"/>

    </application>

</manifest>
