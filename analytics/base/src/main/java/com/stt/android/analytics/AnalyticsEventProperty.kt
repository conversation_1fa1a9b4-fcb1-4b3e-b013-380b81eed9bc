package com.stt.android.analytics

/**
 * Constants for analytics event properties.
 *
 * Event properties are attributes of a particular event and reflect the state at which the event
 * was triggered.
 *
 * Do not add user properties here. They belong to [AnalyticsUserProperty].
 */
object AnalyticsEventProperty {
    const val TYPE: String = "Type"
    const val RESULT: String = "Result"
    const val ERROR_TYPE: String = "ErrorType"
    const val ERROR_CODE: String = "ErrorCode"
    const val ERROR_CAUSE: String = "ErrorCause"
    const val ERROR_STEP: String = "ErrorStep"
    const val ERROR_REASON: String = "ErrorReason"
    const val STACK_TRACE: String = "StackTrace"
    const val ACTIVITY_TYPE: String = "ActivityType"
    const val VISIBILITY: String = "Visibility"
    const val PROFILE_TYPE: String = "ProfileType"
    const val WORKOUT_KEY: String = "WorkoutKey"
    const val WORKOUT_DURATION: String = "Duration"
    const val WORKOUT_PAUSE_DURATION: String = "PauseDuration"
    const val WORKOUT_DISTANCE: String = "Distance"
    const val HAS_DESCRIPTION: String = "HasDescription"
    const val TARGET: String = "Target"
    const val NR_OF_PHOTOS: String = "Photos"
    const val NR_OF_LIKES: String = "Likes"
    const val FOLLOWING_EACH_OTHER: String = "FollowingEachOther"
    const val FOLLOWING_TARGET: String = "FollowingTarget"
    const val FOLLOWER_TARGET: String = "FollowedByTarget"
    const val TARGET_FOLLOW_YOU: String = "TargetFollowsYou"
    const val FOLLOWER_ACCOUNT_TYPE: String = "FollowerAccountType"
    const val NO_RELATIONSHIP: String = "NoRelationship"
    const val SELF: String = "Self"
    const val ROUTES_MODES: String = "RoutingModesUsed"
    const val ROUTES_TYPE: String = "RouteType"
    const val ROUTES_POINTS: String = "RoutePoints"
    const val ROUTES_UNDO_COUNT: String = "UndoCount"
    const val ROUTES_PLANNING_POINTS_ADDED_MANUALLY: String = "PlanningPointsAddedManually"
    const val ROUTES_PLANNING_POINTS_MOVED_MANUALLY: String = "PlanningPointsMovedManually"
    const val ROUTE_IMPORT_SOURCE: String = "ImportSource"
    const val ROUTES_OWNER: String = "Route"
    const val ROUTE_FROM_ACTIVITY: String = "RouteFromActivity"
    const val IMPORTED_ROUTE: String = "ImportedRoute"
    const val EXISTING_ROUTES_AMOUNT: String = "ExistingRoutesAmount"
    const val SPEED: String = "Speed"
    const val DISTANCE_IN_METERS: String = "DistanceInMeters"
    const val DURATION_IN_SECONDS: String = "DurationInSeconds"
    const val DURATION_IN_MINUTES: String = "DurationInMinutes"
    const val WORKOUT_START_TIME: String = "WorkoutStartTime"
    const val WORKOUT_SYNC_DELAY_IN_MINUTES = "WorkoutSyncDelayInMinutes"
    const val ACTIVITY_TYPES: String = "ActivityTypes"
    const val ACTIVITY_TYPE_COUNT: String = "ActivityTypeCount"
    const val DAYS_SINCE_CREATED: String = "DaysSinceCreated"
    const val MINUTES_SINCE_ACTIVITY_ENDED: String = "MinutesSinceActivityEnded"
    const val OUTCOME: String = "Outcome"
    const val SYNC_DURATION: String = "SyncDuration"
    const val DATA_TYPE: String = "DataType"
    const val DATA_DATE: String = "DataDate"
    const val MANUALLY_ADDED: String = "ManuallyAdded"
    const val DAYS_SINCE_FIRST_SESSION: String = "DaysSinceFirstSession"
    const val HARDWARE_USED: String = "HardwareUsed"
    const val NUM_PHOTOS: String = "NumberOfPhotos"
    const val NUM_LIKES: String = "NumberOfLikes"
    const val NUM_PHOTOS_ADDED: String = "NumberOfPhotosAdded"
    const val NUM_VIDEOS_ADDED: String = "NumberOfVideosAdded"
    const val NUM_COMMENTS: String = "NumberOfComments"
    const val NUM_ACTIVITIES: String = "NumberOfActivities"
    const val NUM_WORKOUT_PLANS: String = "NumberOfCurrentPlans"
    const val PLAN_TYPE: String = "PlanType"
    const val PLANNER_STEP: String = "Step"
    const val PLANNER_SPECIFIC_DATE: String = "SpecificDate"
    const val PLANNER_DESCRIPTION_ADDED: String = "DescriptionAdded"
    const val LOCATION_ADDED: String = "LocationAdded"
    const val SOURCE: String = "Source"
    const val DESCRIPTION_ADDED: String = "DescriptionAdded"
    const val CHANGES_COUNT: String = "ChangesCount"
    const val FIELDS_CHANGED: String = "FieldsChanged"
    const val NEW_TSS_METHOD: String = "NewTSSMethod"
    const val CONTEXT: String = "Context"
    const val TARGET_WORKOUT_VISIBILITY: String = "TargetWorkoutVisibility"
    const val ELEVATION_GRAPH_SCRUBBING: String = "ElevationGraphScrubbing"
    const val TARGET_RELATIONSHIP: String = "TargetRelationship"
    const val TARGET_ACCOUNT_TYPE: String = "TargetAccountType"
    const val NEW_NOTIFICATIONS: String = "NewNotifications"
    const val MOOD: String = "Mood"
    const val RECOVERY_TIME: String = "RecoveryTime"
    const val HR_AVERAGE: String = "HRAverage"
    const val HR_MAX: String = "HRMax"
    const val TOTAL: String = "Total"
    const val TOTAL_ASCENT: String = "TotalAscent"
    const val TOTAL_DESCENT: String = "TotalDescent"
    const val TOTAL_DISTANCE: String = "TotalDistance"
    const val TOTAL_DURATION: String = "TotalDuration"
    const val ACTIVITY_DURATION: String = "ActivityDuration"
    const val ACTIVITY_DISTANCE: String = "ActivityDistance"
    const val CALL_TO_ACTION: String = "CallToAction"
    const val REACTION: String = "Reaction"
    const val LAST_SCREEN: String = "LastScreen"
    const val NAVIGATION_METHOD: String = "NavigationMethod"
    const val LOGIN_METHOD: String = "LogInMethod"
    const val SIGNUP_METHOD: String = "SignUpMethod"
    const val TERMS_VERSION: String = "TermsVersion"
    const val TERMS_CONTEXT: String = "TermsContext"
    const val SHARE_CUSTOMIZED: String = "ShareCustomized"
    const val PHOTO_SIZE: String = "PhotoSize"
    const val GRAPH_TYPE: String = "GraphType"
    const val NEW_VIEW: String = "NewView"
    const val STORED_VALUES_EXIST: String = "StoredValuesExist"
    const val STAT1_TYPE: String = "Stat1Type"
    const val STAT2_TYPE: String = "Stat2Type"
    const val STAT3_TYPE: String = "Stat3Type"
    const val STAT4_TYPE: String = "Stat4Type"
    const val SUBSCRIPTION_TYPE: String = "SubscriptionType"
    const val ARTICLE: String = "Article"
    const val TRIGGER: String = "Trigger"
    const val BUTTON: String = "Button"
    const val FILE_FORMAT: String = "FileFormat"
    const val EMPTY_WORKOUT_LIST: String = "EmptyWorkoutList"
    const val METHOD: String = "Method"
    const val WORKOUTS_ON_THIS_ROUTE: String = "WorkoutsOnThisRoute"
    const val DISTANCE_FROM_START_TO_END: String = "DistanceFromStartToEnd"
    const val BLOCKED_USER_ID: String = "BlockedUserID"
    const val UNBLOCKED_USER_ID: String = "UnBlockedUserID"
    const val NEW_VALUE: String = "NewValue"
    const val VALUE_NAME: String = "ValueName"
    const val FIELD_CHANGED: String = "FieldChanged"
    const val AMOUNT_OF_EVENTS: String = "AmountOfEvents"
    const val PERMISSION_TYPE: String = "PermissionType"
    const val PERMISSION_RESULT: String = "Result"
    const val CUSTOMIZED_NAME_GIVEN: String = "CustomizedNameGiven"
    const val MULTI_WAYPOINT: String = "MultiWaypoint"
    const val TYPE_EDITED: String = "TypeEdited"
    const val ORIGINAL_TYPE: String = "OriginalType"
    const val NEW_TYPE: String = "NewType"
    const val NAME_EDITED: String = "NameEdited"
    const val WAYPOINTS: String = "Waypoints"
    const val CALENDAR_GRANULARITY: String = "Granularity"
    const val BUTTON_USED: String = "ButtonUsed"
    const val CATEGORY: String = "Category"
    const val US_STATE_CHOSEN_CONTEXT: String = "Context"
    const val US_STATE_OPTION_CHOSEN: String = "OptionChosen"
    const val POSITION_IN_MULTISPORT_WORKOUT: String = "PositionInMultisportWorkout"
    const val NUMBER_OF_ACHIEVEMENTS: String = "NumberOfAchievements"
    const val TRAVERSE_SHOTS_OR_FISHES_CAUGHT_COUNT: String = "TraverseShotsOrFishesCaughtCount"
    const val PROFILE_PICTURE_ALREADY_EXISTS: String = "ProfilePictureAlreadyExists"
    const val PROFILE_PICTURE_EDITED_NEW_OR_CHANGED: String = "NewOrChanged"
    const val EXPORT_TYPE: String = "ExportType"
    const val VIDEO: String = "Video"
    const val PUBLIC_WORKOUT_TAGS: String = "PublicWorkoutTags"
    const val CUSTOM_TAGS_ADDED: String = "CustomTagsAdded"

    const val SUUNTO_FIRST_ATTEMPT: String = "FirstAttempt"
    const val SUUNTO_WATCH_MODEL: String = "WatchModel"
    const val SUUNTO_WATCH_FIRMWARE_VERSION: String = "WatchFirmwareVersion"
    const val SUUNTO_WATCH_SERIAL_NUMBER: String = "WatchSerialNumber"
    const val SUUNTO_WATCH_VARIANT: String = "WatchVariant"
    const val SUUNTO_WATCH_DAYS_OF_OWNERSHIP: String = "DaysOfOwnership"
    const val SUUNTO_SOFTWARE_VERSION: String = "SoftwareVersion"
    const val SUUNTO_HARDWARE_VERSION: String = "HardwareVersion"
    const val SUUNTO_BOOTLOADER_VERSION: String = "BootloaderVersion"
    const val SUUNTO_SYNC_DURATION: String = "SyncDuration"
    const val SUUNTO_LOCATION_PROVIDER: String = "LocationProvider"
    const val SUUNTO_BLUETOOTH_SETTING: String = "DeviceBluetoothSetting"
    const val SUUNTO_LOCATION_PERMISSION: String = "LocationPermissionGiven"
    const val SUUNTO_CONNECT_ERROR_TYPE: String = "ErrorType"
    const val SUUNTO_CONNECT_REASON: String = "ConnectReason"
    const val SUUNTO_CONNECT_ATTEMPT_NR: String = "AttemptNr"
    const val SUUNTO_CONNECT_ATTEMPT_DURATION: String = "AttemptDuration"
    const val SUUNTO_CONNECT_ERROR_MESSAGE: String = "ErrorMessage"
    const val SUUNTO_CONNECT_DEVICE_ADVERTISED_PAIRING_STATE: String =
        "DeviceAdvertisedPairingState"
    const val SUUNTO_CONNECT_PHONE_PAIRING_STATE_BEFORE_CONNECT: String =
        "PhonePairingStateBeforeConnect"
    const val SUUNTO_CONNECT_PHONE_PAIRING_STATE_AFTER_CONNECT: String =
        "PhonePairingStateAfterConnect"
    const val SUUNTO_CONNECT_ALREADY_CONNECTED_WHEN_CONNECTING: String =
        "AlreadyConnectedWhenConnecting"
    const val SUUNTO_CONNECT_MINUTES_FROM_COUNTER_RESET: String = "MinutesFromCounterReset"
    const val SUUNTO_CONNECT_COUNTER_RESET_REASON: String = "CounterResetReason"
    const val SUUNTO_CONNECT_CONNECT_FAILS_COUNTED: String = "FailsCountSinceReset"
    const val SUUNTO_CONNECT_CONNECT_SUCCESSES_COUNTED: String = "SuccessCountSinceReset"
    const val SUUNTO_CONNECT_INVALID_PACKETS_DETECTED: String = "InvalidPacketsDetected"
    const val SUUNTO_GOAL_NEW_SETTING: String = "NewSetting"
    const val SUUNTO_GOAL_TYPE: String = "GoalType"
    const val SUUNTO_GOAL_NEW: String = "NewGoal"
    const val SUUNTO_GOAL_OLD: String = "OldGoal"
    const val SUUNTO_CURRENTLY_CONNECTED: String = "CurrentlyConnected"
    const val SUUNTO_CURRENTLY_CONNECTED_TO: String = "CurrentlyConnectedTo"
    const val SUUNTO_CONNECTED_SERVICE: String = "Service"
    const val SUUNTO_CONNECTED_SERVICE_ERROR_TYPE: String = "ErrorType"
    const val SUUNTO_SYSTEM_EVENT_RESULT_CODE: String = "ResultCode"
    const val SUUNTO_DIARY_GRAPH_GRANULARITY: String = "Granularity"
    const val SUUNTO_DIARY_GRAPH_TIME_RANGE: String = "TimeRange"
    const val SUUNTO_PLUS_APP_AUTHORS: String = "SuuntoPlusAppAuthors"
    const val SUUNTO_PREVIOUS_FIRMWARE_VERSION: String = "PreviousFirmwareVersion"
    const val SUUNTO_NEW_FIRMWARE_VERSION: String = "NewFirmwareVersion"
    const val SUUNTO_COMMAND_TYPE: String = "CommandType"

    // dive
    const val SUUNTO_DIVE_BATTERY_CHARGE_AT_START: String = "BatteryChargeAtStart"
    const val SUUNTO_DIVE_BATTERY_VOLTAGE_AT_START: String = "BatteryVoltageAtStart"
    const val SUUNTO_DIVE_BATTERY_CHARGE_AT_END: String = "BatteryChargeAtEnd"
    const val SUUNTO_DIVE_BATTERY_VOLTAGE_AT_END: String = "BatteryVoltageAtEnd"
    const val SUUNTO_DIVE_DAYS_IN_SERIES: String = "DaysInSeries"
    const val SUUNTO_DIVE_NUMBER_IN_SERIES: String = "NumberInSeries"
    const val SUUNTO_DIVE_ALGORITHM: String = "Algorithm"
    const val SUUNTO_DIVE_SURFACE_PRESSURE: String = "SurfacePressure"
    const val SUUNTO_DIVE_MINGF: String = "MinGF"
    const val SUUNTO_DIVE_MAXGF: String = "MaxGF"
    const val SUUNTO_DIVE_CONSERVATISM: String = "Conservatism"
    const val SUUNTO_DIVE_ALTITUDE: String = "Altitude"
    const val SUUNTO_DIVE_DEEPSTOP_ENABLED: String = "DeepstopEnabled"
    const val SUUNTO_DIVE_SAFETYSTOP_TIME: String = "SafetystopTime"
    const val SUUNTO_DIVE_LAST_DECO_STOP_DEPTH: String = "LastDecoStopDepth"
    const val SUUNTO_DIVE_ASCENT_MODE: String = "AscentMode"
    const val SUUNTO_DIVE_PREVIOUS_DIVE_DEPTH: String = "PreviousDiveDepth"
    const val SUUNTO_DIVE_TISSUE_START_CNS: String = "TissueStartCNS"
    const val SUUNTO_DIVE_TISSUE_START_OTU: String = "TissueStartOTU"
    const val SUUNTO_DIVE_TISSUE_START_OLF: String = "TissueStartOLF"
    const val SUUNTO_DIVE_TISSUE_END_CNS: String = "TissueEndCNS"
    const val SUUNTO_DIVE_TISSUE_END_OTU: String = "TissueEndOTU"
    const val SUUNTO_DIVE_TISSUE_END_OLF: String = "TissueEndOLF"
    const val SUUNTO_DIVE_DIVE_MODE: String = "DiveMode"
    const val SUUNTO_DIVE_ASCENT_TIME: String = "AscentTime"
    const val SUUNTO_DIVE_BOTTOM_TIME: String = "BottomTime"
    const val SUUNTO_DIVE_BOTTOM_MIXTURE_OXYGEN: String = "BottomMixtureOxygen"
    const val SUUNTO_DIVE_BOTTOM_MIXTURE_HELIUM: String = "BottomMixtureHelium"
    const val SUUNTO_DIVE_DESATURATION_TIME: String = "DesaturationTime"
    const val SUUNTO_DIVE_GAS_COUNT: String = "GasCount"
    const val SUUNTO_DIVE_GAS_WITH_TANKPOD_COUNT: String = "GasWithTankpodCount"
    const val SUUNTO_DIVE_DEPTH_MAX: String = "DepthMax"
    const val SUUNTO_DIVE_DEPTH_AVG: String = "DepthAvg"
    const val SUUNTO_DIVE_VENTILATION_AVG: String = "VentilationAvg"
    const val SUUNTO_DIVE_TISSUE_RESET: String = "TissueReset"
    const val SUUNTO_DIVE_EVENTS_ERROR_COUNT: String = "EventsErrorCount"
    const val SUUNTO_DIVE_EVENTS_GAS_SWITCH_COUNT: String = "EventsGasSwitchCount"
    const val SUUNTO_DIVE_AUTOMATIC_LOCATION_ADDED: String = "AutomaticLocationAdded"
    const val SUUNTO_DIVE_EVENT_COUNT_ERROR_CEILING_BROKEN: String = "ErrorCountCeilingBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_SAFETY_STOP_BROKEN: String =
        "AlarmCountSafetyStopBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_ASCENT_SPEED: String = "AlarmCountAscentSpeed"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_DILUENT_HYPEROXIA: String = "AlarmCountDiluentHyperoxia"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_DEEP_STOP_VIOLATION: String =
        "AlarmCountDeepStopViolation"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_CEILING_BROKEN: String = "AlarmCountCeilingBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_HIGH: String = "AlarmCountPO2High"
    const val SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_LOW: String = "AlarmCountPO2Low"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_SAFETY_STOP_BROKEN: String =
        "WarningCountSafetyStopBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_BROKEN: String =
        "WarningCountDeepStopBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_CEILING_BROKEN: String = "WarningCountCeilingBroken"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_PO2_HIGH: String = "WarningCountPO2High"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_MISSED_DECO: String = "WarningCountMissedDeco"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_MINI_LOCK: String = "WarningCountMiniLock"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_ICD: String = "WarningCountICD"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_PENALTY: String =
        "WarningCountDeepStopPenalty"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_MANDATORY_SAFETY_STOP: String =
        "WarningCountMandatorySafetyStop"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU250: String = "WarningCountOTU250"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU300: String = "WarningCountOTU300"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS80: String = "WarningCountCNS80"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS100: String = "WarningCountCNS100"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_MAX_DEPTH: String = "WarningCountMaxDepth"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_AIR_TIME: String = "WarningCountAirTime"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_TANK_PRESSURE: String = "WarningCountTankPressure"
    const val SUUNTO_DIVE_EVENT_COUNT_WARNING_CCRO2_TANK_PRESSURE: String =
        "WarningCountCCRO2TankPressure"
    const val SUUNTO_DIVE_EVENT_COUNT_NOTIFY_DECO_BROKEN_ACKNOWLEDGED: String =
        "NotificationCountDecoBrokenAcknowledged"

    const val UNREAD_MESSAGES: String = "UnreadMessages"

    // Sport modes
    const val LOADING_TIME_PER_MODE: String = "LoadingTimePerMode"
    const val SPORT_MODE_TYPE: String = "SportModeType"
    const val SPORT_MODE_SYNC_TYPE: String = "SyncType"
    const val SPORT_MODE_DISPLAY_NAVIGATION_TYPE: String = "SportModeDisplayNavigationType"
    const val SPORT_MODE_TOTAL_DISPLAYS: String = "TotalDisplays"
    const val SPORT_MODE_DISPLAY_TYPE: String = "Display%dType"
    const val SPORT_MODE_DISPLAY_TYPE_WITHOUT_INDEX: String = "DisplayType"
    const val SPORT_MODE_DISPLAY_FIELDS: String = "Display%dFields"
    const val SPORT_MODE_MULTISPORT_ORDER: String = "MultiSportOrder"

    const val SUUNTO_ROUTES_COUNT: String = "Routes"
    const val SUUNTO_ROUTES_IN_WATCH_COUNT: String = "RoutesInWatch"
    const val SUUNTO_SYNC_ROUTES_CONTEXT: String = "Context"

    // Connectivity service start analytics
    const val SUUNTO_CONNECTIVITY_RESTART_COUNT: String = "RestartCountSinceServiceStarted"
    const val SUUNTO_CONNECTIVITY_MINUTES_SINCE_SERVICE_STARTED: String =
        "MinutesSinceServiceStarted"
    const val SUUNTO_CONNECTIVITY_MINUTES_SINCE_PHONE_BOOT: String = "MinutesSincePhoneBooted"
    const val SUUNTO_CONNECTIVITY_MINUTES_SINCE_LAST_RESTART: String = "MinutesSinceLastRestart"
    const val SUUNTO_CONNECTIVITY_CONNECTED_GPS_ON_WHEN_SHUT_DOWN: String =
        "ConnectedGPSOnWhenShutDown"
    const val SUUNTO_CONNECTIVITY_POWER_SAVE_MODE_ON_WHEN_STARTING: String =
        "PowerSaveModeOnWhenStarting"
    const val SUUNTO_CONNECTIVITY_IGNORING_DOZE_MODE_WHEN_STARTING: String =
        "IgnoringDozeModeWhenStarting"

    // Gps analytics properties.
    const val GPS_DURATION: String = "Duration"
    const val GPS_COORDINATES: String = "NumberOfGPSCoordinates"
    const val GPS_SECONDS_SINCE_LAST: String = "SecondsSinceLastCoordinate"
    const val GPS_MAX_OFFSET_IN_TIME: String = "GPSSampleOffsetMaxTime"
    const val GPS_MAX_OFFSET_IN_METERS: String = "GPSSampleOffsetMaxMeters"
    const val GPS_NUMBER_OFFSETS_ABOVE_THRESHOLD: String = "GPSSampleOffsetsAboveThreshold"
    const val GPS_BACK_GROUND_LOCATION_ENABLED: String = "BackgroundLocationEnabled"

    // Power saving related analytics properties.
    const val POWER_SAVE_MODE_ON: String = "PowerSaveModeOn"
    const val IGNORING_DOZE_MODE: String = "IgnoringDozeMode"

    // Maps
    const val MAP_MODE: String = "MapMode"
    const val MAP_TYPE: String = "MapType"
    const val MAP_3D_MODE: String = "3DMap"
    const val MAP_3D: String = "Map3D"
    const val MY_POIS: String = "MyPOIS"
    const val POPULAR_ROUTES_TYPE: String = "PopularRoutesType"
    const val MAP_3D_MODE_INPUT_METHOD: String = "3DMapInputMethod"
    const val MAP_HEATMAP_TYPE: String = "HeatMapType"
    const val MAP_ROAD_SURFACE_LAYER: String = "RoadSurfaceLayer"
    const val MAP_HIDE_CYCLING_FORBIDDEN_ROADS: String = "HideCyclingForbiddenRoads"
    const val MAP_MYTRACKS: String = "MyTracks"
    const val MAP_FEATURE_CHANGED: String = "MapFeatureChanged"
    const val LOCATION_PIN: String = "LocationPin"
    const val TURN_WAYPOINT: String = "TurnWaypoint"
    const val TURN_WAYPOINTS: String = "TurnWaypoints"
    const val TURN_WAYPOINT_COUNT: String = "TurnWaypointCount"
    const val ROUTE_PLANNING_STARTED_SOURCE: String = "Source"

    // 247 analytics properties
    const val WORKOUTS: String = "Workouts"
    const val WORKOUTS_DURATION: String = "WorkoutsDuration"
    const val WORKOUTS_CALORIES: String = "WorkoutsCalories"
    const val STEPS: String = "Steps"
    const val STEPS_GOAL_ACHIEVED: String = "StepsGoalAchieved"
    const val STEPS_GOAL: String = "StepsGoal"
    const val CALORIES: String = "Calories"
    const val CALORIES_GOAL_ACHIEVED: String = "CaloriesGoalAchieved"
    const val CALORIES_GOAL: String = "CaloriesGoal"
    const val SLEEP: String = "Sleep"
    const val SLEEP_GOAL_ACHIEVED: String = "SleepGoalAchieved"
    const val SLEEP_GOAL: String = "SleepGoal"
    const val SLEEP_QUALITY: String = "SleepQuality"
    const val SLEEP_DEEP_SLEEP: String = "SleepDeepSleep"
    const val SLEEP_AWAKE_TIME: String = "SleepAwakeTime"
    const val SLEEP_AVERAGE_HR: String = "SleepAverageHR"
    const val SLEEP_TIME_FELL_ASLEEP: String = "SleepTimeFellAsleep"
    const val SLEEP_TIME_WOKE_UP: String = "SleepTimeWokeUp"
    const val DAY_TYPE: String = "DayType"
    const val WEEKDAY: String = "Weekday"
    const val SLEEP_HRV: String = "SleepHRV"
    const val SLEEP_HRV_7_DAYS_AVG: String = "SleepHRV7daysAvg"
    const val SLEEP_HRV_NORMAL_RANGE_MIN: String = "SleepHRVNormalRangeMin"
    const val SLEEP_HRV_NORMAL_RANGE_MAX: String = "SleepHRVNormalRangeMax"

    // Day view analytics properties
    const val DAY_DETAILS_SOURCE: String = "Source"
    const val DAY_DETAILS_DAYS_SINCE: String = "DaysSince"
    const val DAY_DETAILS_LOCAL_HOUR: String = "LocalHour"
    const val DAY_DETAILS_CALORIES: String = "Calories"
    const val DAY_DETAILS_STEPS: String = "Steps"
    const val DAY_DETAILS_SLEEP: String = "Sleep"
    const val WORKOUT_DETAILS_SOURCE: String = "Source"
    const val DAY_DETAILS_SPO2AVG: String = "SpO2Avg"
    const val DAY_DETAILS_SPO2MAX: String = "SpO2Max"
    const val DAY_DETAILS_SPO2MIN: String = "SpO2Min"

    // Stress and recovery
    const val STRESS_AND_RECOVERY_HIGHEST_VALUE: String = "Resources"
    const val STRESS_AND_RECOVERY_HIGHEST_VALUE_TIME: String = "ResourcesTime"
    const val STRESS_AND_RECOVERY_NO_DATA: String = "NoResourcesTracked"
    const val STRESS_AND_RECOVERY_DURATION_ACTIVE: String = "ResourcesDurationActive"
    const val STRESS_AND_RECOVERY_DURATION_PASSIVE: String = "ResourcesDurationInactive"
    const val STRESS_AND_RECOVERY_DURATION_STRESSFUL: String = "ResourcesDurationStressed"
    const val STRESS_AND_RECOVERY_DURATION_RELAXING: String = "ResourcesDurationRecovering"

    // Diary calendar view
    const val NEW_LEVEL: String = "NewLevel"
    const val CALENDAR_LEVEL: String = "CalendarLevel"

    // ST diary
    const val ST_DIARY_SUMMARY_TYPE: String = "SummaryType"

    // App start stability reporting.
    const val GPS_TRACKING_INTERRUPTED: String = "GpsTrackingInterrupted"
    const val CONNNECTED_GPS_TRACKING_INTERRUPTED: String = "ConnectedGpsTrackingInterrupted"
    const val CONNECTIVITY_RESTART_DELTA_MS: String = "ConnectivityRestartDeltaMs"
    const val CONNECTIVITY_RESTART_COUNT: String = "ConnectivityRestartCount"

    // Movescount disconnect warning properties
    const val MOVESCOUNT_CONTEXT: String = "Context"
    const val MOVESCOUNT_REACTION: String = "Reaction"
    const val MOVESCOUNT_INSTALLED: String = "MovescountInstalled"

    // Route share
    const val ROUTE_SHARE_DISTANCE_IN_METERS: String = "DistanceInMeters"
    const val ROUTE_SHARE_DURATION_IN_MINUTES: String = "DurationInMinutes"
    const val ROUTE_SHARE_FORMAT: String = "Format"

    // Report workout
    const val CONTENT_TYPE: String = "ContentType"
    const val DESCRIPTION: String = "Description"
    const val REPORTER_TOTAL_WORKOUTS: String = "ReporterTotalWorkouts"
    const val REPORTED_WORKOUT_ID: String = "ReportedWorkoutID"
    const val REPORTED_USER_ID: String = "ReportedUserID"
    const val NUMBER_OF_PHOTOS: String = "NumberOfPhotos"
    const val NUMBER_OF_VIDEOS: String = "NumberOfVideos"

    // Suunto news and offers subscription
    const val NEWS_AND_OFFERS_SUBSCRIBED: String = "NewsAndOffersSubscribed"
    const val NEWS_AND_OFFERS_CONTEXT: String = "Context"

    // Suunto watch guide type
    const val OTHER_GUIDES: String = "OtherGuides"

    const val FIRST_TIME_SINCE_SIGNUP: String = "FirstTimeSinceSignup"

    const val SIGNUP_FLOW_TYPE: String = "SignUpFlowType"
    const val PHONE_NUMBER_STATUS: String = "PhoneNumberStatus"

    // Suunto no watch paired dialog response/reason type
    const val SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE: String = "Response"
    const val SUUNTO_NO_WATCH_PAIRED_DIALOG_REASON: String = "Reason"

    // Suunto 7 helpshift articles
    const val RELATED_WATCH_MODEL: String = "RelatedWatchModel"
    const val WATCH_PAIRED_ON_OS_LEVEL: String = "WatchPairedOnOSLevel"

    // Pairing help articles
    const val HELPSHIFT_ARTICLE_ID: String = "HelpshiftArticleID"

    // Companion device association
    const val SUUNTO_COMPANION_LINKING_RESPONSE: String = "Response"
    const val SUUNTO_COMPANION_LINKING_CONTEXT: String = "Context"

    const val SEARCH_DURATION_IN_MILLISECONDS: String = "SearchDurationInMilliseconds"
    const val NUMBER_OF_ROUTES_FOUND: String = "NumberOfRoutesFound"
    const val POPULAR_ROUTES_STATUS: String = "PopularRoutesStatus"
    const val NUMBER_OF_ROUTES: String = "NumberOfRoutes"
    const val NUMBER_OF_ROUTES_AVAILABLE: String = "NumberOfRoutesAvailable"
    const val PLACE_IN_SUGGESTIONS: String = "PlaceInSuggestions"
    const val USE_IN_WATCH: String = "UseInWatch"
    const val ROUTE_NAME: String = "RouteName"

    // Dashboard
    const val WIDGET_TAPPED: String = "WidgetTapped"

    const val NEW_DASHBOARD_PAGE: String = "NewDashboardPage"
    const val NUMBER_OF_DASHBOARD_PAGES: String = "NumberOfDashboardPages"
    const val DASHBOARD_PAGE_ENTERED: String = "DashboardPageEntered"
    const val SLEEP_DATA_AVAILABLE: String = "SleepDataAvailable"
    const val STEPS_DATA_AVAILABLE: String = "StepsDataAvailable"
    const val RESOURCES_DATA_AVAILABLE: String = "ResourcesDataAvailable"
    const val CALORIES_DATA_AVAILABLE: String = "CaloriesDataAvailable"
    const val TRAINING_DATA_AVAILABLE: String = "TrainingDataAvailable"
    const val PROGRESS_DATA_AVAILABLE: String = "ProgressDataAvailable"
    const val CURRENT_ATL_VALUE: String = "CurrentATLValue"
    const val CURRENT_CTL_VALUE: String = "CurrentCTLValue"
    const val CURRENT_FORM_VALUE: String = "CurrentFormValue"
    const val DEVICE_CATEGORY: String = "DeviceCategory"

    // Dashboard widget customization
    const val WIDGET: String = "Widget"
    const val WIDGETS: String = "Widgets"
    const val WIDGET_COUNT: String = "WidgetCount"
    const val DASHBOARD_PAGE: String = "DashboardPage"
    const val ADD_OR_REPLACE: String = "AddOrReplace"

    // Achievement
    const val ACHIEVEMENT_TYPE: String = "AchievementType"
    const val ACHIEVEMENT_WORKOUT_DURATION: String = "WorkoutDuration"
    const val ACHIEVEMENT_WORKOUT_DISTANCE: String = "WorkoutDistance"
    const val ACHIEVEMENT_WORKOUT_START_HOUR: String = "WorkoutStartHour"

    // OTA update
    const val WATCH_MANAGEMENT_UPDATE_CHECK_METHOD: String = "UpdateCheckMethod"
    const val WATCH_MANAGEMENT_DOWNLOAD_DURATION: String = "DownloadDuration"
    const val WATCH_MANAGEMENT_FAILURE_REASON: String = "FailureReason"
    const val WATCH_MANAGEMENT_UPDATE_FLOW_STATUS: String = "UpdateFlowStatus"
    const val WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION: String = "NewFirmwareVersion"
    const val WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE: String = "FirmwareDowngrade"
    const val WATCH_MANAGEMENT_FIRMWARE_DOWNGRADE_SUGGESTED: String = "FirmwareDowngradeSuggested"

    // Share summary
    const val SHARE_SUMMARY_WORKOUT_DAYS: String = "WorkoutDays"
    const val SHARE_SUMMARY_SHARE_PHOTO_INITIATED_FROM: String = "SharePhotoInitiatedFrom"
    const val SHARE_SUMMARY_CALENDAR_LEVEL: String = "CalendarLevel"

    // POIS
    const val NUMBER_OF_POIS: String = "NumberOfPOIs"

    // hr belt
    const val BELT_MODEL: String = "BeltModel"
    const val HR_BELT: String = "HrBelt"

    // workout weather
    const val WEATHER_CONDITION: String = "WeatherCondition"
    const val WEATHER_TEMPERATURE: String = "WeatherTemperature"
    const val WEATHER_WIND_SPEED: String = "WeatherWindSpeed"
    const val WEATHER_WIND_DIRECTION: String = "WeatherWindDirection"
    const val WEATHER_RAINFALL_LAST_HOUR: String = "WeatherRainfallLastHour"
    const val WEATHER_SNOWFALL_LAST_HOUR: String = "WeatherSnowfallLastHour"

    // TSS
    const val LINK_NAME: String = "LinkName"

    // Laps
    const val PART_OF_MULTISPORT_WORKOUT: String = "PartOfMultisportWorkout"
    const val COLUMN_CHANGED: String = "ColumnChanged"
    const val NEW_DATA_TYPE: String = "NewDataType"
    const val OLD_DATA_TYPE: String = "OldDataType"
    const val NEW_INTERVAL: String = "NewInterval"

    // System widgets
    const val SYSTEM_WIDGET_TYPE: String = "Widget"
    const val SYSTEM_WIDGET_NEW_LAYOUT: String = "NewLayout"

    // Glance widgets
    const val GLANCE_WIDGET_NAME: String = "appWidgetName"
    const val GLANCE_WIDGET_COUNT: String = "appWidgetCount"

    // App open
    const val APP_OPEN_WATCH_MODEL: String = "WatchModel"
    const val APP_OPEN_IS_FIRST_TIME: String = "isFirstTime"
    const val APP_OPEN_SOURCE_TYPE: String = "SourceType"
    const val APP_OPEN_SOURCE_TYPE_DETAIL: String = "SourceTypeDetail"
    const val APP_OPEN_RESUME_FROM_BACKGROUND: String = "ResumeFromBackground"

    const val ACTION_CHOSEN: String = "ActionChosen"

    // Device Switch
    const val CURRENT_WATCH_MODEL: String = "CurrentWatchModel"
    const val CURRENT_WATCH_FIRMWARE_VERSION: String = "CurrentWatchFirmwareVersion"
    const val CURRENT_WATCH_SERIAL_NUMBER: String = "CurrentWatchSerialNumber"
    const val NEW_WATCH_MODEL: String = "NewWatchModel"
    const val NEW_WATCH_FIRMWARE_VERSION: String = "NewWatchFirmwareVersion"
    const val NEW_WATCH_SERIAL_NUMBER: String = "NewWatchSerialNumber"
    const val PREV_WATCH_MODEL: String = "PreviousWatchModel"
    const val PREV_WATCH_SERIAL: String = "PreviousWatchSerialNumber"

    // SuuntoPlus™ Store
    const val SUUNTO_PLUS_STORE_FILTERED_FILTER: String = "Filter"
    const val SUUNTO_PLUS_ITEM_NAME: String = "SuuntoPlusName"
    const val SUUNTO_PLUS_ITEM_ID: String = "SuuntoPlusId"
    const val SUUNTO_PLUS_ITEM_TYPE: String = "Type"
    const val SUUNTO_PLUS_ITEM_CATEGORIES: String = "Categories"
    const val SUUNTO_PLUS_ITEM_ADD_TO_WATCH: String = "AddToWatch"
    const val SUUNTO_PLUS_DETAIL_SOURCE: String = "Source"

    // SuuntoPlus™ Guides
    const val SUUNTO_PLUS_GUIDES_TOTAL_COUNT: String = "GuidesTotalCount"
    const val SUUNTO_PLUS_GUIDES_PINNED_COUNT: String = "GuidesPinnedCount"
    const val SUUNTO_PLUS_GUIDES_NON_PINNED_IN_WATCH_COUNT: String = "GuidesNonPinnedInWatchCount"
    const val SUUNTO_PLUS_GUIDES_NOT_IN_WATCH_COUNT: String = "GuidesNotInWatchCount"
    const val SUUNTO_PLUS_GUIDE_NOT_COMPATIBLE_COUNT: String = "GuidesNotCompatibleCount"
    const val SUUNTO_PLUS_GUIDE_SOURCES_PINNED: String = "GuideSourcesPinned"
    const val SUUNTO_PLUS_GUIDE_SOURCES_NON_PINNED_IN_WATCH: String = "GuideSourcesNonPinnedInWatch"
    const val SUUNTO_PLUS_GUIDE_GUIDES_DELETED: String = "GuidesDeleted"
    const val SUUNTO_PLUS_GUIDES_NAME: String = "GuidesName"
    const val SUUNTO_PLUS_PLANS_NAME: String = "PlansName"
    const val LINK_TYPE: String = "LinkType"

    // SuuntoPlus™ Features
    const val SUUNTO_PLUS_FEATURES_IN_USE: String = "SuuntoPlusInUse"
    const val SUUNTO_PLUS_FEATURES_NOT_IN_USE: String = "SuuntoPlusNotInUse"

    // SuuntoPlus™ sports apps
    const val SUUNTO_PLUS_MY_SPORTS_APPS_IN_LIBRARY: String = "SportsAppsInLibrary"
    const val SUUNTO_PLUS_MY_SPORTS_APPS_IN_WATCH: String = "SportsAppsInWatch"
    const val SUUNTO_PLUS_MY_SPORTS_APPS_NAME: String = "SuuntoPlusName"
    const val SUUNTO_PLUS_MY_SPORTS_APPS_NEW_SETTING: String = "NewSetting"
    const val SUUNTO_PLUS_SPORTS_APPS_ID: String = "SportsAppID"
    const val SUUNTO_PLUS_SPORTS_APPS_NAME: String = "SportsAppName"
    const val SUUNTO_PLUS_REPORT_SPORTS_APP_REASON: String = "Reason"

    // Workout playback
    const val WORKOUT_PLAYBACK_DIMENSION: String = "Dimension"
    const val WORKOUT_PLAYBACK_TIMELINE_POINT: String = "TimelinePoint"
    const val WORKOUT_PLAYBACK_PAUSE_REASON: String = "PauseReason"
    const val WORKOUT_PLAYBACK_NEW_DIMENSION: String = "NewDimension"
    const val WORKOUT_PLAYBACK_PLAYBACK_TYPE: String = "PlaybackType"
    const val WORKOUT_PLAYBACK_INITIATED_FROM: String = "PlaybackInitiatedFrom"

    // Graph analysis
    const val WORKOUT_ANALYSIS_MAIN_GRAPH_TYPE: String = "MainGraphType"
    const val WORKOUT_ANALYSIS_COMPARISON_GRAPH_TYPE: String = "ComparisonGraphType"
    const val WORKOUT_ANALYSIS_BACKGROUND_GRAPH_TYPE: String = "BackgroundGraphType"
    const val WORKOUT_ANALYSIS_WORKOUT_DURATION: String = "WorkoutDuration"
    const val WORKOUT_ANALYSIS_LAP_DURATION: String = "LapDuration"
    const val WORKOUT_ANALYSIS_WORKOUT_DISTANCE: String = "WorkoutDistance"
    const val WORKOUT_ANALYSIS_LAP_DISTANCE: String = "LapDistance"
    const val WORKOUT_ANALYSIS_LAP_SELECTION_TYPE: String = "SelectionType"
    const val WORKOUT_ANALYSIS_LAP_TYPE: String = "LapType"
    const val WORKOUT_ANALYSIS_LAP_COUNT: String = "LapCount"
    const val WORKOUT_ANALYSIS_LAP_NUMBER: String = "LapNumber"
    const val WORKOUT_ANALYSIS_FULL_SCREEN_MODE: String = "WorkoutAnalysisScreenFullScreenMode"
    const val WORKOUT_ANALYSIS_IS_LAP_CHOSEN: String = "WorkoutAnalysisScreenLapChosen"

    // Dive Mode Customization
    const val DIVE_MODE_CUSTOMIZATION_SYNC_RESULT: String = "Result"
    const val DIVE_MODE_CUSTOMIZATION_SYNC_RESULT_SUCCESS: String = "Success"
    const val DIVE_MODE_CUSTOMIZATION_SYNC_RESULT_ERROR: String = "Error"
    const val DIVE_MODE_CUSTOMIZATION_DIVE_STYLE: String = "DiveStyle"
    const val DIVE_MODE_CUSTOMIZATION_DIVE_MODE: String = "DiveMode"
    const val DIVE_MODE_CUSTOMIZATION_ACTIVITY_TYPE: String = "ActivityType"
    const val DIVE_MODE_CUSTOMIZATION_ALGORITHM: String = "Algorithm"
    const val DIVE_MODE_CUSTOMIZATION_CONSERVATISM: String = "Conservatism"
    const val DIVE_MODE_CUSTOMIZATION_ALTITUDE: String = "Altitude"
    const val DIVE_MODE_CUSTOMIZATION_GF_LOW: String = "GradientFactorLow"
    const val DIVE_MODE_CUSTOMIZATION_GF_HIGH: String = "GradientFactorHigh"
    const val DIVE_MODE_CUSTOMIZATION_SAFETY_STOP: String = "SafetyStop"
    const val DIVE_MODE_CUSTOMIZATION_DEEP_STOP: String = "DeepStop"
    const val DIVE_MODE_CUSTOMIZATION_LAST_DECO_STOP_DEPTH: String = "LastDecoStopDepth"
    const val DIVE_MODE_CUSTOMIZATION_HELIUM: String = "Helium"
    const val DIVE_MODE_CUSTOMIZATION_MULTIGAS: String = "MultipleGases"
    const val DIVE_MODE_CUSTOMIZATION_MODIFY_GASES_DURING_DIVE: String = "ModifyGasesDuringDive"
    const val DIVE_MODE_CUSTOMIZATION_GAS_MAX_PO2: String = "GasMaxPO2"
    const val DIVE_MODE_CUSTOMIZATION_NUMBER_OF_GASES: String = "NumberOfGases"
    const val DIVE_MODE_CUSTOMIZATION_ALARM_DEPTH: String = "AlarmDepth"
    const val DIVE_MODE_CUSTOMIZATION_ALARM_TIME_SCUBA_DIVE: String = "AlarmTimeScubaDive"
    const val DIVE_MODE_CUSTOMIZATION_ALARM_TIME_FREE_DIVE: String = "AlarmTimeFreediving"
    const val DIVE_MODE_CUSTOMIZATION_ALARM_TANK_PRESSURE: String = "AlarmTankPressure"
    const val DIVE_MODE_CUSTOMIZATION_ALARM_GAS_TIME: String = "AlarmGasTime"
    const val DIVE_MODE_CUSTOMIZATION_NOTIFICATION_SURFACE: String = "NotificationSurface"
    const val DIVE_MODE_CUSTOMIZATION_NOTIFICATION_DEPTH: String = "NotificationDepth"
    const val DIVE_MODE_CUSTOMIZATION_UNITS: String = "Units"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_1_LAYOUT: String = "View1Layout"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_1_SWITCHABLE_DATA_FIELDS: String =
        "View1SwitchableDataFields"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_2_LAYOUT: String = "View2Layout"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_2_SWITCHABLE_DATA_FIELDS: String =
        "View2SwitchableDataFields"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_3_LAYOUT: String = "View3Layout"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_3_SWITCHABLE_DATA_FIELDS: String =
        "View3SwitchableDataFields"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_4_LAYOUT: String = "View4Layout"
    const val DIVE_MODE_CUSTOMIZATION_VIEW_4_SWITCHABLE_DATA_FIELDS: String =
        "View4SwitchableDataFields"
    const val DIVE_MODE_CUSTOMIZATION_GAS_INDEX: String = "GasIndex"
    const val DIVE_MODE_CUSTOMIZATION_GAS_OXYGEN_PERCENTAGE: String = "OxygenPercentage"
    const val DIVE_MODE_CUSTOMIZATION_GAS_HELIUM_PERCENTAGE: String = "HeliumPercentage"
    const val DIVE_MODE_CUSTOMIZATION_GAS_OXYGEN_HELIUM_MIX: String = "OxygenHeliumMix"
    const val DIVE_MODE_CUSTOMIZATION_GAS_LIST_TYPE: String = "GasListType"
    const val DIVE_MODE_CUSTOMIZATION_GAS_FILL_PRESSURE: String = "FillPressure"
    const val DIVE_MODE_CUSTOMIZATION_GAS_TANK_SIZE: String = "TankSize"
    const val DIVE_MODE_CUSTOMIZATION_SAVE_TYPE: String = "SaveType"
    const val DIVE_MODE_CUSTOMIZATION_SET_AS_ACTIVE_MODE: String = "SetAsActiveMode"

    // Tags
    const val TAGS_FIELD_EDITED_NAME: String = "Tags"
    const val TAG_TYPE: String = "TagType"
    const val TAG_TYPE_PUBLIC: String = "Public"
    const val TAG_TYPE_CUSTOM: String = "Custom"
    const val PUBLIC_TAG: String = "PublicTag"
    const val TAG_ADD_METHOD: String = "AddMethod"
    const val TAG_ADD_METHOD_MANUAL: String = "Manual"
    const val TAG_ADD_METHOD_AUTOMATIC: String =
        "Automatic" // Automatic events are sent from backend, we keep the variable here for traceability
    const val CO2_EMISSIONS_REDUCED: String = "CO2EmissionsReduced"

    // Questionnaire
    const val STEP_SKIPPED_IN: String = "StepSkippedIn"
    const val FAVORITE_SPORTS: String = "FavoriteSports"
    const val NUMBER_OF_SPORTS_CHOSEN: String = "NumberOfSportsChosen"
    const val MOTIVATIONS_CHOSEN: String = "MotivationsChosen"
    const val NUMBER_OF_MOTIVATIONS_CHOSEN: String = "NumberOfMotivationsChosen"

    // Watch widget customization
    const val WATCH_WIDGET_NAME: String = "Widget"
    const val NEW_STATUS: String = "NewStatus"
    const val NEW_POSITION: String = "NewPosition"
    const val WIDGETS_IN_USE: String = "WidgetsInUse"
    const val USE_IN_WATCH_STATUS: String = "UseInWatchStatus"

    // Premium subscription
    const val BUY_PREMIUM_POPUP_SHOWN_REASON: String = "Reason"

    // Offline maps
    const val REGION_SELECTED: String = "RegionSelected"
    const val NUMBER_OF_MAPS_DOWNLOADING: String = "NumberOfMapsDownloading"
    const val NUMBER_OF_MAPS_DOWNLOADED: String = "NumberOfMapsDownloaded"
    const val WIFI_SETUP: String = "WifiSetUp"
    const val ISSUE_TYPE: String = "IssueType"

    // Watch open weather event
    const val REAL_TIME_WEATHER_RESULT: String = "RealTimeWeatherResult"
    const val FORECAST_RESULT: String = "ForecastResult"
    const val UV_RESULT: String = "UVResult"
    const val AIR_QUALITY_RESULT: String = "AirQualityResult"
    const val OVERALL_RESULT: String = "OverallResult"

    const val SUCCESS: String = "Success"
    const val FAIL: String = "Fail"
    const val TIME_PERIOD: String = "TimePeriod"
    const val GOAL_TYPE: String = "GoalType"
    const val ACTIVITIES: String = "Activities"
    const val MAJOR: String = "Major"
    const val MINOR: String = "Minor"
    const val HOTFIX: String = "Hotfix"

    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_SPORTS: String = "Sports"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_GROUPING: String = "Grouping"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_MIN_DATE: String = "MinDateEPOCmillis"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_MAX_DATE: String = "MaxDateEPOCmillis"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_MIN_DISTANCE: String = "MinDistance"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_MAX_DISTANCE: String = "MaxDistance"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_SHOW_EMPTY_ROWS_ENABLED: String =
        "ShowEmptyRowsEnabled"
    const val TRAINING_ZONE_SUMMARY_LAST_FILTERING_LAYOUT_TYPE: String = "LayoutType"
    const val LAP_COLOURING_NEW_SETTING: String = "NewSetting"

    const val MENSTRUAL_CYCLE_ONBOARDING_DONE_REASON = "Reason"
    const val MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_REGULARITY = "InitialCycleRegularity"
    const val MENSTRUAL_CYCLE_ONBOARDING_DONE_CYCLE_LENGTH = "InitialCycleLength"
    const val MENSTRUAL_CYCLE_ONBOARDING_DONE_PERIOD_DURATION = "InitialPeriodDuration"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_FROM = "From"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_LOG_ONGOING_OR_BACKLOG = "LogOngoingOrBacklog"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_ACTION = "Action"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_LENGTH = "CycleLength"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_START_DAY = "PeriodStartDay"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_END_DAY = "PeriodEndDate"
    const val MENSTRUAL_CYCLE_PERIOD_LOGGED_CYCLE_REGULARITY = "CycleRegularity"

    // DiveRouteUserFeedback
    const val DIVE_ROUTE_USER_FEEDBACK_RATING = "UserRating"
    const val DIVE_ROUTE_USER_FEEDBACK_QUALITY = "DiveRouteQuality"
    const val DIVE_ROUTE_USER_FEEDBACK_ORIGIN_ALTITUDE = "OriginAltitude"
    const val DIVE_ROUTE_USER_FEEDBACK_ORIGIN_LATITUDE = "OriginLatitude"
    const val DIVE_ROUTE_USER_FEEDBACK_ORIGIN_LONGITUDE = "OriginLongitude"
    const val DIVE_ROUTE_USER_FEEDBACK_FIGURE_OF_MERIT = "FigureOfMerit"
    const val DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_X = "GyroBiasX"
    const val DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_Y = "GyroBiasY"
    const val DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_Z = "GyroBiasZ"

    const val MESSAGE_SOURCE = "Source"
    const val CITY = "City"

    // Annual report
    const val H5LINK_NAME = "H5LinkName"
    const val SHARE_TYPE = "ShareType"
    const val NUMBER_OF_IMAGES = "NumberOfImages"
    const val TYPES_OF_IMAGES = "TypesOfImages"
    const val SHARE_TARGET = "ShareTarget"
    const val H5_CLICK_BUTTON_NAME = "H5ButtonName"
    const val H5_PAGE_NAME = "H5PageName"
    const val ANNUAL_REPORT_TARGET_URL = "TargetURL"

    // Headphone
    const val PAGE_NAME = "PageName"
    const val SPORTS_SWITCH = "SportsSwitch"
    const val HEAD_MOVEMENT_CONTROL = "HeadMovementControl"
    const val SOUND_MODE = "SoundMode"
    const val NECK_FATIGUE_ALERT = "NeckFatigueAlert"
    const val MUSIC_MODE = "MusicMode"
    const val FEATURE_NAME = "FeatureName"
    const val SETTING = "Setting"

    // Marketing Banner
    const val BANNER_ID = "BannerId"
    const val BANNER_NAME = "BannerName"
    const val BANNER_CLICK_AREA = "ClickArea"
    const val BANNER_TARGET_URL = "TargetURL"

    const val NAVIGATION_TYPE = "NavigationType"
    const val NAVIGATION_ERROR_CODE = "error code"
    const val NAVIGATION_TYPE_ROUTE = "route"
    const val NAVIGATION_TYPE_BEARING = "bearing"

    const val PUSH_MESSAGE_TYPE = "PushMessageType"

    const val INBOX_MESSAGE_TITLE = "MessageTitle"

    const val NOTIFICATION_MESSAGE_TYPE = "MessageType"

    const val SN = "SN"

    const val APP_UPDATE_NOW_SOURCE = "Source"

    // Profile
    const val TAB_NAME = "TabName"
    const val SEARCH_WORD = "SearchWord"

    // Training zone
    const val TRAINING_ZONE_BROWSING_DURATION = "BrowsingDuration"
    const val TRAINING_ZONE_BROWED_PAGE = "BrowedPages"
    const val TRAINING_ZONE_PAGE_NAME = "PageName"
    const val STATISTICS_TABLE_ACTIVITY = "Activity"
    const val STATISTICS_TABLE_GROUP_BY = "GroupBy"
    const val STATISTICS_TABLE_TAGS = "Tags"
    const val STATISTICS_TABLE_MIN_DISTANCE = "MinDistance"
    const val STATISTICS_TABLE_MAX_DISTANCE = "MaxDistance"
    const val STATISTICS_TABLE_INTERVAL_DAYS = "IntervalDays"
    const val STATISTICS_TABLE_FIELDS = "Fields"
    const val STATISTICS_GRAPH_ACTIVITY = "Activity"
    const val STATISTICS_GRAPH_MAIN_GRAPH_TYPE = "MainGraphType"
    const val STATISTICS_GRAPH_SUB_GRAPH_TYPE = "SubGraphType"
    const val TRAINING_ZONE_BUTTON_NAME = "ButtonName"

    // Dashboard widgets
    const val WIDGET_NAME = "WidgetName"
    const val TIME_DIM = "TimeDim"
    const val MODULE_NAME = "ModuleName"
    const val BROWSING_DURATION = "BrowsingDuration"
    const val BUTTON_NAME = "ButtonName"
    const val MAIN_GRAPH_TYPE = "MainGraphType"
    const val SUB_GRAPH_TYPE = "SubGraphType"
    const val CHANGED_CONTENT = "ChangedContent"

    // Badges
    const val BADGES_MODULE_NAME = "ModuleName"
    const val BADGE_NAME = "BadgeName"
    const val BADGE_STATUS = "BadgeStatus"
}
