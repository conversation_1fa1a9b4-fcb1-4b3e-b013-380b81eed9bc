package com.stt.android.analytics

interface EmarsysAnalytics {
    fun setBirthYear(year: Int)

    fun trackStringUserProperty(userProperty: String, value: String)

    fun trackIntUserProperty(userProperty: String, value: Int)

    fun setUserId()

    fun trackBooleanUserProperty(userProperty: String, value: Boolean)

    fun trackArrayUserProperty(userProperty: String, values: Array<String?>)

    fun trackUserProperties(properties: Map<String, Any?>)

    fun trackEvent(@AnalyticsEvent.EventName event: String)

    fun trackEventWithProperties(@AnalyticsEvent.EventName event: String, properties: Map<String, Any?>)

    fun isEnabled(): Boolean

    fun logout()
}
