package com.stt.android.remoteconfig.impl.asko

import android.content.SharedPreferences
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import com.stt.android.utils.STTConstants
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify

@RunWith(MockitoJUnitRunner::class)
class AskoRemoteConfigTest {
    @Mock
    lateinit var preferences: SharedPreferences

    @Mock
    lateinit var suuntoPreferences: SharedPreferences

    @Mock
    lateinit var editor: SharedPreferences.Editor

    @Mock
    internal lateinit var askoRemoteConfigApi: AskoRemoteConfigApi

    @Mock
    lateinit var userSettingsController: UserSettingsController

    private lateinit var askoRemoteConfig: AskoRemoteConfig

    private val defaults: AskoRemoteConfigDefaults = AskoRemoteConfigDefaults(
        AskoRemoteConfigResponse(
            emarsysEnabled = AskoRemoteConfigDefaults.singleCondition(value = true),
            graphhopperBaseUrl = AskoRemoteConfigDefaults.singleCondition(
                value = AskoGraphhopperBaseUrl(
                    url = "https://graphhopper.com/api/1",
                    alternatives = emptyList(),
                ),
            ),
            aiPlannerEnabled = AskoRemoteConfigDefaults.singleCondition(value = true),
            tidesQueryParameters = AskoRemoteConfigDefaults.singleCondition(
                value = AskoTidesParameters(
                    autoSyncThresholds = AskoTidesThresholds(
                        maxAge = 5760,
                        maxDistance = 10000,
                    ),
                    manualSyncThresholds = AskoTidesThresholds(
                        maxAge = 8640,
                        maxDistance = 5000,
                    ),
                )
            )
        )
    )

    private val key: String = "TEST_KEY"
    private val rollResultKey = "${AskoRemoteConfig.ASKO_REMOTE_CONFIG_KEY_PERCENTILE_ROLL_RESULT}_$key"
    private val rollResult: Float = 0.4f

    @Before
    fun setup() {
        askoRemoteConfig = spy(
            AskoRemoteConfig(
                preferences = preferences,
                suuntoPreferences = suuntoPreferences,
                askoRemoteConfigApi = askoRemoteConfigApi,
                askoRemoteConfigDefaults = defaults,
                userSettingsController = userSettingsController,
                currentVersion = 3050000,
            )
        )

        // forcing deterministic roll for testing
        doReturn(rollResult).`when`(askoRemoteConfig).rollPercentile()

        Mockito.`when`(preferences.edit()).thenReturn(editor)
        Mockito.`when`(editor.putFloat(ArgumentMatchers.anyString(), ArgumentMatchers.anyFloat())).thenReturn(editor)
    }

    @Test
    fun `match country`() {
        val userSettings = mock<UserSettings>()
        Mockito.`when`(userSettingsController.settings).thenReturn(userSettings)
        Mockito.`when`(userSettings.country).thenReturn("FI")
        assertThat(askoRemoteConfig.matchCountry(null)).isTrue()
        assertThat(askoRemoteConfig.matchCountry(listOf("FI"))).isTrue()
        assertThat(askoRemoteConfig.matchCountry(listOf())).isFalse()
        assertThat(askoRemoteConfig.matchCountry(listOf("EN"))).isFalse()
    }

    @Test
    fun `match watch model`() {
        Mockito.`when`(
            suuntoPreferences.getString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
                null
            )
        )
            .thenReturn("ibiza")
        assertThat(askoRemoteConfig.matchWatchModelVersion(null)).isTrue()
        assertThat(askoRemoteConfig.matchWatchModelVersion(listOf("Ibiza", "IbizaC"))).isTrue()
        assertThat(askoRemoteConfig.matchWatchModelVersion(listOf())).isFalse()
        assertThat(askoRemoteConfig.matchWatchModelVersion(listOf("Lima"))).isFalse()
    }

    @Test
    fun `match percentile first time should roll and save`() {
        Mockito.`when`(preferences.contains(rollResultKey)).thenReturn(false)
        val isMatch = askoRemoteConfig.matchPercentile(key, 0.3f)
        verify(askoRemoteConfig).rollPercentile()
        verify(editor).putFloat(rollResultKey, rollResult)
        verify(editor).apply()
        assertThat(isMatch).isFalse()
    }

    @Test
    fun `same percentile second time should use saved state with fail`() {
        Mockito.`when`(preferences.contains(rollResultKey)).thenReturn(true)
        Mockito.`when`(preferences.getFloat(rollResultKey, 1f)).thenReturn(0.4f)
        val isMatch = askoRemoteConfig.matchPercentile(key, 0.3f)
        verify(askoRemoteConfig, never()).rollPercentile()
        assertThat(isMatch).isFalse()
    }

    @Test
    fun `same percentile second time should use saved state with success`() {
        Mockito.`when`(preferences.contains(rollResultKey)).thenReturn(true)
        Mockito.`when`(preferences.getFloat(rollResultKey, 1f)).thenReturn(0.4f)
        val isMatch = askoRemoteConfig.matchPercentile(key, 0.6f)
        verify(askoRemoteConfig, never()).rollPercentile()
        assertThat(isMatch).isTrue()
    }

    @Test
    fun `compare versions`() {
        assertThat(askoRemoteConfig.matchAppVersion("3049000", "3050000")).isTrue()
        assertThat(askoRemoteConfig.matchAppVersion("3049000", null)).isTrue()
        assertThat(askoRemoteConfig.matchAppVersion(null, "3050000")).isTrue()
        assertThat(askoRemoteConfig.matchAppVersion("4000000", null)).isFalse()
        assertThat(askoRemoteConfig.matchAppVersion(null, "3000000")).isFalse()
    }
}
