package com.stt.android.remoteconfig.impl.di

import com.stt.android.remoteconfig.impl.asko.AskoGraphhopperBaseUrl
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigDefaults.Companion.singleCondition
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfigResponse
import com.stt.android.remoteconfig.impl.asko.AskoTidesParameters
import com.stt.android.remoteconfig.impl.asko.AskoTidesThresholds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
internal object FlavorRemoteConfigModule {
    @Provides
    @DefaultAskoRemoteConfig
    fun provideAskoRemoteConfigResponse(): AskoRemoteConfigResponse {
        return AskoRemoteConfigResponse(
            emarsysEnabled = singleCondition(value = true),
            graphhopperBaseUrl = singleCondition(
                value = AskoGraphhopperBaseUrl(
                    url = "https://graphhopper.com/api/1",
                    alternatives = emptyList(),
                ),
            ),
            aiPlannerEnabled = singleCondition(value = true),
            tidesQueryParameters = singleCondition(
                value = AskoTidesParameters(
                    autoSyncThresholds = AskoTidesThresholds(
                        maxAge = 5760,
                        maxDistance = 10000,
                    ),
                    manualSyncThresholds = AskoTidesThresholds(
                        maxAge = 8640,
                        maxDistance = 5000,
                    ),
                ),
            ),
        )
    }
}
