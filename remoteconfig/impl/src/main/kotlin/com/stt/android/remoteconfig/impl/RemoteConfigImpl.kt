package com.stt.android.remoteconfig.impl

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.AppCoroutineScopeProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.remoteconfig.api.GraphhopperBaseUrl
import com.stt.android.remoteconfig.api.RemoteConfig
import com.stt.android.remoteconfig.api.TidesQueryParameters
import com.stt.android.remoteconfig.impl.asko.AskoRemoteConfig
import com.stt.android.remoteconfig.impl.firebase.FirebaseRemoteConfigWrapper
import com.stt.android.utils.BrandFlavourConstants
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

internal class RemoteConfigImpl @Inject constructor(
    private val firebaseRemoteConfigWrapper: FirebaseRemoteConfigWrapper,
    private val askoRemoteConfig: AskoRemoteConfig,
    private val appCoroutineScopeProvider: AppCoroutineScopeProvider,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : RemoteConfig {
    override fun refresh() {
        appCoroutineScopeProvider.appCoroutineScope.launch(coroutinesDispatchers.io) {
            refreshFirebaseRemoteConfig()
        }
        appCoroutineScopeProvider.appCoroutineScope.launch(coroutinesDispatchers.io) {
            refreshAskoRemoteConfig()
        }
    }

    private suspend fun refreshFirebaseRemoteConfig() = runSuspendCatching {
        firebaseRemoteConfigWrapper.refresh()
    }.onFailure { e ->
        Timber.w(e, "Failed to refresh Firebase remote config")
    }

    private suspend fun refreshAskoRemoteConfig() = runSuspendCatching {
        askoRemoteConfig.refresh()
    }.onFailure { e ->
        Timber.w(e, "Failed to refresh Asko remote config")
    }

    override fun getGraphHopperKey(): String = firebaseRemoteConfigWrapper.getGraphHopperKey()

    override fun getGraphhopperBaseUrl(): GraphhopperBaseUrl = askoRemoteConfig.graphhopperBaseUrl

    override fun isEmarsysEnabled(): Boolean = askoRemoteConfig.isEmarsysEnabled

    override fun isAiPlannerEnabled(): Boolean = BrandFlavourConstants.PROVIDE_AI_PLANNER_FEATURE &&
        askoRemoteConfig.isAiPlannerEnabled

    override fun getTidesQueryParameters(): TidesQueryParameters = askoRemoteConfig.tidesQueryParameters
}
