package com.stt.android.remoteconfig.impl.asko

import com.stt.android.remoteconfig.impl.di.DefaultAskoRemoteConfig
import javax.inject.Inject
import kotlin.reflect.full.declaredMemberProperties

internal class AskoRemoteConfigDefaults @Inject constructor(
    @param:DefaultAskoRemoteConfig val remoteConfig: AskoRemoteConfigResponse,
) {
    init {
        verify()
    }

    private fun verify() {
        AskoRemoteConfigResponse::class.declaredMemberProperties.forEach {
            val property = it.get(remoteConfig) as? AskoRemoteConfigValueConditions<*>
            if (property != null && property.valueConditions.firstOrNull()?.value == null) {
                throw IllegalStateException("${this::class.java.name} defaults do not respect contract")
            }
        }
    }

    fun <T> getDefault(
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValueConditions<T>?
    ): T? = remoteConfig.getConditions()?.valueConditions?.firstOrNull()?.value

    companion object {
        @JvmStatic
        fun <T> singleCondition(value: T): AskoRemoteConfigValueConditions<T> =
            AskoRemoteConfigValueConditions(listOf(AskoValueConditions(value = value)))
    }
}
