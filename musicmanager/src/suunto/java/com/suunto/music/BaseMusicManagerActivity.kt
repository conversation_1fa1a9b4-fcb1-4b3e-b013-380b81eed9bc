package com.suunto.music

import android.os.Bundle
import androidx.activity.ComponentActivity
import com.stt.android.compose.util.setContentWithTheme
import com.suunto.music.viewmodel.BaseMusicManagerViewModel

abstract class BaseMusicManagerActivity : ComponentActivity() {

    abstract val musicManagerViewModel: BaseMusicManagerViewModel
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithTheme {
            OfflineMusicNavGraph(
                musicManagerViewModel = musicManagerViewModel,
                onBackClick = {
                    finish()
                }
            )
        }
    }
}
