package com.suunto.music

import com.suunto.music.model.MusicPlayModel
import com.suunto.music.model.MusicPlayState
import com.suunto.music.model.PlayList
import com.suunto.music.model.PlayStateUpdateDetail
import com.suunto.music.model.PlayingSong
import com.suunto.music.model.PlaylistDetail
import com.suunto.music.model.SongDetail
import com.suunto.music.model.SongOffsetKey
import io.reactivex.Observable

interface MusicManagerController {
    suspend fun getPlaylists(forcedLoadFromDevice: Boolean): List<PlayList>

    suspend fun updatePlaylists(playList: PlayList)

    suspend fun createPlayList(playlistDetail: PlaylistDetail): Pair<Boolean, PlaylistDetail>

    suspend fun deletePlayList(id: String): Boolean

    suspend fun updatePlayListDetail(playlistDetail: PlaylistDetail): Boolean

    suspend fun getPlayListDetail(
        path: String,
        playlistId: String,
        forcedLoadFromHeadset: Boolean = false
    ): PlaylistDetail?

    suspend fun getPlayListSongs(offsetKeys: List<SongOffsetKey>): List<SongDetail>

    suspend fun updatePlayModel(musicPlayModel: MusicPlayModel): Boolean

    suspend fun updatePlayState(musicPlayState: MusicPlayState): Boolean

    suspend fun skipToNext(): Boolean

    suspend fun skipToPrevious(): Boolean

    suspend fun getLastPlayingSong(): PlayingSong

    suspend fun getSongDetailByKey(key: String): SongDetail?

    suspend fun subscribeMusicPlayState(): Observable<PlayStateUpdateDetail>

    suspend fun unsubscribeMusicPlayState()

    suspend fun allSongChanged(fileStatus: Int): Boolean

    suspend fun getFileStatus(): Int

    suspend fun playSong(sortId: Int, playlistId: String, songDetail: SongDetail): Boolean

    suspend fun updateLocalData(allSongPlaylistDetail: PlaylistDetail)
}
