package com.suunto.music.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.suunto.music.R
import com.suunto.music.model.MusicControlType
import com.suunto.music.model.MusicPlayModel
import com.suunto.music.model.MusicPlayState
import com.suunto.music.model.PlayingSong

@Composable
fun PlayingSongController(
    playingSong: PlayingSong,
    playControl: (MusicControlType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.height(131.dp),
        shape = MaterialTheme.shapes.bottomSheetShape,
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colors.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = MaterialTheme.spacing.medium)
    ) {
        Column(
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        ) {
            val eventThrottler = rememberEventThrottler(throttleMillis = 1000)
            Text(
                text = playingSong.songDetail?.name
                    ?: stringResource(id = R.string.not_playing),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyXLargeBold,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = playingSong.songDetail?.author ?: "",
                color = MaterialTheme.colors.darkGrey,
                style = MaterialTheme.typography.body
            )
            val playingSongEnabled = playingSong.songDetail != null
            Row(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                IconButton(onClick = {
                    eventThrottler.processEvent {
                        playControl.invoke(
                            MusicControlType.CHANGE_PLAY_MODEL
                        )
                    }
                }, enabled = playingSongEnabled) {
                    Icon(
                        painter = painterResource(id = getPlayModeIconResId(playingSong.musicPlayModel)),
                        contentDescription = "",
                        tint = Color.Unspecified.copy(alpha = if (playingSongEnabled) 0f else 0.2f)
                    )
                }
                IconButton(
                    onClick = {
                        eventThrottler.processEvent {
                            playControl.invoke(
                                MusicControlType.SKIP_TO_PREVIOUS_SONG
                            )
                        }
                    },
                    enabled = playingSongEnabled
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_previous),
                        contentDescription = "",
                        tint = Color.Unspecified.copy(alpha = if (playingSongEnabled) 0f else 0.2f)
                    )
                }
                IconButton(
                    onClick = {
                        eventThrottler.processEvent {
                            playControl.invoke(
                                MusicControlType.PLAY_OR_PAUSE
                            )
                        }
                    },
                    enabled = playingSongEnabled
                ) {
                    Icon(
                        painter = painterResource(id = getPlayStateIconResId(playingSong.musicPlayState)),
                        contentDescription = "",
                        tint = Color.Unspecified
                    )
                }
                IconButton(
                    onClick = {
                        eventThrottler.processEvent {
                            playControl.invoke(
                                MusicControlType.SKIP_TO_NEXT_SONG
                            )
                        }
                    },
                    enabled = playingSongEnabled
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_next),
                        contentDescription = "",
                        tint = Color.Unspecified.copy(alpha = if (playingSongEnabled) 0f else 0.2f)
                    )
                }
                // placeholder to make play icon in center position
                IconButton(
                    onClick = {},
                    enabled = false,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_next),
                        contentDescription = "",
                        tint = Color.Transparent
                    )
                }
            }
        }
    }
}

private fun getPlayModeIconResId(musicPlayModel: MusicPlayModel): Int {
    return when (musicPlayModel) {
        MusicPlayModel.RANDOM_PLAY -> R.drawable.icon_random_play
        MusicPlayModel.SEQUENCE -> R.drawable.icon_repeat_playlist
        MusicPlayModel.REPEAT_SONG -> R.drawable.icon_repeat_song
    }
}

private fun getPlayStateIconResId(musicPlayState: MusicPlayState): Int {
    return when (musicPlayState) {
        MusicPlayState.PLAYING -> R.drawable.icon_play
        MusicPlayState.PAUSE -> R.drawable.icon_pause_1
    }
}

@Composable
@Preview
private fun PlayMusicControllerPreview() {
    PlayingSongController(
        playingSong = PlayingSong(),
        playControl = {},
    )
}
