package com.stt.android.data.activitydata.dailyvalues

import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.suunto.algorithms.data.Energy.Companion.joules
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class ActivityDataDailyRepositoryTest {
    @Mock
    private lateinit var activityDataDailyRemoteDataSource: ActivityDataDailyRemoteDataSource

    @Mock
    private lateinit var activityDataDailyLocalDataSource: ActivityDataDailyLocalDataSource

    private lateinit var repository: ActivityDataDailyRepository

    @Before
    fun setup() {
        repository = ActivityDataDailyRepositoryImpl(
            activityDataDailyLocalDataSource,
            activityDataDailyRemoteDataSource
        )
    }

    // STEPS

    @Test
    fun `should fetch steps from local and remote and then save locally`() = runTest {
        // prepare
        whenever(activityDataDailyLocalDataSource.fetchSteps())
            .thenReturn(LOCAL_FETCH_VALUE)
        whenever(activityDataDailyRemoteDataSource.fetchSteps())
            .thenReturn(REMOTE_FETCH_VALUE)
        doNothing().whenever(activityDataDailyLocalDataSource).saveSteps(REMOTE_FETCH_VALUE)
        // verify
        assertEquals(
            listOf(LOCAL_FETCH_VALUE, REMOTE_FETCH_VALUE),
            repository.fetchSteps().toList(),
        )
        verify(activityDataDailyLocalDataSource, times(1)).fetchSteps()
        verify(activityDataDailyRemoteDataSource).fetchSteps()
        verify(activityDataDailyLocalDataSource).saveSteps(REMOTE_FETCH_VALUE)
    }

    @Test
    fun `should emit steps value only once if local value and remote value are same`() = runTest {
        // prepare
        whenever(activityDataDailyLocalDataSource.fetchSteps())
            .thenReturn(REMOTE_FETCH_VALUE)
        whenever(activityDataDailyRemoteDataSource.fetchSteps())
            .thenReturn(REMOTE_FETCH_VALUE)
        doNothing().whenever(activityDataDailyLocalDataSource).saveSteps(REMOTE_FETCH_VALUE)
        // verify
        assertEquals(
            listOf(REMOTE_FETCH_VALUE),
            repository.fetchSteps().toList(),
        )
        verify(activityDataDailyLocalDataSource, times(1)).fetchSteps()
        verify(activityDataDailyRemoteDataSource).fetchSteps()
        verify(activityDataDailyLocalDataSource).saveSteps(REMOTE_FETCH_VALUE)
    }

    @Test
    fun `should fetch steps from only local when error occurs on remote`() = runTest {
        // prepare
        whenever(activityDataDailyRemoteDataSource.fetchSteps())
            .thenThrow(RuntimeException())
        whenever(activityDataDailyLocalDataSource.fetchSteps())
            .thenReturn(LOCAL_FETCH_VALUE)
        // verify
        assertEquals(
            listOf(LOCAL_FETCH_VALUE),
            repository.fetchSteps().toList(),
        )
        verify(activityDataDailyLocalDataSource, times(2)).fetchSteps()
        verify(activityDataDailyRemoteDataSource).fetchSteps()
    }

    // ENERGY

    @Test
    fun `should fetch energy from local and remote and then save locally`() = runTest {
        // prepare
        whenever(activityDataDailyLocalDataSource.fetchEnergy())
            .thenReturn(LOCAL_FETCH_VALUE)
        whenever(activityDataDailyRemoteDataSource.fetchEnergy())
            .thenReturn(REMOTE_FETCH_VALUE)
        doNothing().whenever(activityDataDailyLocalDataSource).saveEnergy(REMOTE_FETCH_VALUE)
        // verify
        assertEquals(
            listOf(LOCAL_FETCH_VALUE.joules, REMOTE_FETCH_VALUE.joules),
            repository.fetchEnergy().toList(),
        )
        verify(activityDataDailyLocalDataSource, times(1)).fetchEnergy()
        verify(activityDataDailyRemoteDataSource).fetchEnergy()
        verify(activityDataDailyLocalDataSource).saveEnergy(REMOTE_FETCH_VALUE)
    }

    @Test
    fun `should emit energy value only once if local value and remote value are same`() = runTest {
        // prepare
        whenever(activityDataDailyLocalDataSource.fetchEnergy())
            .thenReturn(REMOTE_FETCH_VALUE)
        whenever(activityDataDailyRemoteDataSource.fetchEnergy())
            .thenReturn(REMOTE_FETCH_VALUE)
        doNothing().whenever(activityDataDailyLocalDataSource).saveEnergy(REMOTE_FETCH_VALUE)
        // verify
        assertEquals(listOf(REMOTE_FETCH_VALUE.joules), repository.fetchEnergy().toList())
        verify(activityDataDailyLocalDataSource, times(1)).fetchEnergy()
        verify(activityDataDailyRemoteDataSource).fetchEnergy()
        verify(activityDataDailyLocalDataSource).saveEnergy(REMOTE_FETCH_VALUE)
    }

    @Test
    fun `should fetch energy from only local when error occurs on remote`() = runTest {
        // prepare
        whenever(activityDataDailyRemoteDataSource.fetchEnergy())
            .thenThrow(RuntimeException())
        whenever(activityDataDailyLocalDataSource.fetchEnergy())
            .thenReturn(LOCAL_FETCH_VALUE)
        // verify
        assertEquals(listOf(LOCAL_FETCH_VALUE.joules), repository.fetchEnergy().toList())
        verify(activityDataDailyLocalDataSource, times(2)).fetchEnergy()
        verify(activityDataDailyRemoteDataSource).fetchEnergy()
    }

    @Test
    fun `fetch metabolic energy should access local and remote data source`() = runTest {
        // prepare
        val valueRemote = 20000
        val valueLocal = 10000
        whenever(activityDataDailyRemoteDataSource.fetchMetabolicEnergy())
            .thenReturn(valueRemote)
        whenever(activityDataDailyLocalDataSource.fetchMetabolicEnergy())
            .thenReturn(valueLocal)

        // verify
        repository.fetchMetabolicEnergy()
            .collect()
        verify(activityDataDailyRemoteDataSource).fetchMetabolicEnergy()
        verify(activityDataDailyLocalDataSource, times(1)).fetchMetabolicEnergy()
    }

    @Test
    fun `fetch metabolic energy should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataDailyRemoteDataSource.fetchMetabolicEnergy())
            .thenThrow(RuntimeException())
        whenever(activityDataDailyLocalDataSource.fetchMetabolicEnergy())
            .thenReturn(10000)
        // verify
        repository.fetchMetabolicEnergy()
            .collect()
        verify(activityDataDailyRemoteDataSource).fetchMetabolicEnergy()
        verify(activityDataDailyLocalDataSource, times(2)).fetchMetabolicEnergy()
    }

    companion object {
        private const val REMOTE_FETCH_VALUE = 10000
        private const val LOCAL_FETCH_VALUE = 5000
    }
}
