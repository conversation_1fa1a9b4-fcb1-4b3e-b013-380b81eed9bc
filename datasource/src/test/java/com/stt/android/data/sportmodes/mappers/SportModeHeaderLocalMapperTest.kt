package com.stt.android.data.sportmodes.mappers

import com.stt.android.moshi.buildBasicMoshi
import io.reactivex.Single
import org.junit.Before
import org.junit.Test

class SportModeHeaderLocalMapperTest {
    private val moshi = buildBasicMoshi()
    private lateinit var mapper: SportModeHeaderLocalMapper

    @Before
    fun setup() {
        mapper = SportModeHeaderLocalMapper(moshi)
    }

    @Test
    fun toDomainEntity() {
        val json = javaClass.classLoader?.getResourceAsStream("samples/sportmodes/sport_modes_current_modes_mock_response.json")
            ?.bufferedReader()
            .use { it?.readText() }

        Single.just(json)
            .map(mapper.toDomainEntity())
            .test()
            .assertNoErrors()
            .assertValue { list -> list.size == 18 }
    }
}
