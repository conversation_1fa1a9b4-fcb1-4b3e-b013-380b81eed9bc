package com.stt.android.data.sportmodes

import com.stt.android.data.source.local.sportmodes.SportModesFileStorage
import com.stt.android.data.source.local.sportmodes.SportModesSharedPrefStorage
import com.stt.android.data.sportmodes.component.SportModeComponent
import com.stt.android.data.sportmodes.mappers.SportModeActivityHeaderLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeDisplayLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeDisplaySectionLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeFieldLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeFieldSectionLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeHeaderLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeIdsLocalMapper
import com.stt.android.data.sportmodes.mappers.SportModeSettingsMapper
import com.stt.android.data.sportmodes.mappers.SportModeTemplateMapper
import com.stt.android.domain.sportmodes.WatchInfo
import com.stt.android.moshi.buildBasicMoshi
import io.reactivex.Completable
import io.reactivex.Single
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class SportModesLocalDataSourceTest {
    @Mock
    private lateinit var sportModeComponentManager: SportModeComponentManager

    @Mock
    private lateinit var sportModeComponent: SportModeComponent

    @Mock
    private lateinit var sportModesSharedPrefStorage: SportModesSharedPrefStorage

    @Mock
    private lateinit var sportModesFileStorage: SportModesFileStorage

    private lateinit var sportModesLocalDataSource: SportModesLocalDataSource
    private val moshi = buildBasicMoshi()
    private val sportModeActivityHeaderLocalMapper = SportModeActivityHeaderLocalMapper(moshi)
    private val sportModeDisplayFieldLocalMapper = SportModeFieldLocalMapper(moshi)
    private val sportModeHeaderLocalMapper = SportModeHeaderLocalMapper(moshi)
    private val sportModeDisplayLocalMapper = SportModeDisplayLocalMapper(moshi)
    private val sportModeTemplateMapper = SportModeTemplateMapper(moshi)
    private val sportModeFieldSectionLocalMapper = SportModeFieldSectionLocalMapper(moshi)
    private val sportModeDisplaySectionLocalMapper = SportModeDisplaySectionLocalMapper(moshi)
    private val sportModeSettingsMapper = SportModeSettingsMapper(moshi)
    private val sportModeIdsLocalMapper = SportModeIdsLocalMapper(moshi)

    @Before
    fun setup() {
        sportModesLocalDataSource = SportModesLocalDataSource(
            sportModeComponentManager,
            sportModesSharedPrefStorage,
            sportModesFileStorage,
            sportModeActivityHeaderLocalMapper,
            sportModeIdsLocalMapper,
            sportModeDisplayFieldLocalMapper,
            sportModeHeaderLocalMapper,
            sportModeDisplayLocalMapper,
            sportModeTemplateMapper,
            sportModeDisplaySectionLocalMapper,
            sportModeFieldSectionLocalMapper,
            sportModeSettingsMapper
        )
        whenever(sportModeComponentManager.sportModeComponent).thenReturn(sportModeComponent)
    }

    @Test
    fun `fetch device supported accesses sport mode component`() {
        `when`(sportModeComponent.isDeviceSupported())
            .thenReturn(1.0)
        sportModesLocalDataSource.fetchDeviceSupported()
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).isDeviceSupported()
    }

    @Test
    fun `fetch sport modes fte completed should access shared prefs storage`() {
        `when`(sportModesSharedPrefStorage.fetchFteCompleted()).thenReturn(Single.just(false))
        sportModesLocalDataSource.fetchSportModeFteCompleted()
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesSharedPrefStorage).fetchFteCompleted()
    }

    @Test
    fun `set sport modes fte completed should access shared prefs storage`() {
        `when`(sportModesSharedPrefStorage.setFteCompleted(anyBoolean())).thenReturn(Completable.complete())
        sportModesLocalDataSource.setSportModeFteCompleted(anyBoolean())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesSharedPrefStorage).setFteCompleted(anyBoolean())
    }

    @Test
    fun `initSportModeComponent should access sport mode component`() = runTest {
        sportModesLocalDataSource.initSportModeComponent("", WatchInfo("", "", ""))
        verify(sportModeComponentManager).initialize("", "", "", "")
        verify(sportModeComponent).initialize("", "", "")
    }

    @Test
    fun `fetch sport modes should access sport mode component`() {
        `when`(sportModeComponent.getSportModes()).thenReturn("")
        sportModesLocalDataSource.fetchSportModes()
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getSportModes()
    }

    @Test
    fun `fetch max displays should access sport mode component`() {
        `when`(sportModeComponent.getMaxNumberOfDisplays(anyString())).thenReturn(1.0)
        sportModesLocalDataSource.fetchMaxNumberOfDisplays("")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getMaxNumberOfDisplays("")
    }

    @Test
    fun `fetch min displays should access sport mode component`() {
        `when`(sportModeComponent.getMinNumberOfDisplays(anyString())).thenReturn(1.0)
        sportModesLocalDataSource.fetchMinNumberOfDisplays("")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getMinNumberOfDisplays("")
    }

    @Test
    fun `fetchSportModeTemplate should access sport mode component`() {
        // TODO figure out whether it possible to parse it without usage of Android framework
//        `when`(sportModeComponent.getSportModeTemplate(anyInt(), anyString())).thenReturn("")
//
//        sportModesLocalDataSource.fetchSportModeTemplate(1, "")
//                .test()
//                .assertNoErrors()
//                .assertComplete()
//        verify(sportModeComponent).getSportModeTemplate(anyInt(), anyString())
    }

    @Test
    fun `fetchCurrentSportModesList should access sport modes component`() {
        `when`(sportModeComponent.getCurrentSportModes(anyString())).thenReturn("")

        sportModesLocalDataSource.fetchCurrentSportModesList("")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getCurrentSportModes("")
    }

    @Test
    fun `fetchDisplays should access sport modes component`() {
        `when`(sportModeComponent.getCurrentDisplays(anyString(), anyString())).thenReturn("")

        sportModesLocalDataSource.fetchCurrentDisplays("", "")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getCurrentDisplays("", "")
    }

    @Test
    fun `fetch field list should access sport mode component`() {
        `when`(sportModeComponent.getFields(anyInt(), anyString(), anyInt())).thenReturn("")
        sportModesLocalDataSource.fetchFieldList(0, "", 0)
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getFields(0, "", 0)
    }

    @Test
    fun `fetch display list should access sport mode component`() {
        `when`(sportModeComponent.getDisplays(anyString(), anyInt(), anyInt())).thenReturn("")
        sportModesLocalDataSource.fetchDisplayList("", 0, 0)
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getDisplays("", 0, 0)
    }

    @Test
    fun `change field should access sport mode component`() {
        // prepare
        `when`(sportModeComponent.changeField(anyString(), anyInt(), anyInt(), anyString()))
            .thenReturn("")
        // verify
        sportModesLocalDataSource.changeField("", 0, 0, "")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).changeField("", 0, 0, "")
    }

    @Test
    fun `fetch sport mode setting should access sport mode component`() {
        `when`(sportModeComponent.getSetting(anyString(), anyString(), anyString()))
            .thenReturn("")
        sportModesLocalDataSource.fetchSetting("", "", "")
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModeComponent).getSetting("", "", "")
    }

    @Test
    fun `fetch sport mode component etag should access shared pref storage`() {
        `when`(sportModesSharedPrefStorage.fetchLastSportModeComponentEtag())
            .thenReturn("")
        sportModesLocalDataSource.fetchLastSportModeComponentEtag()
        verify(sportModesSharedPrefStorage).fetchLastSportModeComponentEtag()
    }

    @Test
    fun `save sport mode component should access sport modes file and shared pref storages`() = runTest {
        `when`(sportModesFileStorage.saveSportModeComponent(any(), anyString()))
            .thenReturn(Unit)
        doNothing().whenever(sportModesSharedPrefStorage)
            .saveLastSportModeComponentEtag(anyString())

        sportModesLocalDataSource.saveSportModeComponent(Pair(null, ""))

        verify(sportModesFileStorage).saveSportModeComponent(any(), anyString())
        verify(sportModesSharedPrefStorage).saveLastSportModeComponentEtag(anyString())
    }

    @Test(expected = Exception::class)
    fun `save sport mode component will fail if save sport mode component to file system fails`() = runTest {
        `when`(sportModesFileStorage.saveSportModeComponent(any(), anyString()))
            .thenThrow(Exception())
        `when`(sportModesSharedPrefStorage.saveLastSportModeComponentEtag(anyString()))
            .thenReturn(Unit)

        sportModesLocalDataSource.saveSportModeComponent(Pair(null, ""))

        verify(sportModesFileStorage).saveSportModeComponent(any(), anyString())
        verify(sportModesSharedPrefStorage).saveLastSportModeComponentEtag(anyString())
    }

    @Test
    fun `fetch sport mode component exists should access file storage`() = runTest {
        `when`(sportModesFileStorage.fetchSportModeComponentExistsOnFileSystem())
            .thenReturn(true)

        sportModesLocalDataSource.fetchSportModeComponentExistsOnFileSystem()

        verify(sportModesFileStorage).fetchSportModeComponentExistsOnFileSystem()
    }
}
