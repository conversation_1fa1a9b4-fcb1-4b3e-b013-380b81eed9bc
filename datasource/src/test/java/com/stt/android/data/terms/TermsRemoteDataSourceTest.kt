package com.stt.android.data.terms

import com.stt.android.remote.terms.TermsRemoteApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertFalse

class TermsRemoteDataSourceTest {
    private lateinit var termsRemoteApi: TermsRemoteApi

    private lateinit var termsRemoteDataSource: TermsRemoteDataSource

    @Before
    fun setup() {
        termsRemoteApi = mock()
        termsRemoteDataSource = TermsRemoteDataSource(termsRemoteApi)
    }

    @Test
    fun `should access terms remote api when accepting terms`() = runTest {
        // Prepare
        whenever(termsRemoteApi.acceptTerms())
            .thenReturn(Unit)

        // Verify
        termsRemoteDataSource.acceptTerms()

        verify(termsRemoteApi).acceptTerms()
    }

    @Test
    fun `should access terms remote api when checking terms`() = runTest {
        // Prepare
        whenever(termsRemoteApi.needAcceptTerms())
            .thenReturn(false)

        // Verify
        assertFalse(termsRemoteDataSource.needAcceptTerms())

        verify(termsRemoteApi).needAcceptTerms()
    }
}
