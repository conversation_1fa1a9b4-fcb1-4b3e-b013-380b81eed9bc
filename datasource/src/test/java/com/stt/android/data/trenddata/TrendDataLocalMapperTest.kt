package com.stt.android.data.trenddata

import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.domain.trenddata.TrendData
import io.reactivex.Single
import org.junit.Test

class TrendDataLocalMapperTest {
    private val mapper = TrendDataLocalMapper()

    @Test
    fun `map TrendData entity list to DbTrendData entity list`() {
        val expected = listOf(trendDataMock)
        Single.just(expected)
            .map(mapper.toDataEntityList(LocalTrendData.STATUS_NOT_SYNCED))
            .test()
            .assertValue { actual -> areEntitiesEqual(actual.first(), expected.first()) }
    }

    @Test
    fun `map LocalTrendData entity list to TrendData entity list`() {
        val expected = listOf(localTrendDataMock)
        Single.just(expected)
            .map(mapper.toDomainEntityList())
            .test()
            .assertValue { actual -> areEntitiesEqual(expected.first(), actual.first()) }
    }

    private fun areEntitiesEqual(localTrendData: LocalTrendData, trendData: TrendData): Boolean {
        return trendData.serial == localTrendData.serial &&
            trendData.energy.inJoules.toFloat() == localTrendData.energy &&
            trendData.steps == localTrendData.steps &&
            trendData.timestamp / 1000 == localTrendData.timestampSeconds &&
            trendData.hr == localTrendData.heartrate &&
            trendData.timeISO8601 == localTrendData.timeISO8601 &&
            trendData.hrMin == localTrendData.hrMin &&
            trendData.hrMax == localTrendData.hrMax
    }
}
