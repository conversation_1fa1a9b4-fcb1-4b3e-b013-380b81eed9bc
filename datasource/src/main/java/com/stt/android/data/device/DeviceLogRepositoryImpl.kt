package com.stt.android.data.device

import com.stt.android.TestOpen
import com.stt.android.domain.device.DeviceLogRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import java.io.File
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class DeviceLogRepositoryModule {

    @Binds
    abstract fun bindDeviceLogRepositoryImpl(
        repositoryImpl: DeviceLogRepositoryImpl
    ): DeviceLogRepository
}

@TestOpen
class DeviceLogRepositoryImpl
@Inject constructor(
    private val watchDataSource: DeviceLogWatchDataSource,
    private val remoteDataSource: DeviceLogRemoteDataSource
) : DeviceLogRepository {
    override suspend fun getLogs(logType: Int): List<File> = watchDataSource.fetchLogs(logType)

    override suspend fun sendLogs(logFiles: List<File>, toFile: Boolean, remoteLogId: String) {
        remoteDataSource.sendLogs(logFiles, toFile, remoteLogId)
    }

    override suspend fun getHeadsetLogs(): List<File> = watchDataSource.getHeadsetLogs()
}
