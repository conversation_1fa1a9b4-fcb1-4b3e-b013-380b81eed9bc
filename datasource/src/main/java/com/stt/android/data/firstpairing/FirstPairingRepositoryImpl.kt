package com.stt.android.data.firstpairing

import com.stt.android.TestOpen
import com.stt.android.data.Local
import com.stt.android.domain.firstpairing.FirstPairingRepository
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.reactivex.Completable
import io.reactivex.Single
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class FirstPairingRepositoryModule {

    @Binds
    abstract fun bindFirstPairingRepositoryImpl(
        repositoryImpl: FirstPairingRepositoryImpl
    ): FirstPairingRepository
}

/**
 * Repository definition to fetch info about first time pairing related info
 */
@TestOpen
class FirstPairingRepositoryImpl
@Inject constructor(
    @Local private val firstPairingLocalDataSource: FirstPairingDataSource
) : FirstPairingRepository {
    override fun isFirstTimePairingAttempt(): Single<Boolean> {
        return firstPairingLocalDataSource.isFirstTimePairingAttempt()
    }

    override fun markFirstPairingAttemptAsDone(): Completable {
        return firstPairingLocalDataSource.markFirstPairingAttemptAsDone()
    }

    override fun isOnboardingEverShown(deviceType: SuuntoDeviceType): Single<Boolean> {
        return firstPairingLocalDataSource.hasOnboardingEverShown(deviceType)
    }

    override fun markOnboardingShownAtLeastOnce(deviceType: SuuntoDeviceType): Completable {
        return firstPairingLocalDataSource.markOnboardingShownAtLeastOnce(deviceType)
    }
}
