package com.stt.android.data.recovery

import com.stt.android.data.source.local.recovery.LocalRecoveryData.Companion.STATUS_NOT_SYNCED
import com.stt.android.data.source.local.recovery.RecoveryDataDao
import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.domain.recovery.RecoveryData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.Instant
import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class RoomRecoveryDataLocalDataSource
@Inject constructor(
    private val recoveryDataDao: RecoveryDataDao,
    private val recoveryDataLocalMapper: RecoveryDataLocalMapper
) : RecoveryDataLocalDataSource {
    override suspend fun fetchRecoveryDataForBackendSync(): List<RecoveryData> {
        return recoveryDataLocalMapper.toDomainEntityList()(
            recoveryDataDao.fetchRecoveryDataByStatus(STATUS_NOT_SYNCED)
        )
    }

    override fun fetchRecoveryDataForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long
    ): Flow<List<RecoveryData>> {
        val mapper = recoveryDataLocalMapper.toDomainEntityList()
        val fromTimestampSeconds = TimeUnit.MILLISECONDS.toSeconds(fromTimestamp)
        val toTimestampSeconds = TimeUnit.MILLISECONDS.toSeconds(toTimestamp)
        Timber.d("### fetchRecoveryDataForDateRange: ${Instant.ofEpochSecond(fromTimestampSeconds)} - ${Instant.ofEpochSecond(toTimestampSeconds)}")
        return recoveryDataDao.fetchRecoveryDataBetween(fromTimestampSeconds, toTimestampSeconds)
            .map { mapper(it) }
    }

    override suspend fun saveRecoveryData(
        recoveryDataList: List<RecoveryData>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean {
        val syncedStatus = if (requireBackendSync) {
            LocalTrendData.STATUS_NOT_SYNCED
        } else {
            LocalTrendData.STATUS_SYNCED
        }

        val localItems = recoveryDataLocalMapper.toDataEntityList(syncedStatus)(recoveryDataList)
        return if (replaceConflicts) {
            recoveryDataDao.insertRecoveryData(localItems)
            localItems.isNotEmpty()
        } else {
            val rowIds = recoveryDataDao.insertRecoveryDataSafe(localItems)
            rowIds.any { it != -1L }
        }
    }

    override suspend fun getLatestSyncedTimestamp(): ZonedDateTime? =
        recoveryDataDao.fetchLatestSynced()?.timeISO8601

    override suspend fun getRecoveryDataExists(): Boolean =
        recoveryDataDao.isRecoveryDataAvailable() > 0

    override suspend fun fetchNumDaysWithRecoveryData(checkLimit: Int): Int =
        recoveryDataDao.fetchNumDaysWithRecoveryData(checkLimit)
}
