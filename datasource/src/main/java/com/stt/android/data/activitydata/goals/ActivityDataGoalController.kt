package com.stt.android.data.activitydata.goals

interface ActivityDataGoalController {
    suspend fun getStepsGoal(): Int

    suspend fun setStepsGoal(stepsTarget: Int)

    suspend fun getEnergyGoal(): Int

    suspend fun setEnergyGoal(energyTarget: Int)

    suspend fun getSleepGoal(): Int

    suspend fun setSleepGoal(sleepTarget: Int)

    suspend fun getBedtimeStart(): Int

    suspend fun getBedtimeEnd(): Int

    suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int)

    suspend fun setBedtimeStart(bedtimeStart: Int)

    suspend fun setBedtimeEnd(bedtimeEnd: Int)
}
