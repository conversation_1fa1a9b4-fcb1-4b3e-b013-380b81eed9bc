plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
    id "stt.android.plugin.moshi"
}

android {
    namespace 'com.stt.android.home.explore'
    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.appBase)
    implementation project(Deps.core)
    implementation project(Deps.datasource)
    implementation project(Deps.domain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.exploreDomain)
    implementation project(Deps.exploreDatasource)
    implementation project(Deps.persistence)
    implementation project(Deps.maps)
    implementation project(Deps.analytics)
    implementation project(Deps.mapsProviderMapbox)
    chinaImplementation project(Deps.mapsProviderAmap)
    implementation project(Deps.infoModel)
    implementation project(Deps.diary)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.remote)
    implementation project(Deps.remoteConfigApi)
    implementation project(Deps.featureToggleApi)

    implementation libs.androidx.swiperefreshlayout
    suuntoImplementation project(Deps.timeline)
    suuntoImplementation project(Deps.connectivity)
    suuntoImplementation project(Deps.watchdebug)

    implementation libs.mpandroid
    implementation libs.play.maps
    implementation libs.maps.utils
    implementation libs.mapbox
    implementation libs.mapbox.search
    implementation libs.easypermissions
    implementation libs.gpxparser
    implementation libs.androidfab
    implementation libs.gson
    implementation libs.rxjava2.android
    implementation libs.simple.tooltip
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }

    implementation libs.soy.algorithms
    implementation libs.coil
    implementation libs.androidx.work
    implementation libs.androidx.browser
    implementation libs.rangeseekbar

    suuntoImplementation libs.lottie
    suuntoImplementation libs.lottie.compose

    suuntoImplementation libs.okhttp
    suuntoImplementation libs.retrofit
}
