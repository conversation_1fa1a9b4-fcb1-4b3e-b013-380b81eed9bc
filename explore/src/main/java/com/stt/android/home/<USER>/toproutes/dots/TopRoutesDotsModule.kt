package com.stt.android.home.explore.toproutes.dots

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.hilt.android.scopes.FragmentScoped

@Module
@InstallIn(FragmentComponent::class)
abstract class TopRoutesDotsModule {
    @Binds
    @FragmentScoped
    abstract fun bindController(
        controller: TopRoutesDotsController
    ): ViewStateEpoxyController<TopRoutesDotsContainer?>
}
