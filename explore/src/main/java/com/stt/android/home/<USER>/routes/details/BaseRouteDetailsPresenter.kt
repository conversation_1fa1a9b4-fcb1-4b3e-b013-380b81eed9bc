package com.stt.android.home.explore.routes.details

import android.app.Activity
import android.content.SharedPreferences
import android.widget.Toast
import com.google.android.gms.maps.model.LatLng
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.ShareBroadcastReceiver
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.Point
import com.stt.android.domain.routes.DeleteRouteUseCase
import com.stt.android.domain.routes.GetRouteUseCase
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.RouteVerticalDeltaCalc
import com.stt.android.domain.routes.ShareRouteUseCase
import com.stt.android.domain.routes.UpdateAverageSpeedForRouteUseCase
import com.stt.android.domain.routes.WaypointTools
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.RouteAnalytics
import com.stt.android.home.explore.routes.RouteUtils
import com.stt.android.home.explore.routes.WaypointDetails
import com.stt.android.home.explore.routes.WaypointDetailsItem
import com.stt.android.home.explore.routes.planner.waypoints.WaypointUtils
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.presenters.BasePresenter
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import kotlin.math.roundToLong
import com.stt.android.R as BaseR

abstract class BaseRouteDetailsPresenter(
    private val userSettingsController: UserSettingsController,
    protected val currentUserController: CurrentUserController,
    private val getRouteUseCase: GetRouteUseCase,
    private val updateAverageSpeedForRouteUseCase: UpdateAverageSpeedForRouteUseCase,
    private val routeAnalytics: RouteAnalytics,
    private val deleteRouteUseCase: DeleteRouteUseCase,
    private val shareRouteUseCase: ShareRouteUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val dispatchers: CoroutinesDispatchers,
    private val waypointTools: WaypointTools
) : BasePresenter<RouteDetailsView>() {
    data class RouteDetailsViewData(
        val route: Route,
        val routeAltitudeChartData: RouteAltitudeChartData,
        val climbGuidance: ClimbGuidance,
        val waypointDetailsItems: List<WaypointDetailsItem>,
        val watchRouteListFull: Boolean,
        val turnByTurnEnabled: Boolean
    )

    private val exceptionHandler = CoroutineExceptionHandler { _, throwable -> Timber.w(throwable) }

    private val _routeDetailsViewData = MutableStateFlow<RouteDetailsViewData?>(null)
    val routeDetailsViewData: Flow<RouteDetailsViewData> =
        _routeDetailsViewData.asStateFlow().filterNotNull()

    protected val route: Route? get() = _routeDetailsViewData.value?.route
    val isWatchRouteListFull: Boolean get() = _routeDetailsViewData.value?.watchRouteListFull ?: false

    private val measurementUnit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    fun loadRouteDetails(routeId: String) {
        view?.getLifecycleScope()?.launch(exceptionHandler) {
            loadRouteDetailsInternal(routeId)
        }
    }

    fun setTurnByTurnEnabled(routeId: String, isVisible: Boolean) {
        view?.getLifecycleScope()?.launch {
            val originalRoute = withContext(dispatchers.io) {
                getRouteUseCase.getRoute(routeId)
                    ?: error("No route with ID: $routeId")
            }
            val waypointDetailsItems = getWaypointItems(originalRoute, isVisible)
            _routeDetailsViewData.update {
                _routeDetailsViewData.value?.copy(waypointDetailsItems = waypointDetailsItems, turnByTurnEnabled = isVisible)
            }
        }
    }

    private suspend fun loadRouteDetailsInternal(routeId: String) = withContext(dispatchers.io) {
        val watchRouteListFull = async(dispatchers.io) { isWatchRouteListFull() }

        val originalRoute = getRouteUseCase.getRoute(routeId) ?: throw IllegalArgumentException("No route with ID: $routeId")

        val chartData = async(dispatchers.computation) {
            RouteUtils.calculateAltitudeChartData(
                originalRoute.segments,
                measurementUnit
            )
        }

        val waypointDetailsItems = async(dispatchers.computation) {
            getWaypointItems(originalRoute, originalRoute.turnWaypointsEnabled)
        }

        val points = originalRoute.segments.flatMap { it.routePoints }
        val totalDistance = async(dispatchers.computation) {
            RouteUtils.calculateDistanceByPoints(points)
        }
        val climbGuidance = async(dispatchers.computation) {
            calculateClimbGuidance(points)
        }
        val verticalDelta = RouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(points)

        val route = originalRoute.copy(
            ascent = verticalDelta?.ascent ?: 0.0,
            descent = verticalDelta?.descent ?: 0.0,
            totalDistance = totalDistance.await()
        )

        _routeDetailsViewData.value = RouteDetailsViewData(
            route = route,
            routeAltitudeChartData = chartData.await(),
            climbGuidance = climbGuidance.await(),
            waypointDetailsItems = waypointDetailsItems.await(),
            watchRouteListFull = watchRouteListFull.await(),
            turnByTurnEnabled = route.turnWaypointsEnabled
        )

        if (view != null) {
            routeAnalytics.trackRouteDetailsScreenAnalytics(route, verticalDelta?.ascent ?: 0.0)
        }
    }

    private suspend fun getWaypointItems(
        originalRoute: Route,
        turnByTurnEnabled: Boolean
    ): List<WaypointDetailsItem> = withContext(dispatchers.computation) {
        RouteUtils.splitAtWaypointsInclusive(originalRoute.segments, waypointTools)
            .map { it.toViewItem() }
    }

    abstract suspend fun calculateClimbGuidance(points: List<Point>): ClimbGuidance

    private fun WaypointDetails.toViewItem(): WaypointDetailsItem =
        WaypointDetailsItem(
            formattedCurrentDistance = infoModelFormatter.formatValue(
                SummaryItem.DISTANCE,
                currentDistance
            ),
            latLng = latLng,
            waypointType = waypointType?.let { WaypointType.from(it) },
            waypointName = waypointName,
            formattedDistanceSummary = distanceSummary?.let {
                infoModelFormatter.formatValue(
                    SummaryItem.DISTANCE,
                    it
                )
            },
            formattedAscentSummary = ascentSummary?.let {
                infoModelFormatter.formatValue(
                    SummaryItem.ASCENTALTITUDE,
                    it
                )
            },
            formattedDescentSummary = descentSummary?.let {
                infoModelFormatter.formatValue(
                    SummaryItem.ASCENTALTITUDE,
                    it
                )
            }
        )

    fun deleteRoute() {
        val route = route ?: return

        view?.getLifecycleScope()?.launch {
            runSuspendCatching {
                deleteRouteUseCase.deleteRoute(route)
                routeAnalytics.trackRouteDelete(route, true, currentUserController.username)
            }.onSuccess {
                view?.onRouteDeleted(true)
            }.onFailure { e ->
                Timber.w(e, "Deleting route failed")
                view?.onRouteDeleted(false)
            }
        }
    }

    /**
     * This method is called only for Analytics to track cancelled route delete event
     */
    fun cancelRouteDeletion() {
        val route = route ?: return
        view?.getLifecycleScope()?.launch(exceptionHandler) {
            routeAnalytics.trackRouteDelete(route, false, currentUserController.username)
                .await()
        }
    }

    fun shareRoute(activity: Activity) {
        if (!ANetworkProvider.isOnline()) {
            Toast.makeText(
                activity.applicationContext,
                BaseR.string.network_disabled_enable,
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        val routeId = route?.id ?: return
        view?.getLifecycleScope()?.launch(exceptionHandler) {
            // Get route again, because it may have been synced to backend and
            // route key may have changed.
            val routeKey = getRouteUseCase.getRoute(routeId)
                ?.key
                ?.takeIf { it.isNotBlank() }
                ?: run {
                    Toast.makeText(
                        activity.applicationContext,
                        BaseR.string.route_can_not_be_shared_right_now,
                        Toast.LENGTH_SHORT
                    ).show()
                    return@launch
                }
            val link = shareRouteUseCase.shareRoute(routeKey)
            ShareBroadcastReceiver.shareRoute(
                activity,
                link,
                AnalyticsPropertyValue.ExportSource.ROUTE_DETAILS_SCREEN
            )
        }
    }

    fun startEditingSpeed(infoModelFormatter: InfoModelFormatter) {
        val route = route ?: return
        view?.showEditSpeedDialog(
            infoModelFormatter.formatValue(SummaryItem.AVGSPEED, route.averageSpeed).value ?: "",
            measurementUnit.speedUnit
        )
    }

    fun saveSpeedToRoute(metersPerSeconds: Double) {
        val routeId = route?.id ?: return

        view?.getLifecycleScope()?.launch {
            runSuspendCatching {
                withContext(dispatchers.io) {
                    updateAverageSpeedForRouteUseCase.updateAverageSpeed(routeId, metersPerSeconds)

                    getRouteUseCase.getRoute(routeId) ?: throw IllegalArgumentException("No route with ID: $routeId")
                }
            }.onSuccess { updatedRoute ->
                _routeDetailsViewData.update {
                    it?.copy(route = updatedRoute)
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to update route speed")
                view?.showSpeedUpdatedFailed()
            }
        }
    }

    fun findWaypointsOnRoute(from: LatLng) {
        val route = route ?: return
        view?.getLifecycleScope()?.launch(exceptionHandler) {
            val waypoints = withContext(dispatchers.computation) {
                WaypointUtils.findWaypointsOnRoute(from, route.segments)
            }
            view?.onFindWaypointsCompleted(waypoints)
        }
    }

    abstract suspend fun isWatchRouteListFull(): Boolean

    abstract fun isSubscribedToPremium(): Boolean

    fun convertRoutePointsByIndex(
        chartData: RouteAltitudeChartData,
        climbGuidance: ClimbGuidance,
        index: Int,
        avgSpeed: Double,
    ) {
        val route = route ?: return

        view?.getLifecycleScope()?.launch(exceptionHandler) {
            val (selectPoints, segmentIndex, pointIndex) = withContext(dispatchers.computation) {
                val indexSelect = chartData.entries.indexOfLast { it.x <= index }
                val endIndex = chartData.indexList[indexSelect]
                val routePoints = route.segments.flatMap { it.routePoints }
                val points = routePoints.subList(0, endIndex + 1)
                var segmentIndex = 0
                var pointIndex = 0
                var pointCount = 0
                while (segmentIndex < climbGuidance.segments.size) {
                    val count = climbGuidance.segments[segmentIndex].points.size
                    if (pointCount + count > endIndex) {
                        pointIndex = endIndex - pointCount
                        break
                    }
                    // segment's last point is the same as next segment's first point
                    pointCount += (count - 1)
                    segmentIndex++
                }
                Triple(points, segmentIndex, pointIndex)
            }

            updateAllDataDisplayed(selectPoints, avgSpeed)
            view?.highlightRouteByIndex(climbGuidance, segmentIndex, pointIndex)
        }
    }

    fun convertRoutePointsByIndex(
        chartData: RouteAltitudeChartData,
        index: Int,
        avgSpeed: Double,
    ) {
        val route = route ?: return

        view?.getLifecycleScope()?.launch(exceptionHandler) {
            val routeSegments = route.segments
            val (selectPoints, unselectPoints) = withContext(dispatchers.computation) {
                val indexSelect = chartData.entries.indexOfLast { it.x <= index }
                val endIndex = chartData.indexList[indexSelect]
                val routePoints = routeSegments.flatMap { it.routePoints }
                val selectPoints = routePoints.subList(0, endIndex + 1)
                val unselectPoints = routePoints.subList(endIndex, routePoints.size)
                Pair(selectPoints, unselectPoints)
            }

            updateAllDataDisplayed(selectPoints, avgSpeed)
            view?.highlightRouteByIndex(selectPoints, unselectPoints)
        }
    }

    private fun updateAllDataDisplayed(points: List<Point>, avgSpeed: Double) {
        view?.getLifecycleScope()?.launch(exceptionHandler) {
            val distanceAndDuration = async(Dispatchers.IO) {
                val distance = RouteUtils.calculateDistanceByPoints(points)
                val duration = if (avgSpeed > 0.0) {
                    (distance / avgSpeed).roundToLong()
                } else {
                    0L
                }
                distance to duration
            }
            val verticalDelta = async(Dispatchers.IO) {
                RouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(points)
            }
            view?.updateAllDataDisplayed(distanceAndDuration.await(), verticalDelta.await())
        }
    }
}
