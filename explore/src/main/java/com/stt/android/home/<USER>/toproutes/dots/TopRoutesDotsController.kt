package com.stt.android.home.explore.toproutes.dots

import android.view.View
import androidx.core.graphics.toColorInt
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import com.airbnb.epoxy.DataBindingEpoxyModel
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.di.FragmentLifecycle
import com.stt.android.home.explore.topRoutesDot
import com.stt.android.utils.setFadeVisible
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class TopRoutesDotsController
@Inject constructor(
    @FragmentLifecycle private val fragmentLifecycle: Lifecycle
) : ViewStateEpoxyController<TopRoutesDotsContainer?>() {
    override fun buildModels(viewState: ViewState<TopRoutesDotsContainer?>) {
        viewState.data?.let { data ->
            if (data.isVisible) {
                if (data.showYellow) {
                    showDot(YELLOW)
                }
                if (data.showOrange) {
                    showDot(ORANGE)
                }
                if (data.showGreen) {
                    showDot(GREEN)
                }
                if (data.showBlue) {
                    showDot(BLUE)
                }
            }
        }
    }

    private fun showDot(color: String) {
        topRoutesDot {
            id(color)
            color(color.toColorInt())
            onBind { _, view, position ->
                fadeIn(position, view)
            }
            onUnbind { _, view -> hide(view) }
        }
    }

    private fun fadeIn(
        position: Int,
        view: DataBindingEpoxyModel.DataBindingHolder
    ) {
        fragmentLifecycle.coroutineScope.launch {
            delay(FADE_DELAY_STEP + position * FADE_DELAY_STEP)
            setFadeVisible(view.dataBinding.root, true)
        }
    }

    private fun hide(view: DataBindingEpoxyModel.DataBindingHolder) {
        view.dataBinding.root.visibility = View.INVISIBLE
    }

    companion object {
        private const val FADE_DELAY_STEP = 100L
        private const val YELLOW = "#FFFDD300"
        private const val ORANGE = "#FFFF7C3B"
        private const val GREEN = "#FF55D781"
        private const val BLUE = "#FF6289FE"
    }
}
