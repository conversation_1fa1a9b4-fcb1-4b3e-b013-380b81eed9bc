package com.stt.android.home.explore;

import com.stt.android.datasource.explore.toproutes.TopRoutesSortOrderRepositoryImpl;
import com.stt.android.domain.explore.toproutes.TopRoutesSortOrderRepository;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class ExploreModule {
    @Binds
    public abstract TopRoutesSortOrderRepository bindTopRoutesSortOrderRepository(
        TopRoutesSortOrderRepositoryImpl repository
    );
}
