package com.stt.android.home.explore.routes.details

import com.soy.algorithms.ascent.VerticalDelta
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.stt.android.domain.routes.TopRoute
import com.stt.android.home.explore.routes.RouteAltitudeChartData

data class RouteDetailSummary(
    val distance: Double,
    val duration: Long,
    val verticalDelta: VerticalDelta?
)

data class RouteHighlightData(
    val chartData: RouteAltitudeChartData,
    val climbGuidance: ClimbGuidance,
    val index: Int
)

data class ClimbSegmentData(
    val climbGuidance: ClimbGuidance,
    val segmentIndex: Int,
    val pointIndex: Int
)

data class ClimbGuidanceSegmentData(
    val showClimbGuidanceSegmentOverview: Boolean,
    val climbGuidance: ClimbGuidance,
    val currentClimbSegment: ClimbSegment? = null,
)

data class TopRouteDetailsViewData(
    val userName: String,
    val route: TopRoute,
    val isWatchEnabled: Boolean,
    val routeAltitudeChartData: RouteAltitudeChartData,
    val climbGuidance: ClimbGuidance,
    val watchRouteListFull: Boolean,
    val onWatchEnableChanged: (Boolean) -> Unit,
    val onFavoriteSavedClick: (Boolean) -> Unit,
    val onTopRouteEditClick: () -> Unit
)
