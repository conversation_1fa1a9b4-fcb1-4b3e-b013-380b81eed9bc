package com.stt.android.home.explore.library

import android.content.res.Resources
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import com.stt.android.R
import com.stt.android.home.explore.offlinemaps.OfflineMapsFragment
import com.stt.android.home.explore.pois.list.POIListFragment
import com.stt.android.home.explore.routesv2.RouteListFragment
import com.stt.android.intentresolver.TopRouteAction
import java.lang.ref.WeakReference
import javax.inject.Inject
import com.stt.android.home.explore.routes.list.RouteListFragment as OldRouteListFragment

internal class LibraryPagerAdapter @Inject constructor(
    fm: FragmentManager,
    private val resources: Resources,
    showOfflineMaps: Boolean,
    private val topRouteFeaturesEnabled: Boolean,
    private val topRouteAction: TopRouteAction,
) : FragmentStatePagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
    enum class Page {
        OFFLINE_MAPS,
        ROUTES,
        POIS,
    }

    private val fragments: MutableMap<Int, WeakReference<Fragment>> = hashMapOf()

    private val pages: List<Page> = if (showOfflineMaps) {
        listOf(Page.OFFLINE_MAPS, Page.ROUTES, Page.POIS)
    } else {
        listOf(Page.ROUTES, Page.POIS)
    }

    override fun getCount(): Int = pages.size

    override fun getItem(position: Int): Fragment {
        var fragment = getFragment(position)
        if (fragment == null) {
            fragment = when (pages.get(position)) {
                Page.OFFLINE_MAPS -> OfflineMapsFragment.newInstance()
                Page.ROUTES -> if (topRouteFeaturesEnabled) {
                    RouteListFragment.newInstance(topRouteAction)
                } else {
                    OldRouteListFragment.newInstance()
                }
                Page.POIS -> POIListFragment()
            }
            fragments.put(position, WeakReference(fragment))
        }
        return fragment
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        super.destroyItem(container, position, `object`)
        fragments.remove(position)
    }

    override fun getPageTitle(position: Int): CharSequence = when (pages[position]) {
        Page.OFFLINE_MAPS -> resources.getString(R.string.offline_maps_title)
        Page.ROUTES -> resources.getString(R.string.routes)
        Page.POIS -> resources.getString(R.string.my_pois)
    }

    fun getFragment(position: Int): Fragment? = fragments[position]?.get()

    fun pageAtPosition(position: Int): Page? = pages.getOrNull(position)
}
