package com.stt.android.home.explore.toproutes.filter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.carousel.TopRouteFilterContainer

@Composable
fun TopRoutesListFilterScreen(
    topRouteFilterContainer: TopRouteFilterContainer,
    onFilterButtonClicked: () -> Unit,
    onSelectChanged: (List<ActivityType>) -> Unit,
    modifier: Modifier = Modifier,
) {
    val selectedTypes = topRouteFilterContainer.topRouteFilter.activityTypes
    val allActivityTypes = rememberSaveable { topRouteFilterContainer.topRoutesActivityTypes }

    Row(
        modifier = modifier
            .padding(start = MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            modifier = Modifier
                .size(MaterialTheme.iconSizes.large)
                .clip(CircleShape)
                .shadow(4.dp, shape = CircleShape),
            colors = IconButtonDefaults.iconButtonColors(
                containerColor = MaterialTheme.colorScheme.surface,
                contentColor = MaterialTheme.colorScheme.onSurface,
            ),
            onClick = onFilterButtonClicked
        ) {
            Image(
                painter = painterResource(R.drawable.ic_top_route_filter),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
        }

        Spacer(modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall))

        TopRouteActivityTypeFilter(
            selectedTypes = selectedTypes ?: emptyList(),
            allTypes = allActivityTypes,
            contentPadding = PaddingValues(start = 0.dp),
            onAllSelected = {
                onSelectChanged(emptyList())
            },
            onSelectChanged = { selected ->
                onSelectChanged(selected)
            }
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun TopRoutesListFilterViewPreview() {
    M3AppTheme {
        TopRoutesListFilterScreen(
            TopRouteFilterContainer(
                topRouteFilter = TopRouteFilter(),
                topRoutesActivityTypes = listOf(
                    ActivityType.RUNNING,
                    ActivityType.HIKING,
                    ActivityType.TREKKING,
                    ActivityType.WALKING,
                    ActivityType.TRAIL_RUNNING,
                    ActivityType.CYCLING,
                    ActivityType.MOUNTAIN_BIKING,
                    ActivityType.CROSS_COUNTRY_SKIING,
                )
            ),
            onFilterButtonClicked = {},
            onSelectChanged = {}
        )
    }
}
