package com.stt.android.home.explore.routesv2

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.ShareBroadcastReceiver.Companion.shareRoute
import com.stt.android.common.ui.observeNotNull
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.Toast
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.databinding.FragmentLibraryRoutesBinding
import com.stt.android.home.explore.routes.FabSpeedDialMenuAdapter
import com.stt.android.home.explore.routes.ImportGPXActionHandler
import com.stt.android.home.explore.routes.ShareRouteEvent
import com.stt.android.home.explore.routes.details.BaseRouteDetailsActivity
import com.stt.android.home.explore.routes.planner.BaseRoutePlannerActivity
import com.stt.android.home.explore.routes.popular.BasePopularRouteDetailsActivity
import com.stt.android.intentresolver.TopRouteAction
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapSnapshotter
import com.stt.android.session.SignInFlowHook
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.PermissionUtils
import com.stt.android.workoutsettings.follow.RouteCard.Companion.DISTANCE_NOT_SET
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject

abstract class BaseRouteListFragment : Fragment() {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var measurementUnit: MeasurementUnit

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var signInFlowHook: SignInFlowHook

    private lateinit var binding: FragmentLibraryRoutesBinding

    val viewModel: RouteListViewModel by viewModels()

    private val requestStoragePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.all { it.value }
        if (allGranted) {
            doImportGPXFile()
        } else {
            enableFabSpeedDialMenuAdapter(true)
        }
    }

    private val popularRouteDetailLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        viewModel.refreshPopularRoutes()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val topRouteAction = arguments?.getSerializable(KEY_TOP_ROUTE_ACTION) as? TopRouteAction
        val (routePage, toastMsgResId) = when (topRouteAction) {
            TopRouteAction.ADD_FAVORITE ->
                Pair(RoutesPage.POPULAR, com.stt.android.home.explore.R.string.top_route_add_favorite_successful)

            TopRouteAction.EDIT ->
                Pair(RoutesPage.MINE, com.stt.android.home.explore.R.string.top_route_saved_successful)

            TopRouteAction.NONE -> Pair(RoutesPage.MINE, null)

            null -> Pair(RoutesPage.MINE, null)
        }
        viewModel.handleRoutePageChanged(routePage)

        binding = FragmentLibraryRoutesBinding.inflate(inflater)
        binding.composeView.setContentWithM3Theme {
            var showToast by remember { mutableStateOf(toastMsgResId != null) }

            LaunchedEffect(showToast) {
                if (showToast) {
                    delay(2500L)
                    showToast = false
                }
            }

            Box(modifier = Modifier.fillMaxWidth()) {
                RouteListScreen(
                    viewModel = viewModel,
                    topRouteTypes = viewModel.topRouteTypes,
                    infoModelFormatter = infoModelFormatter,
                    measurementUnit = measurementUnit
                )

                if (showToast && toastMsgResId != null) {
                    Toast(
                        text = stringResource(id = toastMsgResId),
                        backgroundColor = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .widthIn(max = dimensionResource(id = com.stt.android.core.R.dimen.dialog_max_width))
                            .fillMaxWidth()
                    )
                }
            }
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupMapSnapshotter()
        setupActionButton()
        startListeningViewModelEvents()
    }

    override fun onResume() {
        super.onResume()
        enableFabSpeedDialMenuAdapter(true)
    }

    private fun setupMapSnapshotter() {
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(requireActivity().applicationContext)
            }
        }
    }

    private fun startListeningViewModelEvents() {
        viewModel.handleFabSpeedDialMenu.observeNotNull(viewLifecycleOwner) { position ->
            enableFabSpeedDialMenuAdapter(false)
            when (position) {
                0 -> {
                    if (viewModel.isSubscribedToPremium.value == false) {
                        planRoute()
                    } else {
                        importGPXFile()
                    }
                }

                1 -> planRoute()
                else -> Timber.d("Received click to unsupported menu item $position")
            }
            binding.actionButton.closeSpeedDialMenu()
        }

        viewModel.routeClicked.observeNotNull(viewLifecycleOwner) { routeItem ->
            openRoute(routeItem)
        }

        viewModel.shareRouteEvent.observeNotNull(viewLifecycleOwner) {
            handleShareEvent(it)
        }
    }

    private fun setupActionButton() {
        lifecycleScope.launch {
            viewModel.currentRoutePage.collectLatest {
                binding.actionButton.visibility =
                    if (it == RoutesPage.MINE) View.VISIBLE else View.GONE
            }
        }

        binding.actionButton.setContentCoverColour(
            ContextCompat.getColor(
                requireContext(),
                com.stt.android.home.explore.R.color.fab_menu_cover_color
            )
        )

        val viewModelRef = WeakReference(viewModel)
        binding.actionButton.speedDialMenuAdapter =
            FabSpeedDialMenuAdapter(
                context = requireContext(),
                showPremiumRequiredNotes = viewModel.isSubscribedToPremium.value == false
            ) { position ->
                viewModelRef.get()?.setHandleFabSpeedDialMenu(position)
            }
    }

    private fun importGPXFile() {
        if (FlavorUtils.isSuuntoAppChina &&
            !EasyPermissions.hasPermissions(requireContext(), *PermissionUtils.STORAGE_PERMISSIONS)
        ) {
            AlertDialog.Builder(requireContext())
                .setTitle(R.string.request_permission)
                .setMessage(R.string.request_storage_permission_import_route)
                .setPositiveButton(R.string.allow) { _, _ ->
                    requestStoragePermissionLauncher.launch(PermissionUtils.STORAGE_PERMISSIONS)
                }
                .setNegativeButton(R.string.cancel, null)
                .setOnDismissListener {
                    enableFabSpeedDialMenuAdapter(true)
                }
                .show()
        } else {
            doImportGPXFile()
        }
    }

    private fun doImportGPXFile() {
        val parent = parentFragment
        val activity: Activity? = activity
        when {
            parent is ImportGPXActionHandler -> parent.importGPXFile()
            activity is ImportGPXActionHandler -> activity.importGPXFile()
            else -> Unit
        }
    }

    private fun planRoute() {
        BaseRoutePlannerActivity.startCreateRouteActivityOrRedirectToLogin(
            viewModel.currentUserController,
            signInFlowHook,
            context
        )
    }

    private fun openRoute(routeItem: LibraryRouteItem) {
        val routeId = routeItem.routerId
        val distanceFromCurrentLocation = routeItem.distanceFromCurrentLocation
        when (routeItem) {
            is MyRouteItem -> startActivity(
                BaseRouteDetailsActivity.newStartIntent(
                    requireContext(),
                    routeId,
                    distanceFromCurrentLocation ?: DISTANCE_NOT_SET
                )
            )

            is PopularRouteItem -> {
                val activityType = routeItem.topRoute.activityId
                val isFavoriteSaved = routeItem.isLatestFavoriteSaved
                popularRouteDetailLauncher.launch(
                    BasePopularRouteDetailsActivity.newStartIntentFromLibrary(
                        requireContext(),
                        routeId,
                        activityType,
                        distanceFromCurrentLocation ?: DISTANCE_NOT_SET,
                        isFavoriteSaved
                    )
                )
            }
        }
    }

    private fun enableFabSpeedDialMenuAdapter(enable: Boolean) {
        (binding.actionButton.speedDialMenuAdapter as? FabSpeedDialMenuAdapter)?.setEnabled(enable)
    }

    private fun handleShareEvent(event: ShareRouteEvent) =
        when (event) {
            ShareRouteEvent.NoNetwork ->
                showToast(R.string.network_disabled_enable)

            ShareRouteEvent.NotSynced ->
                showToast(R.string.route_can_not_be_shared_right_now)

            ShareRouteEvent.SharingFailed ->
                showToast(R.string.error_generic_try_again)

            is ShareRouteEvent.RouteShared ->
                shareRoute(
                    requireActivity(),
                    event.link,
                    AnalyticsPropertyValue.ExportSource.ROUTES_SCREEN
                )
        }

    private fun showToast(@StringRes textRes: Int) =
        Toast.makeText(requireContext().applicationContext, textRes, Toast.LENGTH_SHORT).show()

    companion object {
        const val KEY_TOP_ROUTE_ACTION = "com.stt.android.home.explore.KEY_TOP_ROUTE_ACTION"
    }
}
