package com.stt.android.home.explore.toproutes.carousel

import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.toproutes.filter.TopRouteFilter
import com.stt.android.maps.SuuntoTopRouteFeature
import kotlinx.parcelize.Parcelize

// todo missing ascent, not in mapbox data yet
@Parcelize
data class RouteFeature constructor(
    val routeId: String,
    val duration: Long,
    val distance: Double,
    val ascent: Double,
    val descent: Double?,
    val activityType: ActivityType,
    val selected: Boolean,
    val startPoint: LatLng,
    val awayYouDistance: Double,
    val popularity: Double = 0.0,
    val name: String? = null,
    val startAddress: String? = null,
    val endAddress: String? = null,
    val routeImages: List<String>? = null,
) : Parcelable

data class SearchHere constructor(
    val showSearchHere: Boolean,
    val isBusy: <PERSON>olean,
    val onSearchHere: () -> Unit
)

data class TopRoutesCarouselContainer constructor(
    val targetLatitude: Double,
    val targetLongitude: Double,
    val zoomLevel: Double,
    val originalFeatures: List<RouteFeature>,
    val features: List<RouteFeature>,
    val suuntoFeatures: List<SuuntoTopRouteFeature>,
    val searchHere: SearchHere,
    val topRouteFilterContainer: TopRouteFilterContainer,
    val onCardClicked: (CarouselEvent) -> Unit,
    val onScrolled: (CarouselEvent) -> Unit,
    val onRouteLoaded: (String) -> Unit,
    val onRouteVisible: (RouteFeature) -> Unit
)

data class CarouselEvent constructor(
    val type: CarouselEventType,
    val routeFeature: RouteFeature? = null,
    val feature: SuuntoTopRouteFeature? = null,
    val notifyMapMove: Boolean = false,
    val routePosition: Int? = null,
    val skipRouteSuggestionViewedEvent: Boolean = false,
    val awayYouDistance: Double = 0.0,
)

enum class CarouselEventType {
    ON_DRAGGED,
    ON_SCROLLED,
    ON_CARD_CLICKED
}

data class TopRouteFilterContainer(
    val topRouteFilter: TopRouteFilter,
    val topRoutesActivityTypes: List<ActivityType>
)

sealed interface TopRouteFilterViewData {

    data object Loading : TopRouteFilterViewData

    data class Loaded(val topRoutes: List<TopRoute>) : TopRouteFilterViewData
}
