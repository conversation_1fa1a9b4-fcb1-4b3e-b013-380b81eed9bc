package com.stt.android.home.explore.pois.list

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.snackbar.Snackbar
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.extensions.clearText
import com.stt.android.extensions.hideKeyboard
import com.stt.android.extensions.openAppSettings
import com.stt.android.extensions.openLocationSourceSettings
import com.stt.android.extensions.textWatcherChangedFlow
import com.stt.android.home.explore.R
import com.stt.android.home.explore.WorkoutMapActivity
import com.stt.android.home.explore.databinding.FragmentPoiListBinding
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.PermissionUtils
import dagger.hilt.android.AndroidEntryPoint
import pub.devrel.easypermissions.EasyPermissions
import com.stt.android.R as BaseR

@AndroidEntryPoint
class POIListFragment : ViewStateListFragment2<POIListContainer, POIListViewModel>() {

    override val viewModel: POIListViewModel by viewModels()
    override val layoutId = R.layout.fragment_poi_list

    private val binding: FragmentPoiListBinding get() = requireBinding()

    private var snackbar: Snackbar? = null

    private var scrollListener: RecyclerView.OnScrollListener? = null

    @SuppressLint("CheckResult")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.showEditError.observeNotNull(viewLifecycleOwner) {
            Toast.makeText(
                requireContext().applicationContext,
                R.string.error_could_not_edit_poi,
                Toast.LENGTH_LONG
            )
                .show()
        }

        viewModel.showSyncSnackbar.observeNotNull(viewLifecycleOwner) { syncOngoing ->
            if (syncOngoing && snackbar == null) {
                snackbar = Snackbar.make(
                    binding.list,
                    R.string.poi_sync_in_progress,
                    Snackbar.LENGTH_INDEFINITE
                ).also { snack ->
                    snack.show()
                }
            } else if (!syncOngoing) {
                snackbar?.dismiss()
                snackbar = null
            }
        }

        viewModel.openPOIDetailsForId.observeNotNull(viewLifecycleOwner) {
            showPOIDetails(it)
        }

        viewModel.watchEnabledPOILimitExceededEvent.observeNotNull(viewLifecycleOwner) {
            showNumberOfWatchEnabledPOIsExceededError(it)

            // If enabling watchEnabled flag fails, then the list is in a state where the Switch
            // view is enabled in the UI, but the underlying flag in the Epoxy model is still false.
            // Epoxy will not detect any changes in the data if we simply set the data without
            // changes

            // Force Epoxy to rebind list items to reset the Switch state by changing an artificial
            // 'nonce' value to force diffing to detect changes.
            viewModel.forceDataChanged()
        }

        (binding.list.itemAnimator as? DefaultItemAnimator)?.apply {
            supportsChangeAnimations = false
        }

        binding.viewModel = viewModel

        binding.tvFilterRule.setOnClickListenerThrottled {
            val items = POISortingRule.entries.map { getString(it.label) }.toTypedArray()
            AlertDialog.Builder(requireActivity())
                .setItems(items) { _, which -> viewModel.changeSortingRule(POISortingRule.entries[which]) }
                .setNegativeButton(BaseR.string.cancel, null)
                .show()
        }

        binding.etSearch.setOnEditorActionListener { v, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                viewModel.load()
                activity?.hideKeyboard()
                v.clearFocus()
            }
            true
        }

        lifecycleScope.launchWhenResumed {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                binding.etSearch.textWatcherChangedFlow().collect {
                    viewModel.load()
                }
            }
        }

        binding.btnClearSearch.setOnClickListenerThrottled {
            activity?.hideKeyboard()
            binding.etSearch.clearText()
        }

        viewModel.locationDisabled.observeNotNull(viewLifecycleOwner) {
            if (it) {
                requireActivity().openLocationSourceSettings()
            } else {
                requestPermissions(
                    PermissionUtils.LOCATION_PERMISSIONS,
                    LOCATION_PERMISSION_REQUEST_CODE_FOR_POI_LIST
                )
            }
        }

        scrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    val focusedView = requireActivity().window.currentFocus
                    if (focusedView === binding.etSearch && binding.etSearch.hasFocus()) {
                        requireActivity().hideKeyboard()
                        binding.etSearch.clearFocus()
                    }
                }
            }
        }
        scrollListener?.let {
            recyclerView.addOnScrollListener(it)
        }

        binding.defaultEmptyView.goToMap.setOnClickListenerThrottled { goToMap() }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        return binding.root
    }

    private fun showPOIDetails(poiId: Long) =
        startActivity(WorkoutMapActivity.newIntentForPOIDetails(requireContext(), poiId))

    private fun showNumberOfWatchEnabledPOIsExceededError(maximumCount: Int) {
        val title = getString(R.string.poi_limit_on_watch_exceeded_title, maximumCount)
        val message = getString(R.string.poi_limit_on_watch_exceeded_body)
        val dialog = SimpleDialogFragment.newInstance(message, title, getString(BaseR.string.ok))
        dialog.show(childFragmentManager, WATCH_ENABLED_POI_LIMIT_TAG)
    }

    private fun goToMap() {
        activity?.finish()
    }

    private fun onLocationPermissionGranted() {
        viewModel.updateSortingRule(POISortingRule.NEAREST_TO_ME)
    }

    private fun onLocationPermissionDenied() {
        AlertDialog.Builder(requireActivity())
            .setTitle(R.string.location_permission_required_title)
            .setMessage(R.string.sorting_location_permissions_message)
            .setPositiveButton(R.string.open_settings_to_enable_location_button) { _, _ ->
                requireActivity().openAppSettings()
            }
            .setNegativeButton(BaseR.string.cancel, null)
            .show()
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE_FOR_POI_LIST -> {
                if (EasyPermissions.hasPermissions(requireActivity(), *permissions)) {
                    onLocationPermissionGranted()
                } else if (EasyPermissions.somePermissionPermanentlyDenied(
                        this,
                        permissions.toList()
                    )
                ) {
                    onLocationPermissionDenied()
                }
            }
        }
    }

    override fun onStateChanged(state: ViewState<POIListContainer?>) {
        super.onStateChanged(state)

        binding.progressBar.visibility = if (state.isLoading()) View.VISIBLE else View.GONE
    }

    override fun onDestroyView() {
        scrollListener?.let(recyclerView::removeOnScrollListener)
        snackbar?.dismiss()
        snackbar = null

        super.onDestroyView()
    }

    companion object {
        private const val WATCH_ENABLED_POI_LIMIT_TAG = "com.stt.android.WatchEnabledLimitDialog"

        private const val LOCATION_PERMISSION_REQUEST_CODE_FOR_POI_LIST = 201
    }
}
