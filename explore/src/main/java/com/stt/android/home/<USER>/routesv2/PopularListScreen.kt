package com.stt.android.home.explore.routesv2

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.filter.TopRouteFilter
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.map.MapHelper

@Composable
fun PopularListScreen(
    viewData: RouteViewData,
    searchQuery: String,
    topRouteFilter: TopRouteFilter,
    infoModelFormatter: InfoModelFormatter,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    when (viewData) {
        RouteViewData.Loading -> {
            // do nothing here.
        }

        is RouteViewData.Error -> {
            val emptyResId = if (MapHelper.isDefaultProviderAMap()) {
                R.string.popular_route_empty_tip_for_amap
            } else {
                R.string.mine_route_empty_tip
            }
            RouteEmptyContent(
                emptyResId = emptyResId,
                modifier = modifier
            )
        }

        is RouteViewData.Loaded -> {
            val emptyResId = if (MapHelper.isDefaultProviderAMap()) {
                R.string.popular_route_empty_tip_for_amap
            } else {
                R.string.popular_route_empty_tip
            }

            RouteListLoaded(
                searchQuery = searchQuery,
                emptyResId = emptyResId,
                topRouteFilter = topRouteFilter,
                routesContainer = viewData.routesContainer,
                infoModelFormatter = infoModelFormatter,
                measurementUnit = measurementUnit,
                modifier = modifier
            )
        }
    }
}
