package com.stt.android.home.explore.routes.ui

import android.content.Context
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.soy.algorithms.climbanalysis.entities.ClimbSegmentType
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.routes.getGradeIconRes
import com.stt.android.home.explore.routes.getGuidanceColorRes
import com.stt.android.home.explore.routes.getIconRes
import com.stt.android.home.explore.routes.getTitleRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import kotlin.math.abs
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

@Composable
internal fun ClimbGuidanceSegment(
    currentClimbSegment: ClimbSegment,
    ordinal: Int,
    total: Int,
    infoModelFormatter: InfoModelFormatter,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.xsmall,
            )
            .height(24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ClimbGuidanceSegmentInfo(currentClimbSegment, ordinal, total)

        if (currentClimbSegment.hasAscentDescent()) {
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))
            ClimbGuidanceSegmentAscentDescent(currentClimbSegment, infoModelFormatter)
        }

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))
        ClimbGuidanceSegmentDistance(currentClimbSegment, infoModelFormatter)

        if (currentClimbSegment.hasAscentDescentAngle()) {
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))
            ClimbGuidanceSegmentAscentDescentAngle(currentClimbSegment)
        }
    }
}

@Composable
private fun ClimbGuidanceSegmentInfo(currentClimbSegment: ClimbSegment, ordinal: Int, total: Int) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        if (currentClimbSegment.climbSegmentType == ClimbSegmentType.FLAT) {
            Icon(
                painter = painterResource(currentClimbSegment.climbSegmentType.getIconRes()),
                contentDescription = stringResource(currentClimbSegment.climbSegmentType.getTitleRes()),
                tint = colorResource(currentClimbSegment.climbSegmentType.getGuidanceColorRes()),
                modifier = Modifier.size(MaterialTheme.iconSizes.mini)
            )
        } else {
            Text(
                text = stringResource(currentClimbSegment.climbSegmentType.getTitleRes()),
                color = colorResource(currentClimbSegment.climbSegmentType.getGuidanceColorRes()),
                style = MaterialTheme.typography.bodyBold,
            )
        }
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
        Text(
            text = "$ordinal/$total",
            color = colorResource(currentClimbSegment.climbSegmentType.getGuidanceColorRes()),
            style = MaterialTheme.typography.bodyBold,
        )
    }
}

@Composable
private fun ClimbGuidanceSegmentAscentDescent(
    currentClimbSegment: ClimbSegment,
    infoModelFormatter: InfoModelFormatter
) {
    val formattedAscentDescent = infoModelFormatter
        .formatAscentDescent(currentClimbSegment, LocalContext.current)
        ?: return

    Row {
        Icon(
            painter = painterResource(currentClimbSegment.climbSegmentType.getIconRes()),
            contentDescription = stringResource(currentClimbSegment.climbSegmentType.getTitleRes()),
            tint = colorResource(currentClimbSegment.climbSegmentType.getGuidanceColorRes()),
            modifier = Modifier.size(MaterialTheme.iconSizes.mini)
        )

        Text(
            text = formattedAscentDescent,
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall)
        )
    }
}

private fun InfoModelFormatter.formatAscentDescent(
    currentClimbSegment: ClimbSegment,
    context: Context
): String? {
    val ascentDescent = currentClimbSegment.calculateAscentDescent()
    if (ascentDescent.isNaN()) {
        return null
    }

    val formattedAscentDescent = formatValue(SummaryItem.ASCENTALTITUDE, ascentDescent)
        .value
        ?.takeUnless { it.isEmpty() }
        ?: return null
    return "$formattedAscentDescent ${context.getString(unit.altitudeUnit)}"
}

private fun ClimbSegment.hasAscentDescent(): Boolean = !calculateAscentDescent().isNaN()

private fun ClimbSegment.calculateAscentDescent(): Double = when (climbSegmentType) {
    ClimbSegmentType.CLIMB,
    ClimbSegmentType.UPHILL -> ascentAtEnd - ascentAtStart

    ClimbSegmentType.DESCENT,
    ClimbSegmentType.DOWNHILL -> descentAtEnd - descentAtStart

    ClimbSegmentType.FLAT -> Double.NaN
}

@Composable
private fun ClimbGuidanceSegmentDistance(
    currentClimbSegment: ClimbSegment,
    infoModelFormatter: InfoModelFormatter
) {
    val formattedDistance = infoModelFormatter
        .formatDistance(currentClimbSegment, LocalContext.current)
        ?: return

    Row {
        Icon(
            painter = painterResource(BaseR.drawable.distance_fill),
            contentDescription = stringResource(currentClimbSegment.climbSegmentType.getTitleRes()),
            tint = colorResource(currentClimbSegment.climbSegmentType.getGuidanceColorRes()),
            modifier = Modifier.size(MaterialTheme.iconSizes.mini)
        )

        Text(
            text = formattedDistance,
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall)
        )
    }
}

private fun InfoModelFormatter.formatDistance(
    climbSegment: ClimbSegment,
    context: Context
): String? {
    val formattedDistance = formatDistance(climbSegment.calculateDistance())
        .getOrNull()
        ?: return null
    return "${formattedDistance.value} ${formattedDistance.unitResId?.let(context::getString)}"
}

private fun ClimbSegment.calculateDistance(): Double = distanceAtEnd - distanceAtStart

@Composable
private fun ClimbGuidanceSegmentAscentDescentAngle(climbSegment: ClimbSegment) {
    val iconRes = climbSegment.climbSegmentType.getGradeIconRes() ?: return
    val formattedAngle = formatAngle(climbSegment, LocalContext.current) ?: return

    Row {
        Icon(
            painter = painterResource(iconRes),
            contentDescription = stringResource(climbSegment.climbSegmentType.getTitleRes()),
            tint = colorResource(climbSegment.climbSegmentType.getGuidanceColorRes()),
            modifier = Modifier.size(MaterialTheme.iconSizes.mini)
        )

        Text(
            text = formattedAngle,
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall)
        )
    }
}

private fun formatAngle(climbSegment: ClimbSegment, context: Context): String? {
    val elevationDiff = abs(climbSegment.elevationAtEnd - climbSegment.elevationAtStart)
        .takeUnless { it.isNaN() }
        ?: return null
    val distance = climbSegment.calculateDistance()
    val angle = (elevationDiff / distance * 100).roundToInt()
    return "${context.getString(BaseR.string.avg)} $angle%"
}

private fun ClimbSegment.hasAscentDescentAngle(): Boolean = when (climbSegmentType) {
    ClimbSegmentType.CLIMB,
    ClimbSegmentType.DESCENT -> true

    ClimbSegmentType.UPHILL,
    ClimbSegmentType.DOWNHILL,
    ClimbSegmentType.FLAT -> false
}
