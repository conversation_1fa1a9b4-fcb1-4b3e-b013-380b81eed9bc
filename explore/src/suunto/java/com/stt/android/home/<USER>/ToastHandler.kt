import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.widgets.ProgressToast
import com.stt.android.compose.widgets.Toast
import com.suunto.connectivity.watch.navigate.NavigateTip

@Composable
fun ToastHandler(
    navigateTip: NavigateTip,
    modifier: Modifier = Modifier,
) {
    val (textId, isLoading, backgroundColor) = when (navigateTip) {
        is NavigateTip.Normal -> Triple(navigateTip.stateTextResId, navigateTip.showLoading(), MaterialTheme.colors.primary)
        is NavigateTip.Error -> Triple(navigateTip.errorTextResId, false, MaterialTheme.colors.error)
    }

    val text = stringResource(id = textId)
    if (isLoading) {
        ProgressToast(
            text = text,
            backgroundColor = backgroundColor,
            modifier = modifier
        )
    } else {
        Toast(
            text = text,
            backgroundColor = backgroundColor,
            modifier = modifier
        )
    }
}
