package com.stt.android.home.explore.offlinemaps

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.home.explore.offlinemaps.ui.DownloadedMapDetailsScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import com.stt.android.R as BaseR

@AndroidEntryPoint
internal class DownloadedMapDetailsActivity : AppCompatActivity() {
    private val viewModel: DownloadedMapDetailsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithM3Theme {
            val viewState by viewModel.viewState.collectAsState()

            DownloadedMapDetailsScreen(
                viewState = viewState,
                eventHandler = { event ->
                    when (event) {
                        is DownloadedMapDetailsViewEvent.NavigateUp -> finish()
                        is DownloadedMapDetailsViewEvent.Reload -> viewModel.loadRegionDetails()
                        is DownloadedMapDetailsViewEvent.ShowDisclaimer -> showDisclaimer()
                        is DownloadedMapDetailsViewEvent.ShowDeleteConfirmation -> viewModel.showDeleteConfirmation(
                            event.deleteFrom
                        )
                        is DownloadedMapDetailsViewEvent.DismissDeleteConfirmation -> viewModel.dismissDeleteConfirmation()
                        is DownloadedMapDetailsViewEvent.DownloadRegion -> lifecycleScope.launch {
                            viewModel.downloadRegion(
                                region = event.region,
                                downloadTo = event.downloadTo,
                            )
                        }
                        is DownloadedMapDetailsViewEvent.DeleteRegion -> lifecycleScope.launch {
                            viewModel.deleteRegion(
                                region = event.region,
                                deleteFrom = event.deleteFrom,
                            )
                        }
                    }
                },
            )
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.pollRegionDetails()
            }
        }
    }

    private fun showDisclaimer() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(
            Intent(Intent.ACTION_VIEW).apply {
                data = getString(BaseR.string.osm_disclaimer_url).toUri()
            }
        )
    }

    companion object {
        const val EXTRA_DOWNLOADED_REGION_ID: String = "extra_downloaded_region_id"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            regionId: String,
        ): Intent = Intent(context, DownloadedMapDetailsActivity::class.java)
            .putExtra(EXTRA_DOWNLOADED_REGION_ID, regionId)
    }
}
