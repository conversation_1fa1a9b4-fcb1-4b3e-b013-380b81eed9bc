package com.stt.android.home.explore.offlinemaps.selection.ui

import android.text.format.Formatter.formatShortFileSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.stt.android.R
import com.stt.android.compose.component.SuuntoInfiniteLinearProgressBar
import com.stt.android.compose.component.SuuntoLinearProgressBar
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.highlightedString
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.home.explore.offlinemaps.entities.DownloadOrder
import com.stt.android.home.explore.offlinemaps.entities.OfflineAreaSourceTileType
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionStatus
import com.stt.android.home.explore.offlinemaps.entities.isForMobile
import com.stt.android.home.explore.offlinemaps.entities.isForWatch
import com.stt.android.home.explore.offlinemaps.ui.preview.FakeOfflineRegionGroupData
import kotlinx.collections.immutable.persistentListOf
import java.util.Locale

@Composable
internal fun OfflineRegionItem(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onClick: () -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    modifier: Modifier = Modifier,
    highlight: String? = null,
) {
    var showCancelConfirmation by rememberSaveable { mutableStateOf(false) }

    val secondaryText = createSecondaryText(
        region = region,
    )

    val secondaryTextColor: Color = when {
        region.downloadFailed -> MaterialTheme.colorScheme.error
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    val action = getRegionAction(
        region = region,
        cancellingDownload = cancellingDownload,
        requestingDownload = requestingDownload,
        onCancel = { showCancelConfirmation = true },
        onRetry = onRetry,
    )

    Column(
        modifier = modifier
            .background(color = MaterialTheme.colorScheme.surface)
            .clickable(onClick = onClick)
            .padding(MaterialTheme.spacing.medium),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.map_type_outline),
                contentDescription = null,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.medium),
                tint = MaterialTheme.colorScheme.secondary,
            )

            Spacer(Modifier.width(MaterialTheme.spacing.medium))

            Column {
                Text(
                    highlightedString(
                        text = region.name,
                        highlight = highlight,
                        highlightStyle = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold,
                        ),
                    ),
                    style = MaterialTheme.typography.bodyLarge,
                )

                Text(
                    text = highlightedString(
                        text = secondaryText,
                        highlight = highlight,
                        highlightStyle = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold,
                        )
                    ),
                    color = secondaryTextColor,
                    style = MaterialTheme.typography.bodyMedium,
                )
            }

            Spacer(Modifier.weight(1.0F))

            if (action == null) {
                Icon(
                    painter = SuuntoIcons.ActionRight.asPainter(),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                )
            } else {
                action()
            }
        }

        when {
            region.downloadingForWatch -> {
                // For watch, we show the exact downloading progress, because the progress is reported
                // by the watch, and usually takes a long time to finish.
                val downloadProgressForWatch = region.downloadProgressForWatch
                if (downloadProgressForWatch > 0.0F) {
                    SuuntoLinearProgressBar(
                        progress = downloadProgressForWatch,
                        modifier = Modifier
                            .fillMaxWidth(),
                    )
                }
            }

            region.downloadingForMobile -> {
                // For mobile, we show an infinite progress bar, because we only refresh every 15 seconds,
                // and this is too slow to reflect the current download progress.
                SuuntoInfiniteLinearProgressBar(
                    modifier = Modifier
                        .fillMaxWidth(),
                )
            }
        }
    }

    if (showCancelConfirmation) {
        ConfirmationDialog(
            title = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_title
                } else {
                    R.string.cancel_download_confirmation_title
                }
            ),
            text = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_text
                } else {
                    R.string.cancel_download_confirmation_text
                }
            ),
            cancelButtonText = stringResource(R.string.no),
            confirmButtonText = stringResource(R.string.yes),
            onDismissRequest = { showCancelConfirmation = false },
            onConfirm = {
                showCancelConfirmation = false
                onCancel(region)
            }
        )
    }
}

private inline fun getRegionAction(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    crossinline onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    crossinline onRetry: (OfflineRegionResult.OfflineRegion) -> Unit
): @Composable (() -> Unit)? = when {
    region.updateAvailable || region.downloadingUpdate -> {
        {}
    }
    region.cancellable -> {
        {
            TextButton(onClick = { onCancel(region) }, enabled = !cancellingDownload) {
                Text(
                    text = stringResource(id = R.string.cancel).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    }
    region.downloadFailed -> {
        {
            TextButton(onClick = { onRetry(region) }, enabled = !requestingDownload) {
                Text(
                    text = stringResource(id = R.string.retry).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    }
    else -> null
}

@Composable
private fun createSecondaryText(
    region: OfflineRegionResult.OfflineRegion,
): String = when {
    region.downloadRequested -> stringResource(id = R.string.download_requested)
    region.downloading -> stringResource(id = R.string.download_downloading)
    region.downloaded -> stringResource(id = R.string.download_downloaded)
    region.downloadFailed -> stringResource(id = R.string.download_failed)
    else -> {
        val sourceTileTypeForDeletionRequest = region.downloadOrders
            .firstOrNull { it.status == OfflineRegionStatus.DELETE_REQUESTED }
            ?.sourceTileType
        val size = when {
            sourceTileTypeForDeletionRequest?.isForMobile() == true -> region.sizeForMobile?.storageSizeInBytes
            sourceTileTypeForDeletionRequest?.isForWatch() == true -> region.sizeForWatch?.storageSizeInBytes
            else -> region.sizeForWatch?.storageSizeInBytes ?: region.sizeForMobile?.storageSizeInBytes
        } ?: return ""
        val formattedSize = formatShortFileSize(LocalContext.current, size)
        if (region.groupName.isNullOrBlank() || sourceTileTypeForDeletionRequest == null) {
            formattedSize
        } else {
            "$formattedSize · ${region.groupName}"
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionItemPreview(
    @PreviewParameter(OfflineRegionItemParamsProvider::class) params: OfflineRegionItemParams
) {
    M3AppTheme {
        OfflineRegionItem(
            region = params.region,
            cancellingDownload = false,
            requestingDownload = false,
            onClick = {},
            onCancel = {},
            onRetry = {},
        )
    }
}

private class OfflineRegionItemParamsProvider : PreviewParameterProvider<OfflineRegionItemParams> {
    override val values: Sequence<OfflineRegionItemParams>
        get() {
            val dummyRegion = FakeOfflineRegionGroupData.finland.regions.first()
            return sequenceOf(
                OfflineRegionItemParams(region = dummyRegion.copy(downloadOrders = persistentListOf())),
                OfflineRegionItemParams(
                    region = dummyRegion.copy(
                        downloadOrders = persistentListOf(
                            DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.REQUESTED,
                                downloadedSize = 0,
                                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
                            ),
                        ),
                    ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            name = "A very long name that will not fit the space available on one line",
                            downloadOrders = persistentListOf(
                                DownloadOrder(
                                    "",
                                    null,
                                    status = OfflineRegionStatus.UPDATE_IN_PROGRESS,
                                    downloadedSize = 0,
                                    sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
                                ),
                            ),
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
            )
        }
}

private data class OfflineRegionItemParams(
    val region: OfflineRegionResult.OfflineRegion,
    val action: @Composable (() -> Unit)? = null
)
