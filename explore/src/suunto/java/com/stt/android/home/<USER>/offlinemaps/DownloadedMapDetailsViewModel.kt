package com.stt.android.home.explore.offlinemaps

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.home.explore.offlinemaps.DownloadedMapDetailsActivity.Companion.EXTRA_DOWNLOADED_REGION_ID
import com.stt.android.home.explore.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.domain.OfflineRegionDownloadOperators
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapDownloadTarget
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
internal class DownloadedMapDetailsViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val offlineRegionRepository: OfflineRegionRepository,
    private val downloadOperatorsDelegate: OfflineRegionDownloadOperators,
    private val offlineMapsAnalytics: OfflineMapsAnalytics,
    private val featureToggle: FeatureToggle,
) : ViewModel() {
    private val _viewState: MutableStateFlow<DownloadedMapDetailsViewState> = MutableStateFlow(DownloadedMapDetailsViewState.Loading)
    val viewState: StateFlow<DownloadedMapDetailsViewState> = _viewState.asStateFlow()

    private val regionId: String get() = requireNotNull(savedStateHandle[EXTRA_DOWNLOADED_REGION_ID])

    private var loadJob: Job? = null

    private var updateJob: Job? = null

    init {
        loadRegionDetails()
    }

    fun loadRegionDetails() {
        loadJob?.cancel()
        loadJob = viewModelScope.launch {
            _viewState.value = DownloadedMapDetailsViewState.Loading

            _viewState.value = runSuspendCatching {
                DownloadedMapDetailsViewState.Loaded(
                    supportsOfflineMapsOnMobile = featureToggle[FeatureFlag.EnableOfflineMapOnMobile],
                    region = offlineRegionRepository.getLibraryRegion(regionId),
                    showDeleteConfirmation = false,
                    deleteFrom = null,
                )
            }.getOrElse { e ->
                Timber.w(e, "Loading region details failed.")
                DownloadedMapDetailsViewState.Error
            }
        }
    }

    suspend fun pollRegionDetails() {
        loadJob?.join()

        while (true) {
            delay(calculatePollDelay())

            if (_viewState.value !is DownloadedMapDetailsViewState.Loaded) {
                continue
            }

            updateJob = viewModelScope.launch {
                runSuspendCatching {
                    val fetchedRegion = offlineRegionRepository.getLibraryRegion(regionId)
                    _viewState.update { current ->
                        (current as? DownloadedMapDetailsViewState.Loaded)
                            ?.copy(region = fetchedRegion)
                            ?: current
                    }
                }.onFailure { e ->
                    Timber.w(e, "Failed to poll region details")
                }
            }
            updateJob?.join()
        }
    }

    private fun calculatePollDelay(): Duration {
        val isDownloadingForMobile = (_viewState.value as? DownloadedMapDetailsViewState.Loaded)
            ?.region
            ?.downloadingForMobile
        return if (isDownloadingForMobile == true) 5.seconds else 15.seconds
    }

    fun showDeleteConfirmation(deleteFrom: OfflineMapDownloadTarget) {
        _viewState.update { current ->
            (current as? DownloadedMapDetailsViewState.Loaded)
                ?.copy(
                    showDeleteConfirmation = true,
                    deleteFrom = deleteFrom,
                )
                ?: current
        }
    }

    fun dismissDeleteConfirmation() {
        _viewState.update { current ->
            (current as? DownloadedMapDetailsViewState.Loaded)
                ?.copy(showDeleteConfirmation = false)
                ?: current
        }
    }

    suspend fun downloadRegion(
        region: OfflineRegionResult.OfflineRegion,
        downloadTo: OfflineMapDownloadTarget,
    ) {
        runSuspendCatching {
            updateJob?.join() // wait for possible update job to complete

            val updatedRegion = downloadOperatorsDelegate.downloadOfflineRegion(
                downloadTo = downloadTo,
                regionId = region.id,
                groupName = region.name,
            ).addDownloadOrders(region.downloadOrders)
            _viewState.update { current ->
                (current as? DownloadedMapDetailsViewState.Loaded)
                    ?.copy(
                        region = updatedRegion,
                    )
                    ?: current
            }

            offlineMapsAnalytics.trackDownloadStarted(
                regionName = region.name,
                ongoingRegionDownloads = 0,
            )
        }.onFailure { e ->
            Timber.w(e, "Failed to download region: id(${region.name})")
        }
    }

    /**
     * @return true if successfully deleted, or false otherwise.
     */
    suspend fun deleteRegion(
        region: OfflineRegionResult.OfflineRegion,
        deleteFrom: OfflineMapDownloadTarget,
    ) = runSuspendCatching {
        updateJob?.join() // wait for possible update job to complete

        val updatedRegion = downloadOperatorsDelegate.deleteDownload(
            region = region,
            deleteFrom = setOf(deleteFrom),
        ).addDownloadOrdersIfAbsent(region.downloadOrders)
        _viewState.update { current ->
            (current as? DownloadedMapDetailsViewState.Loaded)
                ?.copy(
                    region = updatedRegion,
                )
                ?: current
        }

        offlineMapsAnalytics.trackDownloadDeleted(regionName = region.name)
    }.onFailure { e ->
        Timber.w(e, "Failed to delete downloaded region: id(${region.name})")
    }
}
