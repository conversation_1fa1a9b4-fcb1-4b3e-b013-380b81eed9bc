package com.stt.android.home.explore.offlinemaps.selection

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.VisibleForTesting
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.home.explore.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.domain.OfflineRegionDownloadOperators
import com.stt.android.home.explore.offlinemaps.entities.FreeSpaceAvailable
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapDownloadTarget
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapRegionAndSelectState
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionDownloadError
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionListData
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionStatus
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMarker
import com.stt.android.offlinemaps.domain.SetWatchKeyUseCase
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.watch.wifi.domain.GetSavedWifiNetworksCountUseCase
import com.stt.android.watch.wifi.domain.GetWifiEnabledUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
internal class OfflineMapsSelectionViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val offlineRegionRepository: OfflineRegionRepository,
    private val lastKnownLocationUseCase: LastKnownLocationUseCase,
    private val savedWifiNetworksCountUseCase: GetSavedWifiNetworksCountUseCase,
    isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getWifiEnabledUseCase: GetWifiEnabledUseCase,
    private val setWatchKeyUseCase: SetWatchKeyUseCase,
    private val downloadOperators: OfflineRegionDownloadOperators,
    @SuuntoSharedPrefs sharedPreferences: SharedPreferences,
    private val offlineMapsAnalytics: OfflineMapsAnalytics,
    private val userSettingsController: UserSettingsController,
    private val dispatchers: CoroutinesDispatchers,
    private val featureToggle: FeatureToggle,
) : ViewModel() {
    private val isWatchConnected: StateFlow<Boolean> = isWatchConnectedUseCase()
        .catch { e -> Timber.w(e, "Unable to observe watch connection state.") }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = true,
        )

    @OptIn(ExperimentalCoroutinesApi::class)
    private val savedWifiNetworksCount: StateFlow<Int> = isWatchConnected
        .flatMapLatest { isWatchConnected ->
            if (isWatchConnected) {
                savedWifiNetworksCountUseCase()
                    .catch { e -> Timber.w(e, "Unable to observe saved networks count.") }
            } else {
                emptyFlow()
            }
        }
        .onEach { savedWifiNetworksCount ->
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(
                        showBatteryInfoWithPendingDownload = isWifiEnabled.value && savedWifiNetworksCount > 0,
                    )
                    ?: current
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            // By default, assume we have wifi setup. We do not want to ask the user to setup WiFi
            // if e.g. watch is connected, but getting network count takes time.
            initialValue = 1,
        )

    @OptIn(ExperimentalCoroutinesApi::class)
    private val isWifiEnabled: StateFlow<Boolean> = isWatchConnected
        .flatMapLatest { isWatchConnected ->
            if (isWatchConnected) {
                getWifiEnabledUseCase()
                    .catch { e -> Timber.w(e, "Unable to observe WiFi enabled state.") }
            } else {
                emptyFlow()
            }
        }
        .onEach { isWifiEnabled ->
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(
                        showBatteryInfoWithPendingDownload = isWifiEnabled && savedWifiNetworksCount.value > 0,
                        showWifiDisabledInfoWithPendingDownload = false,
                    )
                    ?: current
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = true, // set initially true until value received from watch
        )

    private val _viewState: MutableStateFlow<OfflineMapsSelectionViewState> =
        MutableStateFlow(OfflineMapsSelectionViewState.Loading)
    val viewState: StateFlow<OfflineMapsSelectionViewState> = _viewState.asStateFlow()

    private val _navAction: MutableSharedFlow<OfflineMapsSelectionNavigationAction> =
        MutableSharedFlow()
    val navAction: SharedFlow<OfflineMapsSelectionNavigationAction> = _navAction.asSharedFlow()

    var offlineRegionCatalogue: OfflineRegionListData.Catalogue
        get() = (_viewState.value as? OfflineMapsSelectionViewState.Loaded)
            ?.catalogue
            ?: OfflineRegionListData.Catalogue()
        private set(value) {
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(catalogue = value)
                    ?: current
            }

            recalculateUpdateAvailableCountAndFreeSpaceAvailable(value)
        }

    private var selectedOfflineRegion: OfflineRegionResult.OfflineRegion?
        get() = (_viewState.value as? OfflineMapsSelectionViewState.Loaded)?.selectedRegion
        set(value) {
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(selectedRegion = value)
                    ?: current
            }
        }

    private var selectedOfflineRegionGroup: OfflineRegionResult.OfflineRegionGroup?
        get() = (_viewState.value as? OfflineMapsSelectionViewState.Loaded)?.selectedRegionGroup
        set(value) {
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(selectedRegionGroup = value)
                    ?: current
            }
        }

    private var loadJob: Job? = null

    private var updateJob: Job? = null

    private var totalSpace: Long? = null

    var userStartedRegionDownload = false
        private set

    var allChinaRegionsAndSelectState = mutableStateListOf<OfflineMapRegionAndSelectState>()

    var chinaOfflineRegionOverlays = emptyList<SuuntoChinaOfflineRegion>()

    var chinaRegionDownloadStateMarkers = emptyList<SuuntoMarker>()

    // only for download china region
    val selectedChinaDownloadRegions = mutableStateListOf<OfflineRegionResult.OfflineRegion>()

    // need check the watch storage space, when select china region
    var watchStorageFullState = mutableStateOf(false)

    init {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_HAS_OPENED_OFFLINE_MAPS, true)
        }

        load()
    }

    private fun load() {
        loadJob = viewModelScope.launch {
            runSuspendCatching {
                totalSpace = downloadOperators.getMapStorageSize()
            }.onFailure { e ->
                Timber.w(e, "Determining total storage space failed")
            }

            runSuspendCatching {
                val hourAgo = System.currentTimeMillis() - 1.hours.inWholeMilliseconds
                val latLng = lastKnownLocationUseCase.getLastKnownLocation(
                    skipPassiveProvider = false,
                    timeInMilliSecondsSinceEpoch = hourAgo
                )
                coroutineScope {
                    val catalogueDeferred =
                        async { offlineRegionRepository.getOfflineRegionCatalogue(latLng) }

                    runSuspendCatching { setWatchKeyUseCase.setToken() }

                    val catalogue = catalogueDeferred.await()

                    _viewState.value = OfflineMapsSelectionViewState.Loaded(
                        supportsOfflineMapsOnMobile = featureToggle[FeatureFlag.EnableOfflineMapOnMobile],
                        showBatteryInfoWithPendingDownload = false,
                        showWifiDisabledInfoWithPendingDownload = false,
                        showWifiSetupInfoWithPendingDownload = false,
                        catalogue = catalogue,
                        selectedRegionGroup = null,
                        selectedRegion = null,
                        showDownloadInfo = savedStateHandle[OfflineMapsSelectionActivity.KEY_SHOW_DOWNLOAD_INFO]
                            ?: false,
                        downloadError = null,
                        cancellingDownload = false,
                        requestingDownload = false,
                        updateAvailableCount = 0,
                        freeSpaceAvailable = null,
                        showDeleteConfirmation = false,
                        deleteFrom = null,
                    )

                    recalculateUpdateAvailableCountAndFreeSpaceAvailable(catalogue)
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to get offline region groups")
            }
        }
    }

    private fun recalculateUpdateAvailableCountAndFreeSpaceAvailable(
        catalogue: OfflineRegionListData.Catalogue,
    ) {
        viewModelScope.launch(dispatchers.computation) {
            val updateAvailableCount = catalogue.groups.sumOf { group ->
                group.regions.count(OfflineRegionResult.OfflineRegion::updateAvailable)
            }

            val freeSpaceAvailable = totalSpace?.let { total ->
                val used = catalogue.groups.sumOf { group ->
                    group.regions.sumOf { region ->
                        region.sizeForWatch
                            ?.storageSizeInBytes
                            ?.takeIf { region.downloadRequested || region.downloading || region.downloaded }
                            ?: 0L
                    }
                }
                val free = total - used
                FreeSpaceAvailable(
                    freeSpace = total - used,
                    freeSpacePercentage = (100.0 * free / total).roundToInt(),
                )
            }

            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(
                        updateAvailableCount = updateAvailableCount,
                        freeSpaceAvailable = freeSpaceAvailable,
                    )
                    ?: current
            }
        }
    }

    fun handleEvent(event: OfflineMapsSelectionViewEvent) {
        when (event) {
            OfflineMapsSelectionViewEvent.ShowDownloadInfo -> showDownloadInfo()
            OfflineMapsSelectionViewEvent.DismissDownloadInfo -> dismissDownloadInfo()
            is OfflineMapsSelectionViewEvent.DownloadOfflineRegion -> downloadOfflineRegion(event.downloadTo, event.regionId)
            is OfflineMapsSelectionViewEvent.RetryDownloadOfflineRegion -> retryDownloadOfflineRegion(event.regionId)
            is OfflineMapsSelectionViewEvent.CancelDownloadingOfflineRegion -> cancelDownload(event.regionId)
            OfflineMapsSelectionViewEvent.MarkDownloadErrorAsShown -> markDownloadErrorAsShown()
            is OfflineMapsSelectionViewEvent.SelectOfflineRegion -> selectOfflineRegion(event.region)
            is OfflineMapsSelectionViewEvent.SelectOfflineRegionGroup -> selectOfflineRegionGroup(
                event.regionGroup
            )

            OfflineMapsSelectionViewEvent.NavigateUp -> navigateUp()
            OfflineMapsSelectionViewEvent.DismissWifiSetupInfo -> dismissWifiSetupInfo()
            OfflineMapsSelectionViewEvent.DismissWifiDisabledInfo -> dismissWifiDisabledInfo()
            OfflineMapsSelectionViewEvent.DismissBatteryInfo -> dismissBatteryInfo()
            OfflineMapsSelectionViewEvent.TrackSearchModeEnabled -> offlineMapsAnalytics.trackSearchScreen()
            is OfflineMapsSelectionViewEvent.ShowDeleteConfirmation -> showDeleteConfirmation(event.deleteFrom)
            OfflineMapsSelectionViewEvent.DismissDeleteConfirmation -> dismissDeleteConfirmation()
            is OfflineMapsSelectionViewEvent.DeleteRegion -> deleteRegion(event.deleteFrom, event.region)
        }
    }

    private fun showDownloadInfo() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showDownloadInfo = true,
                ) ?: current
        }
    }

    private fun dismissDownloadInfo() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showDownloadInfo = false,
                ) ?: current
        }
    }

    private fun markDownloadErrorAsShown() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    downloadError = null,
                ) ?: current
        }
    }

    private fun selectOfflineRegion(region: OfflineRegionResult.OfflineRegion) {
        selectedOfflineRegion = region
        getAllChinaRegions(region)

        viewModelScope.launch {
            _navAction.emit(OfflineMapsSelectionNavigationAction.OpenPreview)
        }
    }

    private fun selectOfflineRegionGroup(regionGroup: OfflineRegionResult.OfflineRegionGroup) {
        selectedOfflineRegionGroup = regionGroup

        viewModelScope.launch {
            _navAction.emit(OfflineMapsSelectionNavigationAction.OpenRegionGroup)
        }
    }

    private fun navigateUp() {
        viewModelScope.launch {
            _navAction.emit(OfflineMapsSelectionNavigationAction.NavigateUp)
        }
    }

    private fun dismissWifiSetupInfo() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showWifiSetupInfoWithPendingDownload = false,
                ) ?: current
        }
        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WIFI_NOT_SETUP)
    }

    private fun dismissWifiDisabledInfo() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showWifiDisabledInfoWithPendingDownload = false,
                ) ?: current
        }
        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WIFI_OFF)
    }

    private fun dismissBatteryInfo() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showBatteryInfoWithPendingDownload = false,
                ) ?: current
        }
    }

    private fun showDeleteConfirmation(deleteFrom: OfflineMapDownloadTarget) {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showDeleteConfirmation = true,
                    deleteFrom = deleteFrom,
                ) ?: current
        }
    }

    private fun dismissDeleteConfirmation() {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    showDeleteConfirmation = false,
                    deleteFrom = null,
                ) ?: current
        }
    }

    private fun deleteRegion(deleteFrom: OfflineMapDownloadTarget, region: OfflineRegionResult.OfflineRegion) {
        viewModelScope.launch {
            runSuspendCatching {
                actOnRegion(region) { target: OfflineRegionResult.OfflineRegion ->
                    downloadOperators.deleteDownload(
                        deleteFrom = setOf(deleteFrom),
                        region = region,
                    ).addDownloadOrdersIfAbsent(region.downloadOrders)
                }

                offlineMapsAnalytics.trackDownloadDeleted(regionName = region.name)
            }.onFailure { e ->
                Timber.w(e, "Failed to download region: id(${region.name})")
            }
        }
    }

    private fun setChinaOfflineRegionOverlaySelectState(regionId: String, color: Int) {
        val findOverLay =
            chinaOfflineRegionOverlays.find { it.id == regionId }
        findOverLay?.let { overlay ->
            overlay.mask?.setLayerColor(color)
        }
    }

    /**
     * when zoom out map and the scale < MARKER_SCALE_THRESHOLD_VALUE, and scale the markers to make them smaller
     */
    fun scaleMarkers(scale: Float) {
        if (scale < MARKER_SCALE_THRESHOLD_VALUE) {
            chinaRegionDownloadStateMarkers.forEach {
                it.setIconScale(scale / SCALE_VALUE)
            }
        } else {
            chinaRegionDownloadStateMarkers.forEach {
                it.setIconScale(1f)
            }
        }
    }

    private fun getAllChinaRegions(region: OfflineRegionResult.OfflineRegion) {
        allChinaRegionsAndSelectState.clear()
        selectedChinaDownloadRegions.clear()
        val group = offlineRegionCatalogue.groups.find { it.name == region.groupName }
        group?.apply {
            if (batchDownloadAllowed == true) {
                allChinaRegionsAndSelectState.addAll(
                    regions.map {
                        OfflineMapRegionAndSelectState(it, !it.downloadAvailable)
                    }.filter { it.region.id != region.id }
                )
            }
        }
    }

    fun selectChinaRegion(context: Context, regionId: String) {
        val index = allChinaRegionsAndSelectState.indexOfFirst { it.region.id == regionId }
        if (index >= 0) {
            val offlineMapRegionAndSelectState = allChinaRegionsAndSelectState[index]
            updateWatchStorageSpace(offlineMapRegionAndSelectState.region)
            if (!watchStorageFullState.value) {
                allChinaRegionsAndSelectState[index] =
                    offlineMapRegionAndSelectState.copy(
                        selectState = !offlineMapRegionAndSelectState.selectState
                    )
                val currentSelectRegion = allChinaRegionsAndSelectState[index]
                val selectState = currentSelectRegion.selectState
                if (selectState) {
                    selectedChinaDownloadRegions.add(currentSelectRegion.region)
                } else {
                    selectedChinaDownloadRegions.remove(currentSelectRegion.region)
                }
                setChinaOfflineRegionOverlaySelectState(
                    regionId,
                    if (selectState) {
                        ContextCompat.getColor(context, R.color.suunto_blue)
                    } else {
                        ContextCompat.getColor(context, R.color.near_white)
                    }
                )
            }
        }
    }

    private fun updateWatchStorageSpace(addRegion: OfflineRegionResult.OfflineRegion) {
        val selectedChinaRegionSize = selectedChinaDownloadRegions.sumOf { region ->
            region.sizeForWatch?.storageSizeInBytes ?: 0
        } +
            (addRegion.sizeForWatch?.storageSizeInBytes ?: 0)

        (_viewState.value as? OfflineMapsSelectionViewState.Loaded)
            ?.freeSpaceAvailable
            ?.freeSpace
            ?.let { freeSpace ->
                watchStorageFullState.value =
                    freeSpace - (selectedChinaRegionSize + (selectedOfflineRegion?.sizeForWatch?.storageSizeInBytes
                        ?: 0)) < 0
            }
    }

    private fun downloadOfflineRegion(downloadTo: OfflineMapDownloadTarget, regionId: String) {
        val selectedChinaDownloadRegionIds = selectedChinaDownloadRegions.map(OfflineRegionResult.OfflineRegion::id)
        val downloadIds = listOf(regionId) + selectedChinaDownloadRegionIds
        setRequestingDownload(
            requestingDownload = true,
        )
        viewModelScope.launch {
            updateJob?.join() // wait for possible update job to complete

            userStartedRegionDownload = true
            _viewState.update { current ->
                (current as? OfflineMapsSelectionViewState.Loaded)
                    ?.copy(
                        showWifiDisabledInfoWithPendingDownload = isWatchConnected.value && !isWifiEnabled.value,
                        showWifiSetupInfoWithPendingDownload = savedWifiNetworksCount.value == 0 &&
                            isWatchConnected.value &&
                            isWifiEnabled.value,
                    ) ?: current
            }

            downloadIds.forEach { id ->
                val region = getRegion(id) ?: return@forEach
                runSuspendCatching {
                    if (region.downloadAvailable) {
                        actOnRegion(region) { target: OfflineRegionResult.OfflineRegion ->
                            downloadOperators.downloadOfflineRegion(
                                downloadTo = downloadTo,
                                regionId = target.id,
                                groupName = region.groupName,
                            ).addDownloadOrders(region.downloadOrders)
                        }
                    }
                    offlineMapsAnalytics.trackDownloadStarted(
                        regionName = region.name,
                        ongoingRegionDownloads = offlineRegionCatalogue.groups.flatMap { it.regions }
                            .count { it.downloading || it.downloadRequested },
                    )
                }.onFailure { e ->
                    Timber.w(e, "Failed to download region id:$id: ids($downloadIds)")

                    val downloadError = OfflineRegionDownloadError(
                        regionName = region.name,
                        description = if (e is ClientError.Conflict) {
                            e.description // Localized description from the backend.
                        } else {
                            null
                        }
                    )
                    _viewState.update { current ->
                        (current as? OfflineMapsSelectionViewState.Loaded)
                            ?.copy(
                                downloadError = downloadError,
                            ) ?: current
                    }

                    if (e is ClientError.Conflict) {
                        trackDownloadBlocked(AnalyticsPropertyValue.DownloadMapsIssueType.WATCH_STORAGE_FULL)
                    }
                }
            }
            setRequestingDownload(
                requestingDownload = false,
            )
        }
    }

    private fun retryDownloadOfflineRegion(regionId: String) {
        val toDownload = getRegion(regionId) ?: return
        val downloadTo = when {
            toDownload.downloadFailedForWatch -> OfflineMapDownloadTarget.WATCH
            toDownload.downloadFailedForMobile -> OfflineMapDownloadTarget.MOBILE
            else -> return
        }
        downloadOfflineRegion(downloadTo, regionId)
    }

    private fun setRequestingDownload(requestingDownload: Boolean) {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    requestingDownload = requestingDownload,
                ) ?: current
        }
    }

    private fun cancelDownload(regionId: String) {
        setCancellingDownload(cancellingDownload = true)

        viewModelScope.launch {
            runSuspendCatching {
                updateJob?.join() // wait for possible update job to complete
                val region = getRegion(regionId) ?: return@runSuspendCatching
                if (region.cancellable) {
                    actOnRegion(region) { toDelete ->
                        val deleteFrom = buildSet {
                            val mobileUuid = userSettingsController.settings.analyticsUUID
                            toDelete.downloadOrders.forEach { downloadOrder ->
                                if (downloadOrder.status == OfflineRegionStatus.REQUESTED ||
                                    downloadOrder.status == OfflineRegionStatus.IN_PROGRESS
                                ) {
                                    if (mobileUuid == downloadOrder.deviceSerialNumber) {
                                        add(OfflineMapDownloadTarget.MOBILE)
                                    } else {
                                        add(OfflineMapDownloadTarget.WATCH)
                                    }
                                }
                            }
                        }
                        downloadOperators.deleteDownload(
                            region = toDelete,
                            deleteFrom = deleteFrom,
                        )
                    }
                }

                offlineMapsAnalytics.trackDownloadCancelled(
                    regionName = region.name,
                    ongoingRegionDownloads = offlineRegionCatalogue.groups.flatMap { it.regions }
                        .count { it.downloading || it.downloadRequested },
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to cancel download: id($regionId)")
            }
            setCancellingDownload(cancellingDownload = false)
        }
    }

    private fun setCancellingDownload(cancellingDownload: Boolean) {
        _viewState.update { current ->
            (current as? OfflineMapsSelectionViewState.Loaded)
                ?.copy(
                    cancellingDownload = cancellingDownload,
                ) ?: current
        }
    }

    private suspend fun actOnRegion(
        region: OfflineRegionResult.OfflineRegion,
        action: suspend (OfflineRegionResult.OfflineRegion) -> OfflineRegionResult.OfflineRegion,
    ) {
        val group = offlineRegionCatalogue.groups.first { it.regions.contains(region) }
        val updatedRegion = action(region)
        val updatedGroup = replaceRegion(group, region, updatedRegion)
        val updatedNearby = replaceNearby(offlineRegionCatalogue.nearby, region, updatedRegion)
        selectedOfflineRegion = updatedRegion
        selectedOfflineRegionGroup = updatedGroup
        val updatedGroups = replaceGroup(group, updatedGroup)
        offlineRegionCatalogue = OfflineRegionListData.Catalogue(
            nearby = updatedNearby,
            groups = updatedGroups
        )
    }

    private fun replaceRegion(
        group: OfflineRegionResult.OfflineRegionGroup,
        region: OfflineRegionResult.OfflineRegion,
        updated: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegionGroup {
        val updatedGroup = group.copy(
            regions = group.regions.map {
                when (it.id) {
                    region.id -> {
                        updated.copy(adjacentRegions = it.adjacentRegions)
                    }

                    else -> it
                }
            }.toPersistentList()
        )
        return updatedGroup
    }

    private fun replaceNearby(
        nearby: ImmutableList<OfflineRegionResult>,
        region: OfflineRegionResult.OfflineRegion,
        updated: OfflineRegionResult.OfflineRegion
    ): PersistentList<OfflineRegionResult> = nearby.map {
        if (it is OfflineRegionResult.OfflineRegion && it.id == region.id) {
            updated
        } else {
            it
        }
    }.toPersistentList()

    private fun replaceGroup(
        group: OfflineRegionResult.OfflineRegionGroup,
        updatedGroup: OfflineRegionResult.OfflineRegionGroup
    ): PersistentList<OfflineRegionResult.OfflineRegionGroup> = offlineRegionCatalogue.groups.map {
        when (it.id) {
            group.id -> updatedGroup
            else -> it
        }
    }.toPersistentList()

    suspend fun pollDownloadOrderStatuses() {
        loadJob?.join()

        while (true) {
            delay(15.seconds)

            val notRequestingNorCancellingDownload =
                (_viewState.value as? OfflineMapsSelectionViewState.Loaded)
                    ?.let { loaded ->
                        !loaded.requestingDownload && !loaded.cancellingDownload
                    }
                    ?: false
            if (notRequestingNorCancellingDownload) {
                updateJob = viewModelScope.launch(dispatchers.computation) {
                    updateDownloadOrderStatuses()
                }
                updateJob?.join()
            }
        }
    }

    @VisibleForTesting
    suspend fun updateDownloadOrderStatuses() = runSuspendCatching {
        Timber.d("Updating download order statuses...")
        val library = offlineRegionRepository.getLibrary()
        val catalogue = offlineRegionCatalogue
        val nearby = catalogue.nearby
        val groups = catalogue.groups
        val downloadOrders = library.associate { it.id to it.downloadOrders }

        val updatedNearby = nearby.map {
            if (it is OfflineRegionResult.OfflineRegion) {
                it.copy(downloadOrders = downloadOrders[it.id] ?: persistentListOf())
            } else {
                it
            }
        }.toPersistentList()

        val updatedGroups = groups.map { group ->
            val regions = group.regions.map { region ->
                region.copy(downloadOrders = downloadOrders[region.id] ?: persistentListOf())
            }
            group.copy(regions = regions.toPersistentList())
        }.toPersistentList()

        val updatedCatalogue = OfflineRegionListData.Catalogue(
            nearby = updatedNearby,
            groups = updatedGroups
        )

        selectedOfflineRegion = selectedOfflineRegion?.id?.let { regionId ->
            updatedCatalogue.groups.flatMap { it.regions }.firstOrNull { it.id == regionId }
        }
        selectedOfflineRegionGroup = selectedOfflineRegionGroup?.id?.let { groupId ->
            updatedCatalogue.groups.firstOrNull { it.id == groupId }
        }

        offlineRegionCatalogue = updatedCatalogue
        Timber.d("DONE: Updating download order statuses...")
    }.onFailure { Timber.w(it, "Failed to update download order statuses") }

    private fun getRegion(regionId: String): OfflineRegionResult.OfflineRegion? =
        offlineRegionCatalogue.groups.flatMap { it.regions }.firstOrNull { it.id == regionId }

    private fun trackDownloadBlocked(issueType: String) {
        offlineMapsAnalytics.trackDownloadBlocked(issueType)
    }

    companion object {
        const val MARKER_SCALE_THRESHOLD_VALUE = 4.0f
        const val SCALE_VALUE = 5.0f
    }
}
