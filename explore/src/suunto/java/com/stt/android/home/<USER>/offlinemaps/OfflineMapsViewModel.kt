package com.stt.android.home.explore.offlinemaps

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.home.explore.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.domain.OfflineRegionDownloadOperators
import com.stt.android.home.explore.offlinemaps.entities.MemoryUsage
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapDownloadTarget
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
internal class OfflineMapsViewModel @Inject constructor(
    private val offlineRegionRepository: OfflineRegionRepository,
    private val downloadOperatorsDelegate: OfflineRegionDownloadOperators,
    private val offlineMapsAnalytics: OfflineMapsAnalytics,
    private val userSettingsController: UserSettingsController,
    private val featureToggle: FeatureToggle,
) : ViewModel() {
    private val _viewState: MutableStateFlow<OfflineMapsViewState> =
        MutableStateFlow(OfflineMapsViewState.Loading)
    val viewState: StateFlow<OfflineMapsViewState> = _viewState.asStateFlow()

    private var loadJob: Job? = null
    private var updateJob: Job? = null
    private var pendingReload = false

    private var totalSpace: Long? = null

    init {
        loadTotalStorageSpace()
        loadOfflineRegionGroups()
    }

    private fun loadTotalStorageSpace() = viewModelScope.launch {
        runSuspendCatching {
            totalSpace = downloadOperatorsDelegate.getMapStorageSize()
        }.onFailure { e ->
            Timber.w(e, "Determining total storage space failed")
        }
    }

    private fun loadOfflineRegionGroups(showLoadingState: Boolean = true) {
        loadJob = viewModelScope.launch {
            if (showLoadingState) {
                _viewState.value = OfflineMapsViewState.Loading
            }

            runSuspendCatching {
                coroutineScope {
                    val offlineRegionsAsync = async { offlineRegionRepository.getLibrary() }
                    val offlineRegions = offlineRegionsAsync.await()

                    _viewState.value = OfflineMapsViewState.Loaded(
                        supportsOfflineMapsOnMobile = featureToggle[FeatureFlag.EnableOfflineMapOnMobile],
                        memoryUsage = offlineRegions.calculateMemoryUsage(totalSpace),
                        offlineRegions = offlineRegions,
                        showBatteryTip = false,
                        cancellingDownload = false,
                        requestingDownload = false,
                        nameOfFailedDownloadRegion = null,
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to get offline region groups")
            }
        }
    }

    suspend fun pollOfflineRegionGroups() {
        loadJob?.join()

        while (true) {
            delay(calculatePollDelay())

            val (cancellingDownload, requestingDownload) = (_viewState.value as? OfflineMapsViewState.Loaded)
                ?.let { loaded -> loaded.cancellingDownload to loaded.requestingDownload }
                ?: (false to false)
            if (!cancellingDownload && !requestingDownload) {
                updateJob = viewModelScope.launch {
                    runSuspendCatching {
                        Timber.d("Updating download order statuses...")

                        val offlineRegions = offlineRegionRepository.getLibrary()
                        _viewState.update { current ->
                            (current as? OfflineMapsViewState.Loaded)
                                ?.let { loaded ->
                                    val mobileUuid = userSettingsController.settings.analyticsUUID
                                    loaded.copy(
                                        memoryUsage = offlineRegions.calculateMemoryUsage(totalSpace),
                                        offlineRegions = offlineRegions,
                                        showBatteryTip = offlineRegions.any { offlineRegion ->
                                            offlineRegion.downloadOrders.any { downloadOrder ->
                                                downloadOrder.deviceSerialNumber != mobileUuid &&
                                                    downloadOrder.status == OfflineRegionStatus.REQUESTED
                                            }
                                        },
                                    )
                                }
                                ?: current
                        }
                    }.onFailure { e ->
                        Timber.w(e, "Failed to update download order statuses")
                    }
                }
                updateJob?.join()
            }
        }
    }

    private fun calculatePollDelay(): Duration {
        val isDownloadingForMobile = (_viewState.value as? OfflineMapsViewState.Loaded)
            ?.offlineRegions
            ?.any { region -> region.downloadingForMobile }
        return if (isDownloadingForMobile == true) 5.seconds else 15.seconds
    }

    fun checkPendingReload() {
        if (pendingReload) {
            pendingReload = false
            loadOfflineRegionGroups()
        }
    }

    fun retryDownload(region: OfflineRegionResult.OfflineRegion) {
        viewModelScope.launch {
            val downloadTo = when {
                region.downloadFailedForWatch -> OfflineMapDownloadTarget.WATCH
                region.downloadFailedForMobile -> OfflineMapDownloadTarget.MOBILE
                else -> return@launch
            }

            updateIsRequestingDownload(isRequestingDownload = true)

            runSuspendCatching {
                updateJob?.join() // wait for possible update job to complete

                val updatedOfflineRegions = updateOfflineRegion(
                    updatedRegion = downloadOperatorsDelegate.downloadOfflineRegion(
                        downloadTo = downloadTo,
                        regionId = region.id,
                    ),
                ) ?: return@runSuspendCatching

                offlineMapsAnalytics.trackDownloadStarted(
                    regionName = region.name,
                    ongoingRegionDownloads = updatedOfflineRegions.count { it.downloading || it.downloadRequested },
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to download region: id(${region.id}) name(${region.name})")
                _viewState.update { current ->
                    (current as? OfflineMapsViewState.Loaded)
                        ?.copy(nameOfFailedDownloadRegion = region.name)
                        ?: current
                }
            }

            updateIsRequestingDownload(isRequestingDownload = false)
        }
    }

    private fun updateIsRequestingDownload(isRequestingDownload: Boolean) {
        _viewState.update { current ->
            (current as? OfflineMapsViewState.Loaded)
                ?.copy(requestingDownload = isRequestingDownload)
                ?: current
        }
    }

    private fun updateOfflineRegion(
        updatedRegion: OfflineRegionResult.OfflineRegion,
    ): List<OfflineRegionResult.OfflineRegion>? {
        var updatedOfflineRegions: List<OfflineRegionResult.OfflineRegion>? = null

        _viewState.update { current ->
            (current as? OfflineMapsViewState.Loaded)
                ?.let { loaded ->
                    updatedOfflineRegions = loaded.offlineRegions
                        .map { region ->
                            if (region.id == updatedRegion.id) {
                                updatedRegion
                            } else {
                                region
                            }
                        }
                    loaded.copy(
                        memoryUsage = updatedOfflineRegions.calculateMemoryUsage(totalSpace),
                        offlineRegions = updatedOfflineRegions.toPersistentList(),
                        showBatteryTip = updatedOfflineRegions.count { it.downloadRequested } > 0,
                    )
                }
                ?: current
        }

        return updatedOfflineRegions
    }

    fun cancelDownload(region: OfflineRegionResult.OfflineRegion) {
        viewModelScope.launch {
            updateIsCancellingDownload(isCancellingDownload = true)

            runSuspendCatching {
                val deleteFrom = buildSet {
                    val mobileUuid = userSettingsController.settings.analyticsUUID
                    region.downloadOrders.forEach { downloadOrder ->
                        if (downloadOrder.status == OfflineRegionStatus.REQUESTED ||
                            downloadOrder.status == OfflineRegionStatus.IN_PROGRESS
                        ) {
                            if (mobileUuid == downloadOrder.deviceSerialNumber) {
                                add(OfflineMapDownloadTarget.MOBILE)
                            } else {
                                add(OfflineMapDownloadTarget.WATCH)
                            }
                        }
                    }
                }
                val updatedOfflineRegions = updateOfflineRegion(
                    updatedRegion = downloadOperatorsDelegate.deleteDownload(
                        region = region,
                        deleteFrom = deleteFrom,
                    ),
                ) ?: return@runSuspendCatching

                offlineMapsAnalytics.trackDownloadCancelled(
                    regionName = region.name,
                    ongoingRegionDownloads = updatedOfflineRegions.count { it.downloading || it.downloadRequested },
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to cancel download: id(${region.id}) name(${region.name})")
            }

            updateIsCancellingDownload(isCancellingDownload = false)
        }
    }

    private fun updateIsCancellingDownload(isCancellingDownload: Boolean) {
        _viewState.update { current ->
            (current as? OfflineMapsViewState.Loaded)
                ?.copy(cancellingDownload = isCancellingDownload)
                ?: current
        }
    }

    fun cancelDeleting(region: OfflineRegionResult.OfflineRegion) {
        viewModelScope.launch {
            val deleteFrom = buildSet {
                when {
                    region.downloadingForMobile -> add(OfflineMapDownloadTarget.MOBILE)
                    region.downloadingForWatch -> add(OfflineMapDownloadTarget.WATCH)
                    else -> return@launch
                }
            }
            val updatedRegion = downloadOperatorsDelegate.deleteDownload(
                deleteFrom = deleteFrom,
                region = region,
            ).addDownloadOrdersIfAbsent(region.downloadOrders)
            updateOfflineRegion(updatedRegion)
        }
    }

    fun setPendingReload() {
        pendingReload = true
    }

    fun markBatteryTipAsShown() {
        _viewState.update { current ->
            (current as? OfflineMapsViewState.Loaded)
                ?.copy(showBatteryTip = false)
                ?: current
        }
    }

    fun markFailedToDownloadRegionAsShown() {
        _viewState.update { current ->
            (current as? OfflineMapsViewState.Loaded)
                ?.copy(nameOfFailedDownloadRegion = null)
                ?: current
        }
    }

    private companion object {
        fun List<OfflineRegionResult.OfflineRegion>.calculateMemoryUsage(
            totalSpace: Long?,
        ): MemoryUsage? {
            if (totalSpace == null || totalSpace <= 0L) {
                return null
            }

            var used = 0L
            var queued = 0L
            var deleted = 0L
            var updated = 0L
            var downloaded = 0L

            forEach { region ->
                val size = region.sizeForWatch?.storageSizeInBytes ?: return@forEach

                if (region.downloadRequested || region.downloading || region.downloaded) {
                    used += size
                }

                if (region.downloadRequested || (region.downloading && !region.downloadingUpdate)) {
                    queued += size
                }

                if (region.deleteRequested) {
                    deleted += size
                }

                if (region.updateAvailable || region.downloadingUpdate) {
                    updated += size
                }

                if (region.downloaded) {
                    downloaded += size
                }
            }

            val usedCoerced = used.coerceAtMost(totalSpace)
            val free = totalSpace - usedCoerced

            return MemoryUsage(
                free = free,
                freePercentage = (100.0F * free / totalSpace).roundToInt(),
                used = usedCoerced,
                total = totalSpace,
                queued = queued,
                deleted = deleted,
                updated = updated,
                downloaded = downloaded,
            )
        }
    }
}
