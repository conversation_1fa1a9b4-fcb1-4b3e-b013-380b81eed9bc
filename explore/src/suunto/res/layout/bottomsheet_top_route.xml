<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.stt.android.ui.components.LockableNestedScrollView
        android:id="@+id/bottomSheetView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/route_planner_bottom_sheet_half_expanded_height"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bottom_sheet_rounded_top_corners_background"
            android:clickable="true"
            android:elevation="@dimen/bottom_sheet_elevation"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:paddingBottom="@dimen/size_spacing_medium">

            <include
                android:id="@+id/bottomSheet"
                layout="@layout/include_bottomsheet_top_route" />

            <include
                layout="@layout/turn_by_turn_switch" />

            <View
                android:id="@+id/dividerTurnByTurnViewBottom"
                style="@style/HorizontalDivider"
                app:layout_constraintBottom_toTopOf="@+id/addToWatchView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/turnByTurnView" />

            <com.stt.android.home.explore.routes.addtowatch.AddToWatchView
                android:id="@+id/addToWatchView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@+id/dividerWatchViewBottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dividerTurnByTurnViewBottom"
                tools:ignore="UnknownIdInLayout" />

            <View
                android:id="@+id/dividerWatchViewBottom"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                app:layout_constraintBottom_toTopOf="@+id/save"
                app:layout_constraintTop_toBottomOf="@+id/addToWatchView" />

            <Button
                android:id="@+id/save"
                style="@style/Button.Primary"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:text="@string/save"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dividerWatchViewBottom"/>

            <androidx.constraintlayout.widget.Group
                android:id="@+id/addToWatchViewGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="addToWatchView,dividerWatchViewBottom" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.stt.android.ui.components.LockableNestedScrollView>

</layout>
