package com.stt.android.datasource.explore.pois

import com.stt.android.data.source.local.pois.LocalPOISyncLogEvent
import com.stt.android.data.source.local.pois.OverallPOISyncEvent
import com.stt.android.data.source.local.pois.POIDao
import com.stt.android.data.source.local.pois.POISyncLogEventDao
import com.suunto.connectivity.poi.POISyncLogicResult
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.ZonedDateTime

interface POISyncLogic {
    val poiDao: POIDao
    val poiSyncLogEventDao: POISyncLogEventDao
    val watchEnabledPOIsMaxCount: Int

    suspend fun processSyncResult(
        hasNewData: Boolean,
        errorsLogs: List<String>,
        isWatchSync: Boolean,
        didExceedWatchEnabledPOILimit: Boolean
    ): POISyncLogicResult = withContext(NonCancellable) {
        val errorLog = errorsLogs.joinToString("\n").let {
            it.substring(0, it.length.coerceAtMost(1000))
        }.trim()
        try {
            // 1. Process error logs and save them in DB
            if (errorLog.isNotEmpty()) {
                logSyncEvent(
                    if (isWatchSync) OverallPOISyncEvent.WATCH_SYNC_ERROR else OverallPOISyncEvent.BACKEND_SYNC_ERROR,
                    errorLog
                )
            }

            if (didExceedWatchEnabledPOILimit) {
                // Notify user that some POIs were not synced to watch due to the limit
                logSyncEvent(OverallPOISyncEvent.WATCH_ENABLED_POI_LIMIT_EXCEEDED, shown = false)
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error in processSyncErrorsAndStopSync. isWatchSync:$isWatchSync")
        }

        when {
            errorLog.isNotEmpty() -> POISyncLogicResult.Failure(errorLog)
            hasNewData -> POISyncLogicResult.Success
            else -> POISyncLogicResult.NoNewData
        }
    }

    suspend fun stopSync() = withContext(NonCancellable) {
        logSyncEvent(OverallPOISyncEvent.IDLE)
    }

    suspend fun clearupLocalDeletedPOIs() {
        poiDao.deleteAllMarkedDeletedAndIdle()
    }

    private suspend fun logSyncEvent(
        event: OverallPOISyncEvent,
        metadata: String? = null,
        shown: Boolean? = null
    ) {
        val now = ZonedDateTime.now()
        poiSyncLogEventDao.insert(
            LocalPOISyncLogEvent(
                timestampMillis = now.toInstant().toEpochMilli(),
                timeISO8601 = now,
                event = event,
                metadata = metadata,
                shown = shown
            )
        )
        Timber.d("Logged POI sync event: $event")
    }
}
