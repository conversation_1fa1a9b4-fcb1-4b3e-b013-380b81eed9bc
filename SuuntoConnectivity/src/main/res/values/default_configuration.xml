<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--
    Empty by default. Consumers of this API need to provide the right values.
    See SdsManagerImpl.initialize(Context) method
    -->
    <string formatted="false" name="suunto_connectivity_service_type" translatable="false"/>
    <string formatted="false" name="suunto_connectivity_app_key" translatable="false"/>
    <!-- internal app subdirectory name where to store Suunto connectivity related files -->
    <string formatted="false" name="suunto_connectivity_subdirectory" translatable="false">suunto_connectivity</string>
    <item name="suunto_connectivity_notification_icon" type="drawable">
        @drawable/ic_foreground_notification
    </item>
    <item name="suunto_connectivity_notification_title" type="string">@string/local_service_label</item>
    <item name="suunto_connectivity_notification_content" type="string">@string/remote_service_started</item>
    <item name="suunto_connectivity_notification_cgps_content" type="string">@string/remote_service_started</item>
    <bool name="suunto_connectivity_debug">false</bool>
</resources>
