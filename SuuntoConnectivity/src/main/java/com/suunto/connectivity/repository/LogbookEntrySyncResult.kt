package com.suunto.connectivity.repository

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.suunto.connectivity.repository.SyncResult.Companion.unknown
import kotlinx.parcelize.Parcelize
import kotlin.jvm.JvmStatic

@Parcelize
data class LogbookEntrySyncResult internal constructor(
    @SerializedName("entryId")
    val entryId: Long,
    @SerializedName("summaryResult")
    val summaryResult: SyncResult,
    @SerializedName("samplesResult")
    val samplesResult: SyncResult,
    @SerializedName("smlZipResult")
    val smlZipResult: SyncResult?
) : Parcelable {

    fun toBuilder(): Builder = builder()
        .entryId(entryId)
        .summaryResult(summaryResult)
        .samplesResult(samplesResult)
        .smlZipResult(smlZipResult)

    /**
     * smlZipResult doesn't count as global success for entry sync
     */
    fun isSuccess(): Boolean {
        return (samplesResult.isAlreadySynced() || samplesResult.isSuccess()) &&
            (summaryResult.isAlreadySynced() || summaryResult.isSuccess())
    }

    fun isJustSyncedSuccessfully(): Boolean {
        return (samplesResult.isSuccess() && summaryResult.isSuccess()) ||
            (samplesResult.isSuccess() && summaryResult.isAlreadySynced()) ||
            (samplesResult.isAlreadySynced() && summaryResult.isSuccess())
    }

    class Builder internal constructor(
        private var entryId: Long = 0L,
        private var summaryResult: SyncResult = unknown(),
        private var samplesResult: SyncResult = unknown(),
        private var smlZipResult: SyncResult? = unknown()
    ) {
        fun entryId(entryId: Long): Builder = apply { this.entryId = entryId }

        fun summaryResult(result: SyncResult): Builder = apply { this.summaryResult = result }

        fun samplesResult(result: SyncResult): Builder = apply { this.samplesResult = result }

        fun smlZipResult(result: SyncResult?): Builder = apply { this.smlZipResult = result }

        fun build(): LogbookEntrySyncResult =
            LogbookEntrySyncResult(
                entryId = entryId,
                summaryResult = summaryResult,
                samplesResult = samplesResult,
                smlZipResult = smlZipResult
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
