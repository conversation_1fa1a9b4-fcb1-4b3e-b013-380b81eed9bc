package com.suunto.connectivity.notifications

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.suuntoconnectivity.ancs.AncsConstants

/**
 * MdsNotification object
 */
@JsonClass(generateAdapter = true)
data class MdsNotification(
    /**
     * @return Unique identifier for this notification
     */
    @<PERSON>son(name = "notificationId")
    val id: Int,
    /**
     * @return Contents of the notification
     */
    @<PERSON>son(name = "requestData")
    val requestData: MdsNotificationRequestData?,
) {
    companion object {
        /**
         * Create a new MdsNotification
         *
         * @param ancsMessage AncsMessage from which to create this MdsNotification
         * @return New MdsNotification
         */
        fun create(ancsMessage: AncsMessage): MdsNotification = create(ancsMessage, false)

        /**
         * Create MdsNotification for updating a previous notification with the same id.
         *
         * @param ancsMessage AncsMessage from which to create this MdsNotification
         * @return MdsNotification
         */
        fun createUpdate(ancsMessage: AncsMessage): MdsNotification = create(ancsMessage, true)

        private fun create(ancsMessage: AncsMessage, isUpdate: Boolean): MdsNotification {
            val filteredMessage = MessageTextFilter.filter(ancsMessage, false)

            val requestData = MdsNotificationRequestData(
                modifyExisting = isUpdate,
                categoryId = filteredMessage.categoryId,
                eventFlags = AncsConstants.EventFlag.IMPORTANT.toInt(),
                date = (filteredMessage.timestamp / 1000L).toInt(),
                title = filteredMessage.title,
                message = filteredMessage.message,
                categoryCount = filteredMessage.categoryCount,
                appId = filteredMessage.pkg,
                labels = filteredMessage.actions.map {
                    MdsNotificationActionLabel(
                        label = it.label,
                        supportsReply = it.supportsReply,
                    )
                }.takeIf { it.isNotEmpty() },
            )
            return MdsNotification(filteredMessage.id, requestData)
        }

        /**
         * Creates MdsNotification that does not have any data, only id
         *
         * @param messageId ID of the message from which to create this MdsNotification
         * @return New MdsNotification
         */
        @JvmStatic
        fun createEmpty(messageId: Int): MdsNotification {
            return MdsNotification(messageId, null)
        }
    }
}

/**
 * RequestData to go with MdsNotifications
 */
@JsonClass(generateAdapter = true)
data class MdsNotificationRequestData(
    /**
     * Whether this request is about modifying existing one
     */
    @Json(name = "modifyExisting")
    val modifyExisting: Boolean,
    /**
     * One of legal category ids defined in [AncsConstants.CategoryID]
     */
    @AncsConstants.Category
    @Json(name = "categoryId")
    val categoryId: Int,
    /**
     * Event flags specified in [AncsConstants.EventFlag]
     */
    @Json(name = "eventFlags")
    val eventFlags: Int,
    /**
     * Date as 32bit unix timestamp
     */
    @Json(name = "date")
    val date: Int,
    /**
     * Title for this notification
     */
    @Json(name = "title")
    val title: String?,
    /**
     * Message of this notification
     */
    @Json(name = "message")
    val message: String?,
    /**
     * CategoryCount of this notification
     */
    @Json(name = "categoryCount")
    val categoryCount: Int,
    /**
     * TODO clarify
     */
    @Json(name = "appId")
    val appId: String?,
    /**
     * TODO clarify spec
     */
    @Json(name = "labels")
    val labels: List<MdsNotificationActionLabel>?,
)

@JsonClass(generateAdapter = true)
data class MdsNotificationActionLabel(
    @Json(name = "label") val label: String,
    @Json(name = "supportsReply") val supportsReply: Boolean,
)

@JsonClass(generateAdapter = true)
data class MdsPredefinedRepliesRequest(
    @Json(name = "replies") val replies: MdsPredefinedLabels
)

@JsonClass(generateAdapter = true)
data class MdsPredefinedLabels(
    @Json(name = "labels") val labels: List<String>
)

@JsonClass(generateAdapter = true)
data class MdsNotificationEnabled(
    @Json(name = "enabled") val enabled: Boolean
)

@JsonClass(generateAdapter = true)
data class MdsNotificationCategoryEnabled(
    @Json(name = "call") val call: Boolean,
    @Json(name = "messages") val messages: Boolean,
    @Json(name = "others") val others: Boolean
)
