package com.suunto.connectivity.util;

/**
 * Created by <PERSON><PERSON> on 8/31/2016.
 */
public class MathUtils {
    /**
     * x modulo y, where the result is always positive
     *
     * @param x
     * @param y
     * @return
     */
    public static int mod(int x, int y) {
        if (y == 0 && x < 0) return 0; // It is quite debatable what the result should be when y==0...
        if (y == 0) return x;          // It is quite debatable what the result should be when y==0...
        int result = x % y;
        if (result < 0) {
            result += y;
        }
        return result;
    }

    public static long mod(long x, long y) {
        if (y == 0 && x < 0) return 0L; // It is quite debatable what the result should be when y==0...
        if (y == 0) return x;           // It is quite debatable what the result should be when y==0...
        long result = x % y;
        if (result < 0) {
            result += y;
        }
        return result;
    }

    public static long mod(long x, int y) {
        if (y == 0 && x < 0) return 0L; // It is quite debatable what the result should be when y==0...
        if (y == 0) return x;           // It is quite debatable what the result should be when y==0...
        long result = x % y;
        if (result < 0) {
            result += y;
        }
        return result;
    }

}
