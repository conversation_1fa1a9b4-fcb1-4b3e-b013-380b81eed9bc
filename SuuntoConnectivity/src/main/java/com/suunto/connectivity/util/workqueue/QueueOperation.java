package com.suunto.connectivity.util.workqueue;

import android.os.Handler;
import android.os.OperationCanceledException;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import org.jdeferred.Promise;
import org.jdeferred.impl.DeferredObject;

/**
 * Base class for all operations that are executed on the {@link WorkQueue}.
 *
 * Derived classes must override {@link #protectedRun} and call
 * {@link #onCompleted} or {@link #onError} when the operation finishes.
 */
public abstract class QueueOperation<T> implements Runnable {

    public static HandlerProvider handlerProvider = new MainThreadHandlerProvider();

    // Operation not run yet
    public static final int STATE_WAITING = 0;

    // Operation is running, run method is executing
    public static final int STATE_RUNNING_BUSY = 1;

    // Operation is still in progress, but run method has completed
    public static final int STATE_RUNNING = 2;

    // Operation has finished but onStop has not been called yet
    public static final int STATE_STOP_PENDING = 3;

    // Operation has finished and there are no pending actions
    public static final int STATE_FINISHED = 4;

    private static final long DEFAULT_TIMEOUT_MS = 0;

    private final DeferredObject<T, Throwable, Object> publicDeferred;
    private final DeferredObject<T, Throwable, Object> internalDeferred;

    private AtomicReference<Semaphore> queueBlockingSemaphore = new AtomicReference<>();
    private AtomicInteger state = new AtomicInteger(STATE_WAITING);
    private long timeout = DEFAULT_TIMEOUT_MS;
    private Object tag;
    private Handler timeoutHandler;
    private Runnable timeoutRunnable;

    public QueueOperation() {
        // Create deferred objects in constructor to avoid possible threading issues.
        publicDeferred = new DeferredObject<>();
        internalDeferred = new DeferredObject<>();
    }

    /**
     * Runs the operation. Called by the queue.
     */
    @Override
    public final void run() {
        if (!state.compareAndSet(STATE_WAITING, STATE_RUNNING_BUSY)) {
            releaseQueue();
            return;
        }

        if (timeout > 0) {
            timeoutHandler = handlerProvider.createHandler();
            timeoutRunnable = () -> {
                Exception customTimeoutException = customTimeoutException();
                if (customTimeoutException != null) {
                    cancel(customTimeoutException);
                } else {
                    String className = QueueOperation.this.getClass().getSimpleName();
                    cancel(new TimeoutException("Queue operation timed out: " + className));
                }
            };
            timeoutHandler.postDelayed(timeoutRunnable, timeout);
        }

        try {
            protectedRun();
        }
        catch (Throwable throwable) {
            onError(throwable);
        }

        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_RUNNING)) {
            // No action
        }
        else if (state.compareAndSet(STATE_STOP_PENDING, STATE_FINISHED)) {
            onStop();
        }
    }

    /**
     * Gets the current state of the operation.
     *
     * @return STATE_WAITING, STATE_RUNNIN or STATE_FINISHED
     */
    public int getState() {
        return state.get();
    }

    /**
     * Cancels the operation with a OperationCanceledException.
     *
     * @see #cancel(Throwable)
     */
    public void cancel() {
        cancel(new OperationCanceledException("Queue operation cancelled"));
    }

    /**
     * Cancels the operation.
     *
     * If the operation is currently running, the queue is allowed to advance.
     * Derived classes may override this function and do something to actually
     * cancel the execution of the operation.
     * If the operation has not been run yet, it will be marked as cancelled
     * and the queue will skip it.
     *
     * @param throwable Throwable to pass to onError.
     */
    public void cancel(Throwable throwable) {
        if (state.get() != STATE_FINISHED) {
            onError(throwable);
        }
    }

    /**
     * Gets the timeout in milliseconds for the operation.
     *
     * @return Timeout in milliseconds. Zero means no timeout.
     */
    public long getTimeout() {
        return timeout;
    }

    /**
     * Sets a timeout in milliseconds for the operation.
     *
     * The queue will cancel the operation if it does not finish before the
     * timeout value after the execution of the operation has started.
     *
     * @param timeout Timeout in milliseconds. Zero means no timeout.
     */
    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    /**
     * Sets the semaphore that will block the queue while the operation is running.
     *
     * Called by the queue before it starts running the operation. This base
     * class will release the semaphore when the task is finished (completed
     * successfully or with an error) or cancelled.
     *
     * @param queueBlockingSemaphore
     */
    public void setQueueBlockingSemaphore(@NonNull Semaphore queueBlockingSemaphore) {
        this.queueBlockingSemaphore.set(queueBlockingSemaphore);
    }

    /**
     * Gets a promise from the queue operation. Call this only before adding
     * the operation to a queue.
     *
     * The promise will be resolved when the operation finishes with onComplete
     * and rejected when the operation fails with onError or is cancelled.
     *
     * @return Promise
     */
    public Promise<T, Throwable, Object> toPromise() {
        return publicDeferred.promise();
    }

    /**
     * Sets the tag associated with this operation. A tag can be used to store
     * arbitary data to the operation.
     *
     * @param tag An object to tag the operation with
     */
    public void setTag(Object tag) {
        this.tag = tag;
    }

    /**
     * Gets the tag associated with this operation
     *
     * @return The tag. Null if not set.
     */
    public Object getTag() {
        return tag;
    }

    /**
     * Does the actual work of the operation.
     *
     * Derived classes must call {@link #onCompleted} or {@link #onError} when
     * the operation finishes.
     * Exceptions thrown from this function are catched and passed to the
     * {@link #onError} function.
     *
     * @throws Throwable
     */
    protected void protectedRun() throws Throwable {

    }

    /**
     * Signals that the operation has completed successfully.
     *
     * Derived classes must call this function when the operation completes.
     *
     * @param value Value to use in resolving the promise.
     */
    protected void onCompleted(@Nullable T value) {
        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_STOP_PENDING)) {
            // Store result for later use. The promise will be resolved using
            // this value when onStop is called. We don't want to resolve the
            // promise here, because we are still inside the try-catch
            // block of the run function, and any exceptions thrown in promise
            // callbacks would be lost.
            internalDeferred.resolve(value);
        }
        else if (state.compareAndSet(STATE_RUNNING, STATE_FINISHED)) {
            internalDeferred.resolve(value);
            onStop();
        }
        else if (state.compareAndSet(STATE_WAITING, STATE_FINISHED)) {
            // Resolve promise immediately because the operation has not been
            // run and onStop will not be called
            publicDeferred.resolve(value);
        }
    }

    /**
     * Convenience function for completing the operation without a value.
     */
    protected void onCompleted() {
        onCompleted(null);
    }

    /**
     * Signals that the operation has failed.
     *
     * Derived classes must call this function when the operation fails.
     *
     * @param throwable Throwable related to the error, if any.
     */
    protected void onError(@Nullable Throwable throwable) {
        if (state.compareAndSet(STATE_RUNNING_BUSY, STATE_STOP_PENDING)) {
            internalDeferred.reject(throwable);
        }
        else if (state.compareAndSet(STATE_RUNNING, STATE_FINISHED)) {
            internalDeferred.reject(throwable);
            onStop();
        }
        else if (state.compareAndSet(STATE_WAITING, STATE_FINISHED)) {
            publicDeferred.reject(throwable);
        }
    }

    /**
     * Function called when the operation stops running.
     *
     * Releases the queue and allows the next operation to run. Derived classes
     * can override this function and add more cleanup, like unregister
     * listeners that are registered in {@link #protectedRun}.
     */
    protected void onStop() {
        releaseQueue();

        if (timeoutHandler != null && timeoutRunnable != null) {
            timeoutHandler.removeCallbacks(timeoutRunnable);
        }

        // Resolve public promise with the value stored to the internal deferred
        internalDeferred
                .done(publicDeferred::resolve)
                .fail(publicDeferred::reject);
    }

    protected void releaseQueue() {
        Semaphore semaphore = queueBlockingSemaphore.getAndSet(null);
        if (semaphore != null) {
            semaphore.release();
        }
    }

    @Nullable
    protected Exception customTimeoutException() {
        return null;
    }
}
