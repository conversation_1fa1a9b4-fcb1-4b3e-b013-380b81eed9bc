package com.suunto.connectivity.settings

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.utils.traceCompletableV1
import com.suunto.connectivity.SettingsPreferences
import com.suunto.connectivity.SpartanUserSettings
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.repository.SyncResult
import com.suunto.connectivity.repository.entities.AskoUserSettings
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.sync.WatchSynchronizer.GenericSyncError
import com.suunto.connectivity.watch.SpartanBt
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.SyncStepResult
import com.suunto.connectivity.watch.UserSettingsResource
import rx.Completable
import rx.Single
import rx.schedulers.Schedulers
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject

class SettingsResource @Inject constructor(
    private val synchronizerStorage: SynchronizerStorage,
    private val userSettingsResource: UserSettingsResource,
    @SettingsPreferences private val settingsPreferences: SharedPreferences,
) {

    private fun isSupportUserProfileSetting(spartanBt: SpartanBt): Boolean {
        val capabilities = SuuntoDeviceCapabilityInfoProvider[spartanBt.suuntoBtDevice.deviceType]
        return capabilities.supportsUserProfileSetting(spartanBt.currentState.deviceInfo)
    }

    fun sync(
        spartanBt: SpartanBt,
        builder: SpartanSyncResult.Builder
    ): Completable {
        val supportsUserProfileSetting = isSupportUserProfileSetting(spartanBt)
        Timber.d("SettingsResource sync, isSupportUserProfileSetting:$supportsUserProfileSetting")
        val macAddress: String = spartanBt.suuntoBtDevice.getMacAddress()
        val syncStartTimestamp = AtomicLong()
        return getUnifiedSettings(spartanBt, supportsUserProfileSetting)
            .doOnSubscribe { syncStartTimestamp.set(System.currentTimeMillis()) }
            .doOnSuccess { userSettings ->
                val oldSettings = synchronizerStorage.readUserSettingsFromWatch(macAddress)
                if (userSettings == oldSettings) {
                    builder.settingsResult(SyncStepResult(SyncResult.alreadySynced()))
                } else {
                    if (synchronizerStorage.storeUserSettingsFromWatch(userSettings, macAddress)) {
                        builder.settingsResult(SyncStepResult(SyncResult.success()))
                    } else {
                        builder.settingsResult(
                            SyncStepResult(SyncResult.failed(GenericSyncError("Storing user settings failed")))
                        )
                    }
                }
            }
            .toCompletable()
            .onErrorComplete { e ->
                Timber.w(e, "Settings sync failed")
                builder.settingsResult(SyncStepResult(SyncResult.failed(e)))
                true
            }
            .concatWith(spartanBt.syncTimeZoneInfo())
            .concatWith(spartanBt.syncAnalyticsUUID())
            .concatWith(spartanBt.syncAskoUserSettings())
            .doOnTerminate {
                // Calculate syncDuration after also the .concatWith operations have completed
                val syncDuration = System.currentTimeMillis() - syncStartTimestamp.get()
                builder.settingsResultSyncDuration(syncDuration)
            }
            .subscribeOn(Schedulers.io())
            .traceCompletableV1("SyncSettings")
    }

    private fun getUnifiedSettings(
        spartanBt: SpartanBt,
        isSupportUserProfileSetting: Boolean
    ): Single<SpartanUserSettings> {
        return userSettingsResource.getUserSettingsBuilder(spartanBt, isSupportUserProfileSetting)
            .map { builder: SpartanUserSettings.Builder -> builder.build() }
    }

    private fun SpartanBt.syncTimeZoneInfo(): Completable = updateTimeZoneInfo()
        .onErrorComplete { e ->
            Timber.w(e, "Error updating TimeZoneInfo")
            true
        }

    private fun SpartanBt.syncAnalyticsUUID(): Completable =
        if (suuntoBtDevice.getDeviceType().isDataLayerDevice && settings != null) {
            requireNotNull(settings).updateAnalyticsUUID()
                .onErrorComplete { e ->
                    Timber.w(e, "Error syncing analytics UUID")
                    true
                }
        } else {
            Completable.complete()
        }

    private fun SpartanBt.syncAskoUserSettings(): Completable {
        return Single.fromCallable(synchronizerStorage::readUserSettingsToWatch)
            .flatMapCompletable { askoUserSettings ->
                askoUserSettings?.let {
                    Completable.merge(syncPredefinedReplies(askoUserSettings), syncFirstDayOfWeek(askoUserSettings))
                } ?: Completable.complete()
            }
            .onErrorComplete { e: Throwable? ->
                Timber.w(e, "Error during syncAskoUserSettings()")
                true
            }
            .subscribeOn(Schedulers.io())
    }

    private fun SpartanBt.syncPredefinedReplies(askoUserSettings: AskoUserSettings): Completable {
        val predefinedRepliesSet = askoUserSettings.predefinedReplies.toSet()
        if (predefinedRepliesSet == settingsPreferences.getStringSet(
                getPrefsKey(KEY_PREFIX_PREDEFINED_REPLIES),
                emptySet()
            )
        ) {
            Timber.v("Predefined replies not changed, skip syncing.")
            return Completable.complete()
        }

        Timber.v("Syncing predefined replies")
        return setPredefinedReplies(askoUserSettings.predefinedReplies)
            .doOnCompleted {
                settingsPreferences.edit {
                    putStringSet(getPrefsKey(KEY_PREFIX_PREDEFINED_REPLIES), predefinedRepliesSet)
                }
            }
    }

    private fun SpartanBt.syncFirstDayOfWeek(askoUserSettings: AskoUserSettings): Completable {
        val settings = settings ?: return Completable.complete()
        val firstDayOfWeek = askoUserSettings.firstDayOfTheWeek ?: return Completable.complete()
        if (firstDayOfWeek.value == settingsPreferences.getInt(getPrefsKey(KEY_PREFIX_FIRST_DAY_OF_WEEK), -1)) {
            Timber.v("First day of week not changed, skip syncing.")
            return Completable.complete()
        }

        Timber.v("Syncing first day of week")
        return settings.updateFirstDayOfTheWeek(firstDayOfWeek)
            .doOnCompleted {
                settingsPreferences.edit {
                    putInt(getPrefsKey(KEY_PREFIX_FIRST_DAY_OF_WEEK), firstDayOfWeek.value)
                }
            }
    }

    fun clearPrefs(serial: String) {
        settingsPreferences.edit {
            remove(getPrefsKey(KEY_PREFIX_PREDEFINED_REPLIES, serial))
            remove(getPrefsKey(KEY_PREFIX_FIRST_DAY_OF_WEEK, serial))
        }
    }

    private fun SpartanBt.getPrefsKey(prefix: String): String = getPrefsKey(prefix, serial)

    private fun getPrefsKey(prefix: String, serial: String): String = "$prefix$serial"

    private companion object {
        const val KEY_PREFIX_PREDEFINED_REPLIES = "KEY_PREDEFINED_REPLIES_PREFIX_"
        const val KEY_PREFIX_FIRST_DAY_OF_WEEK = "KEY_FIRST_DAY_OF_WEEK_PREFIX_"
    }
}
