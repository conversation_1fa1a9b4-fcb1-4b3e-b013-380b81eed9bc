package com.suunto.connectivity.settings

import com.stt.android.domain.user.UserProfile
import com.suunto.connectivity.SpartanUserSettings

fun SpartanUserSettings.convertUserProfile(): UserProfile {
    return UserProfile(
        sex = this.gender.name,
        birthYear = this.birthYear,
        heightInMeter = this.heightInMeter,
        weightInKilograms = this.weightInKilograms,
        maxHR = this.maxHR,
        restHR = this.restHR
    )
}
