package com.suunto.connectivity.battery

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_BATTERY_LEVEL
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.GetBatteryLevelQuery
import com.suunto.connectivity.repository.commands.GetBatteryLevelResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.util.handleDeviceSpecificQueryObservable
import com.suunto.connectivity.watch.WatchBt
import rx.Observable

class BatteryProvider(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean =
        messageType == MSG_GET_BATTERY_LEVEL

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> =
        when (messageType) {
            MSG_GET_BATTERY_LEVEL ->
                handleQuery<GetBatteryLevelQuery>(bundle) { watchBt, _ ->
                    watchBt.batteryLevel
                        .map { batteryLevel ->
                            GetBatteryLevelResponse(batteryLevel)
                        }
                }
            else -> Observable.just(ErrorResponse("Unknown query"))
        }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Observable<Response>
    ): Observable<Response> =
        handleDeviceSpecificQueryObservable(suuntoRepositoryService.activeDevices, bundle, handler)
}
