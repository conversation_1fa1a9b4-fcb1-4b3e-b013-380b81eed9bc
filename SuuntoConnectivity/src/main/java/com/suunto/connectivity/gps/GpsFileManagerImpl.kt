package com.suunto.connectivity.gps

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.suunto.connectivity.gps.entities.FilePathWithIndex
import com.suunto.connectivity.gps.entities.GpsFormatType
import com.suunto.connectivity.gps.entities.NavigationSystem
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.repository.RepositoryConfiguration
import rx.Completable
import rx.CompletableEmitter
import rx.schedulers.Schedulers
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.File
import java.io.FileOutputStream
import java.net.URL
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.net.ssl.HttpsURLConnection

/**
 * Default implementation for the GpsFileManager
 *
 * Method for actually downloading the GPS files will take timestamp of the binary
 * file and emit that. This value can later be used for more intelligent updating
 * of the GPS files.
 */
class GpsFileManagerImpl(
    private val context: Context,
    private val configuration: RepositoryConfiguration,
    private val preferences: SharedPreferences,
    deviceApiBaseUrl: String
) : GpsFileManager {

    private val gpsOrbitUrl = "$deviceApiBaseUrl$GPS_ORBIT_API"
    private val glnsOrbitUrl = "$deviceApiBaseUrl$GLNS_ORBIT_API"
    private val epo1Url = "$deviceApiBaseUrl$EPO_1_API"
    private val epo2Url = "$deviceApiBaseUrl$EPO_2_API"
    private val cepUrl = "$deviceApiBaseUrl$CEP_API"
    private val lleGpsUrl = "$deviceApiBaseUrl$LLE_GPS_API"
    private val lleGlonasUrll = "$deviceApiBaseUrl$LLE_GLONASS_API"
    private val lleQzssUrl = "$deviceApiBaseUrl$LLE_QZSS_API"
    private val lleBeidouUrl = "$deviceApiBaseUrl$LLE_BEIDOU_API"
    private val lleGalileoUrl = "$deviceApiBaseUrl$LLE_GALILEO_API"
    private val lto7dUrl = "$deviceApiBaseUrl$LTO7D_API"
    private val lto2dUrl = "$deviceApiBaseUrl$LTO2D_API"

    // GPS and Glonass files are valid only 24h from last modified timestamp
    // Then 1 hour removed so that there is at least 1 hour of time left
    private val gpsOrbitBinary: File?
        get() = getGNNSFile(GPS_ORBIT_PREF, configuration.gpsBinaryPath)

    private val glonassOrbitBinary: File?
        get() = getGNNSFile(GLNS_ORBIT_PREF, configuration.glnsBinaryPath)

    private val epo1Binary: File?
        get() = getGNNSFile(EPO_1_PREF, configuration.epo1BinaryPath)

    private val epo2Binary: File?
        get() = getGNNSFile(EPO_2_PREF, configuration.epo2BinaryPath)

    // CEP files are valid only 24h from last modified timestamp
    private val cepBinary: File?
        get() = getGNNSFile(CEP_PREF, configuration.cepBinaryPath)

    // LLE_GPS is valid through 7 days
    private val lLEGpsBinary: File?
        get() = getGNNSFile(LLE_GPS_PREF, configuration.lleGpsBinaryPath)

    // LLE_GLONASS is valid through 7 days
    private val lLEGlonassBinary: File?
        get() = getGNNSFile(LLE_GLONASS_PREF, configuration.lleGlonassBinaryPath)

    // LLE_GPS is valid through 7 days
    private val lLEQzssBinary: File?
        get() = getGNNSFile(LLE_QZSS_PREF, configuration.lleQzssBinaryPath)

    // LLE_BEIDOU is valid through 7 days
    private val lLEBeidouBinary: File?
        get() = getGNNSFile(LLE_BEIDOU_PREF, configuration.lleBeidouBinaryPath)

    // LLE_GALILEO is valid through 7 days
    private val lLEGalileoBinary: File?
        get() = getGNNSFile(LLE_GALILEO_PREF, configuration.lleGalileoBinaryPath)

    private val lto7dBinary: File?
        get() = getGNNSFile(LTO7D_PREF, configuration.lto7dBinaryPath)

    private val lto2dBinary: File?
        get() = getGNNSFile(LTO2D_PREF, configuration.lto2dBinaryPath)

    override fun updateGpsFilesForSpartan(gpsType: GpsFormatType): Completable {
        val updateGpsCompletable: Completable = when (gpsType) {
            GpsFormatType.SGEE -> updateGnssFiles()
            GpsFormatType.EPO -> updateEpoFiles()
            GpsFormatType.CEP -> updateCepFiles()
            GpsFormatType.LLE -> updateLLEFiles()
            GpsFormatType.LTO7D -> updateLTO7Files()
            GpsFormatType.LTO2D -> updateLTO2Files()
            GpsFormatType.UNKNOWN -> Completable.error(IllegalArgumentException("Unknown gps format"))
        }
        return updateGpsCompletable
            .doOnError { e ->
                Timber.w(e, "Failed to update GPS files for type %s", gpsType)
            }
    }

    override fun getGpsFilePaths(
        navigationSystems: List<NavigationSystem>,
        gpsType: GpsFormatType
    ): List<FilePathWithIndex>? {
        val debugDataBuilder = StringBuilder()
            .apply {
                append("GPS type: ")
                append(gpsType)
                append(", navigation systems: ")
                append(navigationSystems)
                append(' ')
            }
        val filePaths = when (gpsType) {
            GpsFormatType.SGEE -> {
                val sgeeFiles = getSGEEFiles()
                if (sgeeFiles == null) {
                    debugDataBuilder.append("gpsOrbitBinary:$gpsOrbitBinary glonassOrbitBinary:$glonassOrbitBinary ")
                }
                sgeeFiles
            }

            GpsFormatType.EPO -> {
                val epo1Binary = epo1Binary
                val epo2Binary = epo2Binary
                if (epo1Binary != null && epo2Binary != null) {
                    listOf(
                        FilePathWithIndex(epo1Binary.absolutePath, 0),
                        FilePathWithIndex(epo2Binary.absolutePath, 1)
                    )
                } else {
                    debugDataBuilder.append("epo1Binary:$epo1Binary epo2Binary:$epo2Binary ")
                    null
                }
            }

            GpsFormatType.CEP -> {
                val cepBinary = cepBinary
                if (cepBinary != null) {
                    listOf(FilePathWithIndex(cepBinary.absolutePath, 0))
                } else {
                    debugDataBuilder.append("cepBinary:$cepBinary ")
                    null
                }
            }

            GpsFormatType.LLE -> {
                val lleFiles = getLLEFiles(navigationSystems)
                if (lleFiles == null) {
                    debugDataBuilder.append("lLEGpsBinary:$lLEGpsBinary lLEQzssBinary:$lLEQzssBinary")
                }
                lleFiles
            }

            GpsFormatType.LTO7D -> {
                val lto7dBinary = lto7dBinary
                if (lto7dBinary != null) {
                    listOf(FilePathWithIndex(lto7dBinary.absolutePath, 0))
                } else {
                    debugDataBuilder.append("lto7dBinary:$lto7dBinary ")
                    null
                }
            }

            GpsFormatType.LTO2D -> {
                val lto2dBinary = lto2dBinary
                if (lto2dBinary != null) {
                    listOf(FilePathWithIndex(lto2dBinary.absolutePath, 0))
                } else {
                    debugDataBuilder.append("lto2dBinary:$lto2dBinary ")
                    null
                }
            }

            GpsFormatType.UNKNOWN -> null
        }
        if (filePaths == null) {
            // todo Remove this amplitude event when gps sync investigation is done.
            val debugData = debugDataBuilder.toString()
            AnalyticsUtils.sendGpsSyncNoValidFilesEvent(
                AnalyticsProperties().apply {
                    put("DebugData", debugData)
                    put("NavigationSystems", navigationSystems.joinToString { it.name })
                }
            )
            Timber.w(IllegalStateException("Failed to get GPS file paths"), debugData)
        }
        return filePaths
    }

    private fun getGNNSFile(prefKey: String, binaryPath: String): File? {
        // If there is no last modified in preferences, then there shouldn't be file either
        if (!preferences.contains(prefKey)) {
            return null
        }
        return File(context.filesDir, binaryPath)
    }

    private fun getSGEEFiles(): List<FilePathWithIndex>? {
        val files: MutableList<FilePathWithIndex> = ArrayList()
        gpsOrbitBinary?.also { files.add(FilePathWithIndex(it.absolutePath, 0)) }
        glonassOrbitBinary?.also { files.add(FilePathWithIndex(it.absolutePath, 1)) }
        return if (files.isEmpty()) {
            null
        } else {
            files
        }
    }

    private fun getLLEFiles(navigationSystems: List<NavigationSystem>): List<FilePathWithIndex>? {
        val files: MutableList<FilePathWithIndex> = ArrayList()
        // qzss file is always synced together with gps
        lLEGpsBinary?.also { files.add(FilePathWithIndex(it.absolutePath, 0)) }
        lLEQzssBinary?.also { files.add(FilePathWithIndex(it.absolutePath, 1)) }
        if (navigationSystems.contains(NavigationSystem.GLONASS) && lLEGlonassBinary != null) {
            files.add(FilePathWithIndex(lLEGlonassBinary!!.absolutePath, 2))
        }
        if (navigationSystems.contains(NavigationSystem.GALILEO) && lLEGalileoBinary != null) {
            files.add(FilePathWithIndex(lLEGalileoBinary!!.absolutePath, 3))
        }
        if (navigationSystems.contains(NavigationSystem.BEIDOU) && lLEBeidouBinary != null) {
            files.add(FilePathWithIndex(lLEBeidouBinary!!.absolutePath, 4))
        }
        return if (files.isEmpty()) {
            null
        } else {
            files
        }
    }

    /**
     * Updates GPS and GLNS binaries and returns their timestamp
     *
     * @return Timestamp provided in header when file is downloaded
     */
    private fun updateGnssFiles(): Completable {
        return ensureDirectoryExists()
            .andThen(
                Completable.merge(
                    downloadGpsFile(
                        configuration.gpsBinaryPath,
                        gpsOrbitUrl,
                        GPS_ORBIT_PREF
                    ),
                    downloadGpsFile(
                        configuration.glnsBinaryPath,
                        glnsOrbitUrl,
                        GLNS_ORBIT_PREF
                    )
                )
            )
    }

    /**
     * Updates CEP binary and returns their timestamp
     */
    private fun updateCepFiles(): Completable {
        return ensureDirectoryExists()
            .andThen(downloadGpsFile(configuration.cepBinaryPath, cepUrl, CEP_PREF))
    }

    /**
     * Updates EPO 1 and EPO 2 binaries and return their timestamp
     *
     * @return Timestamp provided in header when file is downloaded
     */
    private fun updateEpoFiles(): Completable {
        return ensureDirectoryExists()
            .andThen(
                Completable.merge(
                    downloadGpsFile(configuration.epo1BinaryPath, epo1Url, EPO_1_PREF),
                    downloadGpsFile(configuration.epo2BinaryPath, epo2Url, EPO_2_PREF)
                )
            )
    }

    private fun updateLLEFiles(): Completable {
        val lleGpsFileDownload = downloadGpsFile(
            configuration.lleGpsBinaryPath,
            lleGpsUrl,
            LLE_GPS_PREF
        )
        val lleGlonassFileDownload = downloadGpsFile(
            configuration.lleGlonassBinaryPath,
            lleGlonasUrll,
            LLE_GLONASS_PREF
        )
        val lleQzssFileDownload = downloadGpsFile(
            configuration.lleQzssBinaryPath,
            lleQzssUrl,
            LLE_QZSS_PREF
        )
        val lleBeidouFileDownload = downloadGpsFile(
            configuration.lleBeidouBinaryPath,
            lleBeidouUrl,
            LLE_BEIDOU_PREF
        )
        val lleGalileoFileDownload = downloadGpsFile(
            configuration.lleGalileoBinaryPath,
            lleGalileoUrl,
            LLE_GALILEO_PREF
        )
        return ensureDirectoryExists()
            .andThen(
                Completable.merge(
                    lleGpsFileDownload,
                    lleGlonassFileDownload,
                    lleQzssFileDownload,
                    lleBeidouFileDownload,
                    lleGalileoFileDownload
                )
            )
    }

    private fun updateLTO7Files(): Completable {
        return ensureDirectoryExists()
            .andThen(downloadGpsFile(configuration.lto7dBinaryPath, lto7dUrl, LTO7D_PREF))
    }

    private fun updateLTO2Files(): Completable {
        return ensureDirectoryExists()
            .andThen(downloadGpsFile(configuration.lto2dBinaryPath, lto2dUrl, LTO2D_PREF))
    }

    /**
     * Makes sure that the path to GPS folder exists
     *
     * @return Completable which completes only if folder for GPS files exists or was created
     */
    private fun ensureDirectoryExists(): Completable {
        return Completable.fromEmitter { emitter: CompletableEmitter ->
            val f = File(context.filesDir, configuration.gpsFolder)
            if (!f.exists() && !f.mkdirs()) {
                emitter.onError(RuntimeException("Could not create necessary folders"))
            } else {
                emitter.onCompleted()
            }
        }
    }

    private fun downloadGpsFile(
        outputFile: String,
        urlStr: String,
        lastModifiedPrefKey: String
    ): Completable {
        return Completable.fromCallable {
            Timber.d("Starting download from $urlStr to $outputFile")
            val url = URL(urlStr)
            val connection = url.openConnection() as HttpsURLConnection

            try {
                val lastModified = preferences.getString(lastModifiedPrefKey, null)
                if (lastModified != null) {
                    // Add this header field so backend knows to not resend same binaries
                    connection.setRequestProperty(HEADER_IF_MODIFIED_SINCE, lastModified)
                }

                connection.connect()

                // Check that the response was a proper one
                val responseCode = connection.responseCode
                when {
                    // There is no newer file available for as at this time
                    responseCode == 304 -> Timber.d("File not modified: $outputFile")

                    responseCode != 200 -> reportAndThrow(
                        responseCode = responseCode,
                        urlStr = urlStr
                    )

                    else -> {
                        downloadFile(url = url, outputFile = outputFile)
                        val lastModifiedFromHeaders =
                            connection.getHeaderField(HEADER_LAST_MODIFIED)
                        Timber.d("Assist file's Last-Modified header:$lastModifiedFromHeaders")
                        updateLastModifiedToPrefs(
                            lastModifiedFromHeaders = lastModifiedFromHeaders,
                            lastModifiedPrefKey = lastModifiedPrefKey
                        )
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "Error while trying to download GPS file '%s'", url)
                throw e
            } finally {
                connection.disconnect()
            }
        }.subscribeOn(Schedulers.io())
    }

    private fun downloadFile(url: URL, outputFile: String) {
        // download the file and write it to file
        BufferedInputStream(url.openStream(), 8192).use { input ->
            val destination = File(context.filesDir, outputFile)
            FileOutputStream(destination).use { output ->
                var count: Int
                val data = ByteArray(1024)
                while (input.read(data).also { count = it } != -1) {
                    // writing data to file
                    output.write(data, 0, count)
                }

                // flushing output
                output.flush()
            }

            Timber.d(
                "Download complete! File: %s, size: %d",
                destination.absolutePath,
                destination.length()
            )
        }
    }

    private fun updateLastModifiedToPrefs(
        lastModifiedFromHeaders: String?,
        lastModifiedPrefKey: String
    ) {
        if (lastModifiedFromHeaders != null) {
            preferences.edit {
                putString(lastModifiedPrefKey, lastModifiedFromHeaders)
            }
        }
    }

    private fun reportAndThrow(responseCode: Int, urlStr: String) {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val timeISO = df.format(Date())
        val syncType = if (urlStr == gpsOrbitUrl) {
            AnalyticsPropertyValue.SYNC_TYPE_GPS
        } else {
            AnalyticsPropertyValue.SYNC_TYPE_GLONASS
        }
        AnalyticsUtils.sendSatelliteDataDownloadFailed(responseCode, timeISO, syncType)
        throw RuntimeException("Failed to download '$urlStr' with error $responseCode")
    }

    companion object {
        private const val APPKEY =
            "appkey=DbCBVqja20NKdrimBHQxtYIdczUJ56WHIWlC6A7vp6NPC0D0a8wA5d0ODyywFKe6"
        private const val HEADER_LAST_MODIFIED = "Last-Modified"
        private const val HEADER_IF_MODIFIED_SINCE = "If-Modified-Since"
        private const val GPS_ORBIT_API = "devices/gpsorbit/binary?$APPKEY"
        private const val GPS_ORBIT_PREF = "gps_lastModified"
        private const val GLNS_ORBIT_API = "devices/glonassorbit/binary?$APPKEY"
        private const val GLNS_ORBIT_PREF = "glns_lastModified"
        private const val EPO_1_API = "devices/gpsorbit/mtk3day?index=1&$APPKEY"
        private const val EPO_1_PREF = "epo1_lastModified"
        private const val EPO_2_API = "devices/gpsorbit/mtk3day?index=2&$APPKEY"
        private const val EPO_2_PREF = "epo2_lastModified"
        private const val CEP_API = "devices/gpsorbit/sony?$APPKEY"
        private const val CEP_PREF = "cep_lastModified"
        private const val LLE_GPS_API = "devices/gpsorbit/sonylle?$APPKEY"
        private const val LLE_GPS_PREF = "lle_gps_lastModified"
        private const val LLE_GLONASS_API = "devices/glonassorbit/sonylle?$APPKEY"
        private const val LLE_GLONASS_PREF = "lle_glonass_lastModified"
        private const val LLE_QZSS_API = "devices/qzssorbit/sonylle?$APPKEY"
        private const val LLE_QZSS_PREF = "lle_qzss_lastModified"
        private const val LLE_BEIDOU_API = "devices/beidouorbit/sonylle?$APPKEY"
        private const val LLE_BEIDOU_PREF = "lle_beidou_lastModified"
        private const val LLE_GALILEO_API = "devices/galileoorbit/sonylle?$APPKEY"
        private const val LLE_GALILEO_PREF = "lle_galileo_lastModified"
        private const val LTO7D_API = "firmware/bes/orbit/bes7day?$APPKEY"
        private const val LTO7D_PREF = "lto7d_lastModified"
        private const val LTO2D_API = "firmware/bes/orbit/bes2day?$APPKEY"
        private const val LTO2D_PREF = "lto2d_lastModified"
    }
}
