package com.suunto.connectivity.watch

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.suunto.connectivity.repository.SyncResult
import kotlinx.parcelize.Parcelize

@Parcelize
data class TrainingZoneSyncResult constructor(
    @SerializedName("trainingSyncResult") val trainingSyncResult: SyncResult,
    @SerializedName("progressSyncResult") val progressSyncResult: SyncResult,
    @SerializedName("recoverySyncResult") val recoverySyncResult: SyncResult,
    @SerializedName("thresholdsSyncResult") val thresholdsSyncResult: SyncResult,
    @SerializedName("exercisesSyncResult") val exercisesSyncResult: SyncResult,
    @SerializedName("syncDuration") val syncDuration: Long? = null,
) : Parcelable {

    fun getOverallSyncResult(): SyncResult {
        val results =
            listOf(trainingSyncResult, progressSyncResult, recoverySyncResult, thresholdsSyncResult, exercisesSyncResult)

        return when {
            results.all { it.isAlreadySynced() } -> SyncResult.alreadySynced()
            results.all { it.isSkipped() } -> SyncResult.skipped()
            results.all { it.isUnknown() } -> SyncResult.unknown()
            results.any { it.isFailed() } -> SyncResult.failed("One or more failed")
            else -> SyncResult.success()
        }
    }

    class Builder internal constructor(
        private var trainingSyncResult: SyncResult = SyncResult.unknown(),
        private var progressSyncResult: SyncResult = SyncResult.unknown(),
        private var recoverySyncResult: SyncResult = SyncResult.unknown(),
        private var thresholdsSyncResult: SyncResult = SyncResult.unknown(),
        private var exercisesSyncResult: SyncResult = SyncResult.unknown(),
        private var syncDuration: Long? = null,
    ) {
        fun trainingSyncResult(result: SyncResult): Builder =
            apply { this.trainingSyncResult = result }

        fun progressSyncResult(result: SyncResult): Builder =
            apply { this.progressSyncResult = result }

        fun recoverySyncResult(result: SyncResult): Builder =
            apply { this.recoverySyncResult = result }

        fun thresholdsSyncResult(result: SyncResult): Builder =
            apply { this.thresholdsSyncResult = result }

        fun exercisesSyncResult(result: SyncResult): Builder =
            apply { this.exercisesSyncResult = result }

        fun syncDuration(syncDuration: Long?): Builder =
            apply { this.syncDuration = syncDuration }

        fun build(): TrainingZoneSyncResult =
            TrainingZoneSyncResult(
                trainingSyncResult = trainingSyncResult,
                progressSyncResult = progressSyncResult,
                recoverySyncResult = recoverySyncResult,
                thresholdsSyncResult = thresholdsSyncResult,
                exercisesSyncResult = exercisesSyncResult,
                syncDuration = syncDuration,
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()

        @JvmStatic
        fun unknown(): TrainingZoneSyncResult = Builder().build()

        @JvmStatic
        fun skipped(): TrainingZoneSyncResult = Builder()
            .trainingSyncResult(SyncResult.skipped())
            .progressSyncResult(SyncResult.skipped())
            .recoverySyncResult(SyncResult.skipped())
            .thresholdsSyncResult(SyncResult.skipped())
            .exercisesSyncResult(SyncResult.skipped())
            .build()

        @JvmStatic
        fun failed(throwable: Throwable, syncDuration: Long? = null): TrainingZoneSyncResult = Builder()
            .trainingSyncResult(SyncResult.failed(throwable))
            .progressSyncResult(SyncResult.failed(throwable))
            .recoverySyncResult(SyncResult.failed(throwable))
            .thresholdsSyncResult(SyncResult.failed(throwable))
            .exercisesSyncResult(SyncResult.failed(throwable))
            .syncDuration(syncDuration)
            .build()
    }
}
