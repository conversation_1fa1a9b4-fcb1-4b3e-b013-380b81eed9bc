package com.suunto.connectivity.watch;

import android.bluetooth.BluetoothDevice;
import androidx.annotation.NonNull;
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo;
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider;
import com.suunto.connectivity.ngBleManager.NgBleManager;
import com.suunto.connectivity.repository.PairingState;
import com.suunto.connectivity.sdsmanager.MdsRx;
import com.suunto.connectivity.sdsmanager.SdsBleAddressMap;
import com.suunto.connectivity.sdsmanager.model.MdsConnectedDevice;
import com.suunto.connectivity.sdsmanager.model.MdsConnectingDevice;
import com.suunto.connectivity.suuntoconnectivity.BleServiceDeviceInterface;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONNECTING_DEVICES_FAILED_TO_CONNECT;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothDiscoverException;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothWaitBondingException;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattConnectException;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleOperation;
import static com.suunto.connectivity.suuntoconnectivity.ble.operation.BluetoothOperationWaitBonding.GATT_CLIENT_CONNECTION_LOST;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason;
import com.suunto.connectivity.util.workqueue.WorkQueue;
import hu.akarnokd.rxjava.interop.RxJavaInterop;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;
import rx.exceptions.Exceptions;
import rx.functions.Action1;
import rx.subjects.PublishSubject;
import timber.log.Timber;

/**
 * Connects a watch.
 * <p>
 * This class supports connecting both legacy and whiteboard watches. Watch type
 * is determined from the bluetooth name.
 */
public class WatchConnector {

    private final SdsBleAddressMap sdsBleAddressMap;
    private final NgBleManager ngBleManager;
    private final MdsRx mdsRx;

    private final WatchStateHoldersMap watchStateHolders;
    private final WorkQueue connectQueue;

    private final BleServiceDeviceInterface bleServiceDeviceInterface;
    private final BluetoothAdapterWrapper bluetoothAdapter;

    private final RxSchedulerProvider schedulerProvider;

    private static final int DELAY_AFTER_FORCED_BLE_DISCONNECT_IN_SECONDS = 8;
    private static final int SHORT_DELAY_AFTER_FORCED_BLE_DISCONNECT_IN_SECONDS = 4;
    public static final int MDS_CONNECTION_TIMEOUT_SECONDS = 45;
    public static final int MDS_SHORT_CONNECTION_TIMEOUT_SECONDS = 15;
    private final BtStateMonitor btStateMonitor;

    private final PublishSubject<String> mdsConnectedPublishSubject;

    public static class MdsFailedToConnectError extends RuntimeException {

        MdsFailedToConnectError(String message) {
            super(message);
        }

        MdsFailedToConnectError() {
        }
    }

    public static class MdsConnectTimeoutError extends MdsFailedToConnectError {
        MdsConnectTimeoutError(String message) {
            super(message);
        }

        MdsConnectTimeoutError() {
        }
    }

    public WatchConnector(
        SdsBleAddressMap sdsBleAddressMap,
        NgBleManager ngBleManager,
        MdsRx mdsRx,
        BleServiceDeviceInterface bleServiceDeviceInterface,
        BluetoothAdapterWrapper bluetoothAdapter,
        WorkQueue connectQueue,
        BtStateMonitor btStateMonitor,
        RxSchedulerProvider schedulerProvider
    ) {
        this.sdsBleAddressMap = sdsBleAddressMap;
        this.ngBleManager = ngBleManager;
        this.mdsRx = mdsRx;
        this.bleServiceDeviceInterface = bleServiceDeviceInterface;
        this.bluetoothAdapter = bluetoothAdapter;
        this.connectQueue = connectQueue;
        this.btStateMonitor = btStateMonitor;
        this.schedulerProvider = schedulerProvider;

        watchStateHolders = new WatchStateHoldersMap();
        mdsConnectedPublishSubject = PublishSubject.create();

        // Subscribe to device connection events
        mdsRx.deviceConnectionObservable()
            .subscribe(device -> {
                if (device.isConnected()) {
                    onDeviceConnected(device);
                } else {
                    onDeviceDisconnected(device);
                }
            }, throwable -> Timber.e(throwable, "Error while receiving device connection events"));
    }

    /**
     * Is BleManager already connected? This is used only for analytics purposes.
     *
     * @return True, if already connected.
     */
    public boolean isAlreadyConnected() {
        // Is connection already done?
        return ngBleManager.isAlreadyConnected();
    }

    /**
     * Watch is removed and won't ever be reused. This method will clear references to this watch.
     *
     * @param suuntoBtDevice
     */
    public void watchRemoved(final SuuntoBtDevice suuntoBtDevice) {
        watchStateHolders.removeWatchStateHolder(suuntoBtDevice);
    }

    /**
     * Returns a Single that starts connecting a watch when subscribed.
     * Connection will not reconnect automatically. Connect must be called again after
     * connection is lost.
     *
     * @param suuntoBtDevice SuuntoBtDevice that should be connected to
     * @return a Single that will complete with the given MAC address when the watch is
     * successfully connected. An error is emitted if connecting fails for any
     * reason. Notice! Device BLE pairing state can not be assumed after error is emitted -
     * Device may or may not be paired. Special exception android.os.OperationCanceledException
     * is emitted when watch is disconnected and pending connects are removed.
     */
    public Single<String> connectWatch(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull final WatchStateHolder watchStateHolder,
        @NonNull ConnectMetadata connectMetadata) {

        /*
        Create connect single and add it to the connect queue.
        There can be only one pending connect at the time. Next connects in the queue
        will succeed immediately on "already connected", if connect succeeded in the previous
        connect. If connect fails, next connects in queue will try to connect again.
         */
        Timber.v("Connect queue size: %s ", connectQueue.size());
        return connectQueue.addSingle(
            connectWatchSingle(suuntoBtDevice, watchStateHolder, connectMetadata)
                .doOnUnsubscribe(watchStateHolders::onConnectOver)
                .doAfterTerminate(watchStateHolders::onConnectOver), this);
    }

    private Single<String> connectWatchSingle(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull final WatchStateHolder watchStateHolder,
        @NonNull ConnectMetadata connectMetadata) {
        // Cache the WatchStateHolder to pass events properly
        if (!watchStateHolders.putOnConnectStart(suuntoBtDevice, watchStateHolder)) {
            return Single.error(new RuntimeException(
                "Connection still waiting for device serial for previous connect."));
        }

        return watchStateHolder.getStateChangeObservable()
            .first()
            .toSingle()
            .flatMap(watchState -> {
                if (watchState.isConnected()) {
                    Timber.v("Already connected");
                    return Single.just(suuntoBtDevice.getMacAddress());
                }

                String macAddress = suuntoBtDevice.getMacAddress();
                if (suuntoBtDevice.getDeviceType().isBleDevice() && !bluetoothAdapter.checkBluetoothAddress(macAddress)) {
                    return Single.error(
                        new IllegalArgumentException(
                            "Invalid watch address, MAC = " + macAddress));
                }

                final Single<String> connectWatch;
                if (connectMetadata.getConnectReason() == ConnectReason.InitialConnect
                    && suuntoBtDevice.getDeviceType().isBleDevice()) {
                    connectWatch =
                        pairingConnectNgAndLegacy(suuntoBtDevice, watchStateHolder,
                            connectMetadata);
                } else {
                    connectWatch =
                        internalConnectWatch(suuntoBtDevice, watchStateHolder, connectMetadata, false);
                }

                // Ensure ble is disconnected and then connect.
                return forceBleDisconnect(suuntoBtDevice.getMacAddress(), false)
                    .andThen(unpairDeviceIfNeeded(suuntoBtDevice, connectMetadata))
                    .andThen(connectWatch)
                    .doOnError(throwable -> {
                        Timber.w(throwable, "Error while attempting to connect to ["
                            + suuntoBtDevice.getMacAddress()
                            + "]");
                        watchStateHolder.setConnectionStateToDisconnected(
                            ngBleManager.getDisconnectReason(),
                            ngBleManager.isInvalidPacketDetectedWhenDisconnected());
                    });
            });
    }

    /**
     * Implements the pairing connect single. Adds conditional retry on pairing connect.
     */
    private Single<String> pairingConnectNgAndLegacy(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull final WatchStateHolder watchStateHolder,
        @NonNull ConnectMetadata connectMetadata
    ) {
        final AtomicBoolean pairingStarted = new AtomicBoolean(false);
        final AtomicBoolean paired = new AtomicBoolean(false);
        final String macAddress = suuntoBtDevice.getMacAddress();
        // This merge is used to subscribe to all observables below at the same time
        return Single.merge(
            // Never completes. Only sets pairingStarted AtomicBoolean when device is pairing.
            listenAndNeverComplete(pairingStarted, macAddress,
                BtStateMonitor.BtEvent.DEVICE_PAIRING),
            // Never completes. Only sets paired AtomicBoolean when device is paired.
            listenAndNeverComplete(paired, macAddress, BtStateMonitor.BtEvent.DEVICE_PAIRED),
            // First connect attempt. Only single ever completing withing merged commits here.
            internalConnectWatch(suuntoBtDevice, watchStateHolder, connectMetadata, true)
        )
            .first()
            .toSingle()
            .doOnEach(notification -> {
                Timber.d("Pairing started in this connect: %s", pairingStarted.get());
                Timber.d("Paired during this connect: %s", paired.get());
            })
            .onErrorResumeNext(throwable -> {
                final boolean retryConnection;
                if (pairingStarted.get() && !paired.get()) {
                    // No retry in this case as PIN code query has likely been shown.
                    retryConnection = false;
                } else if (throwable instanceof BluetoothWaitBondingException) {
                    if (suuntoBtDevice.getDeviceType().isAmbit()) {
                        retryConnection = false;
                    } else {
                        retryConnection =
                            GATT_CLIENT_CONNECTION_LOST.equals(throwable.getMessage());
                    }

                } else if (throwable instanceof NgBleManager.BleConnectionLostError) {
                    if (suuntoBtDevice.getDeviceType().isAmbit()) {
                        retryConnection = false;
                    } else {
                        retryConnection = true;
                    }
                } else if (throwable instanceof MdsFailedToConnectError) {
                    retryConnection = true;
                } else if (throwable instanceof GattConnectException) {
                    retryConnection =
                        !BleOperation.OPERATION_TIMEOUT.equals(throwable.getMessage());
                } else if (throwable instanceof NgBleManager.StartDataNotifyError) {
                    retryConnection = true;
                } else if (throwable instanceof BluetoothDiscoverException) {
                    retryConnection = true;
                } else {
                    retryConnection = false;
                }
                if (retryConnection) {
                    Timber.w(throwable, "Initial connect failed. Trying again.");
                    return internalConnectWatch(suuntoBtDevice,
                        watchStateHolder,
                        connectMetadata,
                        false);
                } else {
                    return Single.error(throwable);
                }
            });
    }

    private Single<String> listenAndNeverComplete(
        AtomicBoolean atomicBoolean,
        String macAddress,
        BtStateMonitor.BtEvent expectedBtEvent) {
        return RxJavaInterop.toV1Completable(btStateMonitor.btEventObservable(macAddress)
            .filter(btEvent -> btEvent == expectedBtEvent)
            .firstOrError()
            .ignoreElement())
            .doOnCompleted(() -> atomicBoolean.set(true))
            // Must never fail.
            .onErrorResumeNext(throwable -> Completable.never())
            .andThen(Completable.never())
            .andThen(Single.just(""));
    }

    private Completable unpairDeviceIfNeeded(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull ConnectMetadata connectMetadata
    ) {
        // Connecting to NG watch always fails if watch is unpaired but phone is paired.
        // Device advertised state information is reliable for NG devices only.
        // This is causing about 5% of the pairing errors with NG devices.
        if (connectMetadata.getConnectReason() == ConnectReason.InitialConnect &&
            suuntoBtDevice.getDeviceType().isNgDevice() &&
            connectMetadata.getDeviceAdvertisedPairingState() == PairingState.Unpaired &&
            connectMetadata.getPhonePairingStateBeforeConnect() == PairingState.Paired) {
            Timber.d("Unpairing phone from device because device is unpaired");
            // Ensure device instance is removed from Ble layer and unpair device from phone.
            // Small wait is needed because otherwise unpair event received from the OS may
            // immediately stop the reconnect attempt done after this.
            return destroyBleDevice(suuntoBtDevice)
                .andThen(unpairDeviceFromPhone(suuntoBtDevice))
                .onErrorComplete()
                .andThen(Completable.timer(2,  TimeUnit.SECONDS));
        } else {
            return Completable.complete();
        }
    }

    private Single<String> internalConnectWatch(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull final WatchStateHolder watchStateHolder,
        @NonNull ConnectMetadata connectMetadata,
        boolean shortMdsConnectTimeout) {
        String macAddress = suuntoBtDevice.getMacAddress();
        final boolean reconnecting =
            connectMetadata.getConnectReason() != ConnectReason.InitialConnect;
        final String uuid =
            Integer.toHexString(sdsBleAddressMap.getBleHandleGenerateNew(macAddress));

        // Connect BLE and MDS to watch. Both needs to be connected before watch is connected.
        return connectBle(suuntoBtDevice, uuid, connectMetadata)
            .zipWith(waitMdsConnectsWatch(suuntoBtDevice, shortMdsConnectTimeout),
                (s, s2) -> suuntoBtDevice.getMacAddress())
            // Handle error in connecting to watch.
            .onErrorResumeNext(
                throwable -> handleWatchConnectError(throwable, macAddress, reconnecting,
                    connectMetadata.getConnectReason() == ConnectReason.InitialConnect))
            .subscribeOn(schedulerProvider.io())
            .observeOn(schedulerProvider.mainThread())
            .doOnSubscribe(() -> Timber.v("Trying to connect whiteboard watch. Name = "
                + suuntoBtDevice.getName()
                + ", MAC = "
                + suuntoBtDevice.getMacAddress()
                + ", serialVisible = "
                + suuntoBtDevice.getSerial()
                + ", uuid = "
                + uuid))
            .doOnSubscribe(() -> {
                ngBleManager.clearInvalidPacketDetectedState();
                if (reconnecting) {
                    watchStateHolder.setLastConnectionStarted(System.currentTimeMillis());
                    watchStateHolder.setConnectionState(
                        WatchState.ConnectionState.RECONNECTING);
                } else {
                    watchStateHolder.setLastConnectionStarted(System.currentTimeMillis());
                    watchStateHolder.setConnectionState(WatchState.ConnectionState.CONNECTING);
                }
            })
            .doOnSuccess(s -> {
                // Run on main thread
                watchStateHolder.setDeviceBusy(false);
                watchStateHolder.setRegistered(true);
                watchStateHolder.setConnectionState(WatchState.ConnectionState.CONNECTED);
            });
    }

    /**
     * Handle connect error.
     *
     * @param throwable Connect error exception.
     * @param macAddress Device mac adress.
     * @param shortDisconnectWait If true, there is shorter wait after disconnect
     * @return Single for handling the watch connect error.
     */
    private Single<String> handleWatchConnectError(@NonNull Throwable throwable,
        @NonNull String macAddress, boolean reconnecting, boolean shortDisconnectWait) {
        if (throwable instanceof MdsFailedToConnectError) {
            return disconnectBleAfterMdsError(macAddress, throwable, shortDisconnectWait);
        }
        return Single.error(throwable);
    }

    /**
     * BLE connect device.
     *
     * @param suuntoBtDevice Device to be connected.
     * @param uuid Sds Ble address.
     * @return Single emitting device mac address when ble connects.
     */
    private Single<String> connectBle(
        @NonNull final SuuntoBtDevice suuntoBtDevice,
        @NonNull final String uuid,
        @NonNull final ConnectMetadata connectMetadata) {
        return ngBleManager.directConnect(uuid, suuntoBtDevice.getDeviceType(), connectMetadata)
            .toSingleDefault(suuntoBtDevice.getMacAddress())
            .doOnSuccess(s -> Timber.v("Ble connected to device %s", s))
            .doOnError(throwable -> Timber.v("Ble connection failed to device %s",
                suuntoBtDevice.getMacAddress()));
    }

    /**
     * Analytics method. Has there been invalid packet(s) since last watch connect.
     *
     * @return True, if invalid packets detected.
     */
    public boolean isInvalidPacketDetectedAfterLastWatchConnect() {
        return ngBleManager.isInvalidPacketDetectedWhenDisconnected();
    }

    /**
     * Disconnect ble and emit error given as a parameter.
     *
     * @param macAdress BLE device to be disconnected.
     * @param mdsError Error to be emitted.
     * @param shortDisconnectWait
     * @return Single for disconnecting BLE.
     */
    private Single<String> disconnectBleAfterMdsError(String macAdress, Throwable mdsError,
        boolean shortDisconnectWait) {
        return forceBleDisconnect(macAdress, shortDisconnectWait)
            .andThen(Single.error(mdsError));
    }

    /**
     * Forces BLE to disconnect if it is connected. This used only for special connectivity
     * error resolving.
     *
     * @param macAddress Device to be disconnected.
     * @param shortDisconnectWait If true, there is a shorter wait after disconnect.
     * @return Completable for force disconnecting BLE.
     */
    public Completable forceBleDisconnect(String macAddress, boolean shortDisconnectWait) {
        return ngBleManager.isBleOn()
            .flatMapCompletable(bleOn -> {
                if (bleOn) {
                    return Completable.fromAction(() -> {
                        Timber.v("Ensure BLE isDisconnected");
                        ngBleManager.disconnectBle(macAddress);
                    })
                        // Must not be subscribed on main thread, because ble queue is running
                        // on main thread and NgBleManager.disconnectBle would deadlock.
                        .subscribeOn(schedulerProvider.io())
                        .onErrorComplete()
                        .andThen(Completable.timer(
                            shortDisconnectWait ? SHORT_DELAY_AFTER_FORCED_BLE_DISCONNECT_IN_SECONDS
                                : DELAY_AFTER_FORCED_BLE_DISCONNECT_IN_SECONDS,
                            TimeUnit.SECONDS));
                } else {
                    return Completable.complete();
                }
            });
    }

    /**
     * Wait MDS connects to watch or fails to connect.
     *
     * @param suuntoBtDevice device to wait for
     * @param shortMdsConnectTimeout
     * @return a Single emitting the device mac address when the mds gets connected to watch or an
     * error in case of mds connect error or BLE connection loss.
     */
    private Single<String> waitMdsConnectsWatch(@NonNull final SuuntoBtDevice suuntoBtDevice,
        boolean shortMdsConnectTimeout) {
        return doWaitMdsConnectsWatch(suuntoBtDevice)
            .ambWith(mdsConnectTimeoutError(suuntoBtDevice.getMacAddress(), shortMdsConnectTimeout))
            .ambWith(mdsFailedToConnectError())
            .ambWith(ngBleManager.bleConnectionLostError(suuntoBtDevice.getMacAddress()))
            .first()
            .toSingle()
            .doOnSuccess(s -> Timber.v("Device %s connected to MDS", suuntoBtDevice.getName()));
    }

    /**
     * Waits until a MDS gets connected to watch.
     *
     * @param suuntoBtDevice device to wait for
     * @return a Single emitting the device mac address when the MDS gets connected to watch.
     */
    private Observable<String> doWaitMdsConnectsWatch(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        return mdsConnectedPublishSubject
            .filter(serial -> !SuuntoDeviceType.advertisementHasSerial(suuntoBtDevice.getDeviceType())
                || serial.equals(suuntoBtDevice.getSerial()))
            .map(serial -> suuntoBtDevice.getMacAddress())
            .first();
    }

    public Completable disconnectWatch(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        Timber.v("disconnectWatch %s", suuntoBtDevice.getMacAddress());
        return disconnectWatchCompletable(suuntoBtDevice);
    }

    private Completable disconnectWatchCompletable(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        return bleServiceDeviceInterface.disconnect(suuntoBtDevice.getMacAddress())
            .doOnSubscribe(() -> {
                // Cancel all the pending connects.
                connectQueue.cancel(this);
            })
            .doOnSuccess(result -> {
                if (result != BleCore.BLE_OK) {
                    throw Exceptions.propagate(new Throwable());
                }
            })
            .observeOn(schedulerProvider.mainThread())
            .toCompletable();
    }

    public Completable destroyBleDevice(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        Timber.v("distroyBleDevice %s", suuntoBtDevice.getMacAddress());
        return bleServiceDeviceInterface.destroyBleDevice(suuntoBtDevice.getMacAddress());
    }

    public Completable unpairWatch(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        Timber.v("unpairWatch %s", suuntoBtDevice.getMacAddress());
        return unpairWatchCompletable(suuntoBtDevice).onErrorComplete(throwable -> {
            // Ignore errors
            Timber.v(throwable,"Ignoring unpairing error");
            return true;
        });
    }

    private Completable unpairWatchCompletable(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        String uri = String.format(Locale.US,
            SuuntoConnectivityConstants.MDS_URI_UNPAIR_DEVICE,
            suuntoBtDevice.getSerial());

        return mdsRx.put(uri,
            SuuntoConnectivityConstants.MDS_UNPAIR_DEVICE_CONTRACT)
            // Complete in two seconds, the request usually completes with REQUEST_TIMEOUT anyway.
            // We don't want to wait that long
            .timeout(2, TimeUnit.SECONDS)
            .toCompletable();
    }

    /**
     * Emits MdsConnectTimeoutError if MDS is not connected within timeout.
     * This workaround is needed because MDS does not always report
     * "FailedToConnect" via ConnectingDevice resource. This may happen
     * if whiteboard failed to handshake with watch.
     *
     * @return Error emitting observable.
     */
    // Todo: Remove this timeout error if MDS is fixed to return FailedToConnect always.
    private Observable<String> mdsConnectTimeoutError(String bleMac,
        boolean shortMdsConnectTimeout) {
        return ngBleManager.bleConnected(bleMac)
            .andThen(Observable.timer(shortMdsConnectTimeout ? MDS_SHORT_CONNECTION_TIMEOUT_SECONDS
                : MDS_CONNECTION_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .map(aLong -> {
                    Timber.d("MDS connection timeout");
                    throw new MdsConnectTimeoutError();
                }));
    }

    /**
     * Emits MdsFailedToConnectError if MDS report "FailedToConnect" state.
     *
     * @return Error emitting observable.
     */
    private Observable<String> mdsFailedToConnectError() {
        return mdsRx.connectingDevicesObservable()
            .filter(s -> s.getState().equals(MDS_CONNECTING_DEVICES_FAILED_TO_CONNECT))
            .doOnNext(s -> {
                String error = "";
                if (s.getEventInfo() != null) {
                    error = s.getEventInfo();
                }
                throw new MdsFailedToConnectError(error);
            }).map(MdsConnectingDevice::getState);
    }

    void onDeviceConnected(final MdsConnectedDevice device) {
        final String variant = device.getDeviceInfo().getVariant();
        final String serial = device.getSerial();

        final ISuuntoDeviceCapabilityInfo capabilityInfo =
            SuuntoDeviceCapabilityInfoProvider.get(variant);
        if (!capabilityInfo.isWatch()) {
            Timber.w("Variant %s not recognised as watch", variant);
            return;
        }

        final WatchStateHolder watchStateHolder = watchStateHolders.onConnected(device);
        mdsConnectedPublishSubject.onNext(serial);
        if (watchStateHolder == null) {
            Timber.e("onDeviceConnected cannot find WatchStateHolder with given name or serial! This means most that the device connect() was never called.");
            return;
        }

        // Set these details to WatchStateHolder
        Timber.v("onDeviceConnected: %s", device.getDeviceInfo().toString());
        watchStateHolder.setDeviceInfo(device.getDeviceInfo());
    }

    void onDeviceDisconnected(MdsConnectedDevice device) {

        Timber.v("onDeviceDisconnected, serialVisible = %s", device.getSerial());

        WatchStateHolder watchStateHolder = watchStateHolders.onDisconnected(device);
        if (watchStateHolder == null) {
            Timber.e("onDeviceDisconnected called serial with no WatchStateHolder! This means most that the device connect() was never called.");
            return;
        }

        watchStateHolder.setConnectionStateToDisconnected(
            ngBleManager.getDisconnectReason(),
            ngBleManager.isInvalidPacketDetectedWhenDisconnected());
    }

    public Completable unpairDeviceFromPhone(@NonNull final SuuntoBtDevice suuntoBtDevice) {
        return Completable.fromAction(() -> {
            try {
                doSystemUnpairDevice(suuntoBtDevice);
            } catch (Exception e) {
                Timber.e("systemUnpairDevice %s failed", suuntoBtDevice.getMacAddress());
            }
        });
    }

    private void doSystemUnpairDevice(@NonNull final SuuntoBtDevice suuntoBtDevice) throws Exception {
        doSystemUnpairBtDevice(bluetoothAdapter.getRemoteDevice(suuntoBtDevice.getMacAddress()));
    }

    private void doSystemUnpairBtDevice(BluetoothDevice device) throws Exception {
        Method m = device.getClass().getMethod("removeBond", (Class[]) null);
        m.invoke(device, (Object[]) null);
    }

    public Subscription getMdsConnectedSubscription(Action1<String> onNext, Action1<Throwable> onError) {
        return mdsConnectedPublishSubject
            .doOnError(t -> Timber.e(t, "Mds ConnectedDevice subscription error: %s", t.getMessage()))
            .onErrorResumeNext(Observable.empty())
            .subscribe(onNext, onError);
    }
}
