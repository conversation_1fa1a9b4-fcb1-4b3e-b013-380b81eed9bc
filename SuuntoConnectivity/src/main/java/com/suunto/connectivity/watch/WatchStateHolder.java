package com.suunto.connectivity.watch;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.notifications.MdsNotificationCategoryEnabled;
import com.suunto.connectivity.notifications.NotificationState;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.sync.SyncState;
import com.suunto.connectivity.util.DisconnectReason;
import com.suunto.connectivity.watch.navigate.NavigateState;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import rx.Observable;
import rx.subjects.BehaviorSubject;
import rx.subjects.SerializedSubject;
import timber.log.Timber;

/**
 * Holds current watch state.
 * <p>
 * This class is thread safe.
 */
public class WatchStateHolder {

    // True, until WatchStateHolder.onDestroy() is called and holder is not active anymore.
    private final AtomicBoolean holderActive = new AtomicBoolean(true);

    /**
     * Subject that emits the most recent {@link WatchState}
     */
    private final BehaviorSubject<WatchState> watchStateSubject =
        BehaviorSubject.create(WatchState.builder().build());

    /**
     * Serializing watchStateSubject to allow calling `onNext` and other on-methods from multiple
     * threads safely, without violating observable contract.
     */
    private final SerializedSubject<WatchState, WatchState> watchStateSerializedSubject =
        watchStateSubject.toSerialized();

    // The latest disconnect reason. Used for analytics.
    private final AtomicReference<DisconnectReason> disconnectReason =
        new AtomicReference<>(DisconnectReason.Unknown);

    // Invalid packets detected when disconnected?. Used for analytics.
    private final AtomicBoolean invalidPacketDetectedWhenDisconnected = new AtomicBoolean(false);

    /**
     * Gets an observable emitting watch state changes.
     * <p>
     * Note that the state changes are not emitted on any specific scheduler. UI code should usually
     * add {@code observeOn(AndroidSchedulers.mainThread())}.
     *
     * @return an Observable emitting watch state changes
     */
    public Observable<WatchState> getStateChangeObservable() {
        return watchStateSubject.asObservable()
            .doOnNext(
                watchState -> Timber.v("Holder [" + WatchStateHolder.this + "] state changed to [" +
                    watchState + "]"))
            .distinctUntilChanged().onBackpressureBuffer();
    }

    /**
     * WARNING: This function returns null after {@link #onDestroy()} is called. Not annotate it
     * with @Nullable because this function is called in many different places, but it currently
     * only affects TransferringFirmwareState.handleTransferError().
     *
     * @return The current state
     */
    public WatchState getCurrentState() {
        return watchStateSubject.getValue();
    }

    private void setCurrentState(@NonNull WatchState watchState) {
        if (holderActive.get()) {
            if (!getCurrentState().equals(watchState)) {
                Timber.v("Holder [%s] publishing new state [%s]", WatchStateHolder.this,
                    watchState);
                watchStateSerializedSubject.onNext(watchState);
            }
        } else {
            Timber.v("New state [" + watchState + "] not published.");
        }
    }

    public synchronized void setPaired(boolean paired) {
        setCurrentState(getCurrentState().withPaired(paired));
    }

    public synchronized void setRegistered(boolean registered) {
        setCurrentState(getCurrentState().withRegistered(registered));
    }

    public synchronized void setConnectedGpsInUse(boolean inUse) {
        setCurrentState(getCurrentState().withConnectedGpsInUse(inUse));
    }

    synchronized void setConnectionStateToDisconnected(
        DisconnectReason disconnectReason,
        boolean invalidPacketDetectedWhenDisconnected
    ) {
        this.disconnectReason.set(disconnectReason);
        this.invalidPacketDetectedWhenDisconnected.set(invalidPacketDetectedWhenDisconnected);
        setCurrentState(
            getCurrentState().withConnectionState(WatchState.ConnectionState.DISCONNECTED));
    }

    synchronized void setConnectionState(WatchState.ConnectionState connectionState) {
        if (connectionState == WatchState.ConnectionState.DISCONNECTED) {
            setConnectionStateToDisconnected(DisconnectReason.Unknown, false);
        } else {
            setCurrentState(getCurrentState().withConnectionState(connectionState));
        }
    }

    public synchronized void setSyncState(SyncState syncState) {
        setCurrentState(getCurrentState().withSyncState(syncState));
    }

    public synchronized void setDeviceBusy(boolean deviceBusy) {
        setCurrentState(getCurrentState().withDeviceBusy(deviceBusy));
    }

    synchronized void setDeviceInfo(MdsDeviceInfo deviceInfo) {
        setCurrentState(getCurrentState().withDeviceInfo(deviceInfo));
    }

    public synchronized void setConnectionQuality(WatchState.ConnectionQuality connectionQuality) {
        setCurrentState(getCurrentState().withConnectionQuality(connectionQuality));
    }

    synchronized void setLastConnectionStarted(Long timeInMillis) {
        setCurrentState(getCurrentState().withLastConnectStarted(timeInMillis));
    }

    public synchronized void setFirmwareUpdateStatus(boolean uploadInProgress,
        @Nullable Integer progressPercentage) {
        FirmwareUpdateStatus newStatus = getCurrentState().getFirmwareUpdateStatus()
            .withUploadProgress(uploadInProgress, progressPercentage);
        setCurrentState(getCurrentState().withFirmwareUpdateStatus(newStatus));
    }

    public synchronized void setOTAUpdate(boolean enabled) {
        FirmwareUpdateStatus newStatus =
            getCurrentState().getFirmwareUpdateStatus().withOTAUpdateSupported(enabled);
        setCurrentState(getCurrentState().withFirmwareUpdateStatus(newStatus));
    }

    public synchronized void setFirmwareVersionCurrentlyTransferred(
        @NonNull String firmwareVersion,
        @Nullable Long packageId,
        @NonNull String firmwareVersionLog
    ) {
        FirmwareUpdateStatus newStatus = getCurrentState().getFirmwareUpdateStatus()
            .withFirmwareVersionCurrentlyTransferred(firmwareVersion, packageId, firmwareVersionLog);
        setCurrentState(getCurrentState().withFirmwareUpdateStatus(newStatus));
    }

    public synchronized void setFirmwareFileSizeInBytes(Long sizeInBytes) {
        FirmwareUpdateStatus newStatus = getCurrentState().getFirmwareUpdateStatus()
            .withFileSizeInBytes(sizeInBytes);
        setCurrentState(getCurrentState().withFirmwareUpdateStatus(newStatus));
    }

    public DisconnectReason getDisconnectReason() {
        return disconnectReason.get();
    }

    public boolean isInvalidPacketDetectedWhenDisconnected() {
        return invalidPacketDetectedWhenDisconnected.get();
    }

    public synchronized void setNotificationEnabled(boolean enabled) {
        NotificationState newState = getCurrentState().getNotificationState().withEnabled(enabled);
        setCurrentState(getCurrentState().withNotificationState(newState));
    }

    public synchronized void setNotificationCategoryEnabled(
        boolean call,
        boolean sms,
        boolean application) {
        NotificationState newState = getCurrentState().getNotificationState()
            .withCategoryEnabled(call, sms, application);
        setCurrentState(getCurrentState().withNotificationState(newState));
    }

    public synchronized void setNotificationState(NotificationState notificationState) {
        setCurrentState(getCurrentState().withNotificationState(notificationState));
    }

    public synchronized void setNotificationCategorySwitchState(
        MdsNotificationCategoryEnabled categoryEnabled) {
        WatchState currentState = getCurrentState();
        NotificationState notificationStateNew = new NotificationState(
            currentState.getNotificationState().getEnabled(),
            categoryEnabled.getCall(),
            categoryEnabled.getMessages(),
            categoryEnabled.getOthers()
        );
        setCurrentState(currentState.withNotificationState(notificationStateNew));
    }

    synchronized public void onDestroy() {
        setRegistered(false);
        holderActive.set(false);
        watchStateSerializedSubject.onCompleted();
    }
}
