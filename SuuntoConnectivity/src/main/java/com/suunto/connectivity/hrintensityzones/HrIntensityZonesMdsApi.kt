package com.suunto.connectivity.hrintensityzones

import com.squareup.moshi.Moshi
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.utils.toV2Flowable
import com.suunto.connectivity.repository.commands.SetHrIntensityZonesContract
import com.suunto.connectivity.repository.commands.WatchCombinedHrIntensityZones
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.reactive.asFlow
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

class HrIntensityZonesMdsApi @Inject constructor(
    private val mdsRx: MdsRx,
    private val moshi: <PERSON><PERSON>,
) : HrIntensityZonesWatchApi {

    companion object {
        const val HR_INTENSITY_ZONES_URI: String = "%s/Settings/User/HrIntensityZones"
    }

    private fun <T : Any> mdsSubscribe(uri: String, classOfT: Class<T>): Flow<T> =
        // Don't need to get the data immediately.
        mdsRx.subscribe(uri, classOfT, false).toV2Flowable().asFlow().distinctUntilChanged()

    private suspend fun mdsPut(uri: String, contract: String): String =
        mdsRx.put(uri, contract).await()

    override suspend fun setHrIntensityZones(
        serial: String,
        hrIntensityZones: WatchCombinedHrIntensityZones
    ) {
        runSuspendCatching {
            mdsPut(
                String.format(Locale.US, MDS_SCHEME_PREFIX + HR_INTENSITY_ZONES_URI, serial),
                moshi.adapter(SetHrIntensityZonesContract::class.java)
                    .toJson(SetHrIntensityZonesContract(hrIntensityZones))
            )
        }.onFailure {
            Timber.w(it, "Fail to set hr & intensity zones.")
            throw it
        }
    }

    override suspend fun subscribeHrIntensityZones(serial: String): Flow<WatchCombinedHrIntensityZones> {
        val uri = String.format(Locale.US, HR_INTENSITY_ZONES_URI, serial)
        return mdsSubscribe(uri, WatchCombinedHrIntensityZones::class.java)
    }
}
