package com.suunto.connectivity.sdsmanager;

import androidx.annotation.Nullable;

import java.util.HashMap;
import java.util.Map;

import timber.log.Timber;

// Todo: Tomi this class is under construction. Generate whiteboard id based on BLE mac.

/**
 * Keeps track of Whiteboard address vs. BLE MAC mapping.
 * WB addresses are generated on need bases, they are hexadecimal numbers with 8 _significant_
 * digits
 */
public class SdsBleAddressMap {

    // Needs to be 8 _significant_ digits due to some SDS/WB implementation specifics
    public static final int DEFAULT_HANDLE = 0x10000001;
    public static final int INVALID_HANDLE = -1;

    //private final Preferences preferences;

    // Container for known NG BLE addresses and their corresponding handles == wbAddresses
    private final Map<Integer, String> addressMap = new HashMap<>();

    private int nextDeviceHandle = 0x10000001;

    public SdsBleAddressMap() {
        //addressMap = readAddressMapFromPreferences();

        //nextDeviceHandle = nextHandle >= DEFAULT_HANDLE ? nextHandle : DEFAULT_HANDLE;
    }

    /**
     * Given a wbAddress (which is the same as <PERSON><PERSON><PERSON><PERSON><PERSON> but String) return the corresponding BLE
     * MAC.
     * In not found and error cases return null.
     *
     * @return BLE MAC address, or null if not found from the internal address map
     */
    public @Nullable
    String wbAddressToBleMac(@Nullable String wbAddress) {
        if (wbAddress == null || wbAddress.isEmpty()) {
            return null;
        }

        synchronized (addressMap) {
            try {
                int handle = Integer.valueOf(wbAddress, 16);
                return addressMap.get(handle);
            } catch (NumberFormatException ex) {
                Timber.e(
                    "Invalid wbAddress, unable to map to MAC address: %s", wbAddress);
                return null;
            }
        }
    }

    /**
     * Given a BLE MAC address, return the corresponding int handle.
     *
     * @return handle or INVALID_HANDLE
     */
    public int getBleHandle(@Nullable String bleMac) {
        if (bleMac == null || bleMac.isEmpty()) {
            return INVALID_HANDLE;
        }

        synchronized (addressMap) {
            for (Map.Entry<Integer, String> entry : addressMap.entrySet()) {
                if (entry.getValue().equals(bleMac)) {
                    return entry.getKey();
                }
            }

            return INVALID_HANDLE;
        }
    }

    /**
     * Given a BLE MAC address, return the corresponding int handle. If BLE MAC is not known,
     * generate a new handle.
     *
     * @return handle or INVALID_HANDLE
     */
    public int getBleHandleGenerateNew(@Nullable String bleMac) {
        if (bleMac == null || bleMac.isEmpty()) {
            return INVALID_HANDLE;
        }

        int handle = getBleHandle(bleMac);
        if (handle == INVALID_HANDLE) {
            handle = getBleNextHandle();
            addressMap.put(handle, bleMac);
        }

        return handle;
    }

    /**
     * Get the current nextDeviceHandle, either stored locally or in preferences.
     * Updates the next possible handle value by adding one to the current value.
     *
     * @return Handle
     */
    private int getBleNextHandle() {
        int handle = nextDeviceHandle++;
        //preferences.setBleNextHandle(nextDeviceHandle);

        return handle;
    }
}
