package com.suunto.connectivity.watchface

import com.stt.android.coroutines.await
import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryException
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.Response

class WatchFaceQueryConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {

    private val relatedClass = listOf(
        GetWatchFaceCapabilitiesResponse::class.java,
        GetWatchFaceInstalledListResponse::class.java,
        GetWatchFaceInstalledPathResponse::class.java,
        GetWatchFaceInfoResponse::class.java,
        SyncWatchFaceResponse::class.java,
        WatchFaceSimpleResponse::class.java,
    )

    override fun isResponseRelated(response: Response): Boolean =
        response::class.java in relatedClass

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*>? {
        return ResponseMessage(messageId, response)
    }

    suspend fun getWatchFaceCapabilities(macAddress: String): GetWatchFaceCapabilitiesResponse {
        return sendQuery<GetWatchFaceCapabilitiesResponse>(GetWatchFaceCapabilitiesQuery(macAddress))
    }

    suspend fun getOnlineWatchFaceList(macAddress: String): List<MdsWatchFace> {
        return sendQuery<GetWatchFaceInstalledListResponse>(GetWatchFaceInstalledListQuery(macAddress)).lists
    }

    suspend fun getCurrentWatchFaceInfo(macAddress: String): MdsWatchFace? {
        return sendQuery<GetWatchFaceInfoResponse>(GetCurrentWatchFaceInfoQuery(macAddress)).watchFace
    }

    suspend fun getWatchFaceInfo(macAddress: String, watchFaceId: String): MdsWatchFace? {
        return sendQuery<GetWatchFaceInfoResponse>(GetWatchFaceInfoQuery(macAddress, watchFaceId)).watchFace
    }

    suspend fun setAsCurrentWatchFace(macAddress: String, watchFaceId: String): Boolean {
        return sendSimpleQuery(SetAsCurrentWatchFaceQuery(macAddress, watchFaceId))
    }

    suspend fun putWatchFaceInfoBeforeInstall(
        macAddress: String,
        contract: InstallWatchFaceContract
    ): Boolean {
        return sendSimpleQuery(PutWatchFaceInfoQuery(macAddress, contract))
    }

    suspend fun getWatchFaceInstallPath(macAddress: String): String {
        return sendQuery<GetWatchFaceInstalledPathResponse>(GetWatchFaceInstallPathQuery(macAddress)).installPath
    }

    suspend fun startInstallWatchFace(macAddress: String): Boolean {
        return sendSimpleQuery(StartWatchFaceInstallQuery(macAddress))
    }

    suspend fun uninstallWatchFace(macAddress: String, watchFaceId: String): Boolean {
        return sendSimpleQuery(UninstallWatchFaceInfoQuery(macAddress, watchFaceId))
    }

    suspend fun syncWatchFace(macAddress: String) {
        sendQuery<SyncWatchFaceResponse>(SyncWatchFaceQuery(macAddress))
    }

    private suspend fun sendSimpleQuery(
        query: DeviceSpecificQuery
    ): Boolean {
        return sendQuery<WatchFaceSimpleResponse>(query).isSuccess
    }

    private suspend inline fun <reified T : Response> sendQuery(query: DeviceSpecificQuery): T {
        return suuntoRepositoryClient.waitForServiceReady()
            .andThen(suuntoRepositoryClient.sendQuery(query).first().toSingle().map { response ->
                if (response is T) {
                    return@map response
                }
                (response as? ErrorResponse)?.cause?.let { error -> throw error }
                throw SuuntoRepositoryException("Invalid response [$response]")
            })
            .await()
    }
}
