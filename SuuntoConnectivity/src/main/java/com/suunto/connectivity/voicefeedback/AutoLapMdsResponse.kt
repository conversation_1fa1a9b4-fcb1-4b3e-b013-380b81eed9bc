package com.suunto.connectivity.voicefeedback

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * AutoLap object that MDS understands used when pushing and pulling from watch
 */
@Parcelize
data class AutoLapMdsResponse(
    @SerializedName("LapNumber") val lapNumber: Int?, // lap number
    @SerializedName("LapMode") val lapMode: Int?, // number, 1 distance 2 duration
    @SerializedName("LapActivityID") val lapActivityID: Int?, // workout id
    @SerializedName("LapPODHeartRateSW") val lapPODHeartRateSW: Boolean?,
    @SerializedName("LapGpsIntervalSW") val lapGpsIntervalSW: Int?, //  gps switch 0 off 1 on
    @SerializedName("Distance") val distance: Double?, // distance of the total in meters
    @SerializedName("Duration") val duration: Double?, // time of the total in seconds
    @SerializedName("Energy") val energy: Double?, // energy in KJ
    @SerializedName("AverageSpeed") val averageSpeed: Double?, // speed in meters per second
    @SerializedName("TotalAverageHeartRate") val totalAverageHeartRate: Double?, // hz
    @SerializedName("CurrentSpeed") val currentSpeed: Double?, // speed in meters per second
    @SerializedName("CurrentHeartRate") val currentHeartRate: Double?, // hz
    @SerializedName("SplitTime") val splitTime: Double?, // time of the lap in seconds
    @SerializedName("SplitDistance") val splitDistance: Double?, // distance of the lap in seconds
    @SerializedName("SplitAverageSpeed") val splitAverageSpeed: Double?, // speed in meters per second
    @SerializedName("SplitAverageHeartRate") val splitAverageHeartRate: Double?, // hz
    @SerializedName("SplitMaxHeartRate") val splitMaxHeartRate: Double?, // hz
    @SerializedName("MeasurementUnit") val measurementUnit: Int?, // measurementUnit = MeasurementUnit.METRIC.key 0 if the unit is metric, measurementUnit = MeasurementUnit.IMPERIAL.key 1 if the unit is imperial
    @SerializedName("LapPODPowerSW") val lapPODPowerSW: Boolean?, // pod power switch false off true on
    @SerializedName("TotalAscent") val totalAscent: Double?, // ascent of the total in meter
    @SerializedName("SplitPower") val splitPower: Double?, // power of the lap in watt
    @SerializedName("SplitAscent") val splitAscent: Double?, // ascent of the lap in meter
    @SerializedName("SplitDescent") val splitDescent: Double?, // descent of the lap in meter
    @SerializedName("SplitCadence") val splitCadence: Double? // cadence of the lap in hz
) : Parcelable {

    fun isIndoorActivities(): Boolean {
        return lapGpsIntervalSW == GPS_OFF
    }
}

/**
 * GPS switch button
 */
const val GPS_ON = 1
const val GPS_OFF = 0

const val LAP_MODE_OFF = 0
const val LAP_MODE_DISTANCE = 1
const val LAP_MODE_DURATION = 2
