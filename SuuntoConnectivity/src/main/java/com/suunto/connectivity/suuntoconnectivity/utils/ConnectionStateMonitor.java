package com.suunto.connectivity.suuntoconnectivity.utils;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import androidx.annotation.NonNull;

import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattConnectionStateChangedEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.event
    .BleGattServerConnectionStateChangedEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Keeps track of bluetooth device connection states by listening to connection
 * state change events.
 *
 * This class is needed because BluetoothManager.getConnectionState may return
 * incorrect values when BluetoothGatt and BluetoothGattServer are used at the
 * same time.
 */
public class ConnectionStateMonitor {

    private final BluetoothManager bluetoothManager;
    private final EventBus eventBus;
    private final Map<DeviceAndProfile, Integer> connectionStates;

    public ConnectionStateMonitor(@NonNull BluetoothManager bluetoothManager,
        @NonNull EventBus eventBus) {
        this.bluetoothManager = bluetoothManager;
        this.eventBus = eventBus;

        connectionStates = Collections.synchronizedMap(new HashMap<DeviceAndProfile, Integer>());

        eventBus.register(this);
    }

    /**
     * Get the current connection state of the profile to the remote device.
     * Needs nearby permissions.
     *
     * @param device Bluetooth device
     * @param profile {@link BluetoothProfile#GATT} or {@link BluetoothProfile#GATT_SERVER}
     * @return State of the profile connection. One of
     * {@link BluetoothProfile#STATE_CONNECTED},
     * {@link BluetoothProfile#STATE_CONNECTING},
     * {@link BluetoothProfile#STATE_DISCONNECTED},
     * {@link BluetoothProfile#STATE_DISCONNECTING}
     */
    @SuppressLint("MissingPermission")
    public int getConnectionState(BluetoothDevice device, int profile) {
        // Try to get the connection state from our internal map first
        Integer connectionState = connectionStates.get(new DeviceAndProfile(device, profile));
        if (connectionState != null) {
            return connectionState;
        }

        // Fall back to using BluetoothManager
        return bluetoothManager.getConnectionState(device, profile);
    }

    public void onDestroy() {
        eventBus.unregister(this);
    }

    @Subscribe
    public void onBleGattConnectionStateChanged(BleGattConnectionStateChangedEvent event) {
        connectionStates.put(new DeviceAndProfile(
                event.getBluetoothGatt().getDevice(), BluetoothProfile.GATT),
            event.getNewState());
    }

    @Subscribe
    public void onBleGattServerConnectionStateChanged(
        BleGattServerConnectionStateChangedEvent event) {
        connectionStates.put(new DeviceAndProfile(
                event.getBluetoothDevice(), BluetoothProfile.GATT_SERVER),
            event.getNewState());
    }

    private static class DeviceAndProfile {
        private final BluetoothDevice device;
        private final int profile;

        public DeviceAndProfile(@NonNull BluetoothDevice device, int profile) {
            this.device = device;
            this.profile = profile;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DeviceAndProfile that = (DeviceAndProfile) o;

            if (profile != that.profile) return false;
            return device.equals(that.device);
        }

        @Override
        public int hashCode() {
            int result = device.hashCode();
            result = 31 * result + profile;
            return result;
        }
    }
}
