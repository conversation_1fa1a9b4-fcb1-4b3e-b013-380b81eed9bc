package com.suunto.connectivity.suuntoconnectivity.ble;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattServer;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Handler;
import androidx.annotation.NonNull;
import com.suunto.connectivity.suuntoconnectivity.ble.event
    .BleGattServerConnectionStateChangedEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattServerException;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationAddService;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationClose;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationConnect;
import com.suunto.connectivity.suuntoconnectivity.ble.operation
    .BleGattServerOperationConnectAndDiscover;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationDisconnect;
import com.suunto.connectivity.suuntoconnectivity.ble.operation
    .BleGattServerOperationNotifyCharacteristicChanged;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationOpen;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationRemoveService;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BleGattServerOperationSendResponse;
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BluetoothOperationDiscover;
import com.suunto.connectivity.suuntoconnectivity.utils.AndroidBtEnvironment;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectStrategy;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectionStateMonitor;
import com.suunto.connectivity.util.workqueue.QueueOperation;
import com.suunto.connectivity.util.workqueue.QueueOperationDelay;
import com.suunto.connectivity.util.workqueue.WorkQueue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.jdeferred.DoneCallback;
import org.jdeferred.DonePipe;
import org.jdeferred.FailFilter;
import org.jdeferred.Promise;
import org.jdeferred.impl.DeferredObject;
import org.jdeferred.multiple.MasterDeferredObject;
import org.jdeferred.multiple.OneReject;
import timber.log.Timber;

/**
 * Encapsulates BluetoothGattServer and handles connection sharing.
 * <p>
 * Each user should create a {@link Client} instance by calling {@link #createClient(String)}.
 * GATT server operations are then accessed by calling methods of the client instance.
 * When multiple clients connect to the same bluetooth device, the connection is
 * not closed until all the clients have disconnected.
 */
public class BleGattServer {

    private final Context context;
    private final WorkQueue workQueue;
    private final BluetoothManager bluetoothManager;
    private final ConnectionStateMonitor connectionStateMonitor;
    private final AndroidBtEnvironment androidBtEnvironment;
    private final Map<BluetoothDevice, Connection> connections;
    private final Set<UUID> serviceUuids;
    private final EventBus eventBus;
    private final BleGattServerCallbackEventPublisher eventPublisher;
    private final Object gattServerLock;
    private final AtomicReference<BluetoothGattServer> bluetoothGattServer;

    private Promise<BluetoothGattServer, Throwable, Object> gattServerPromise;

    public BleGattServer(@NonNull Context context, @NonNull WorkQueue workQueue,
        @NonNull BluetoothManager bluetoothManager,
        @NonNull ConnectionStateMonitor connectionStateMonitor,
        @NonNull AndroidBtEnvironment androidBtEnvironment,
        @NonNull Handler connectivityHandler) {
        this.context = context;
        this.workQueue = workQueue;
        this.bluetoothManager = bluetoothManager;
        this.connectionStateMonitor = connectionStateMonitor;
        this.androidBtEnvironment = androidBtEnvironment;

        connections = Collections.synchronizedMap(
            new HashMap<BluetoothDevice, Connection>());
        serviceUuids = Collections.synchronizedSet(new HashSet<UUID>());
        eventBus = EventBus.getDefault();
        eventPublisher = new BleGattServerCallbackEventPublisher(eventBus, connectivityHandler);
        eventBus.register(this);
        gattServerLock = new Object();
        bluetoothGattServer = new AtomicReference<>();
    }

    /**
     * Creates a new client to use with this BleGattServer.
     *
     * @param clientId String identifying the client. Used mainly for logging.
     * @return Client instance
     */
    public Client createClient(@NonNull String clientId) {
        return new Client(clientId);
    }

    private Promise<BluetoothGattServer, Throwable, Object> ensureGattServer() {
        synchronized (gattServerLock) {
            BluetoothGattServer gattServer = bluetoothGattServer.get();
            if (gattServer != null) {
                return new DeferredObject<BluetoothGattServer, Throwable, Object>()
                    .resolve(gattServer);
            } else if (gattServerPromise != null && gattServerPromise.isPending()) {
                return gattServerPromise;
            }

            gattServerPromise = workQueue.addPromise(new BleGattServerOperationOpen(
                context, bluetoothManager, eventPublisher))
                .then((DonePipe<BluetoothGattServer, BluetoothGattServer, Throwable, Object>)
                    gattServer1 -> {
                        Timber.d("Bluetooth gatt server opened");
                        bluetoothGattServer.set(gattServer1);
                        return new DeferredObject<BluetoothGattServer, Throwable, Object>()
                            .resolve(gattServer1);
                    })
                .fail(result -> {
                    Timber.e(result, "Failed to open bluetooth gatt server");
                });

            return gattServerPromise;
        }
    }

    private void closeServerIfUnused() {
        synchronized (gattServerLock) {
            BluetoothGattServer gattServer = bluetoothGattServer.get();
            if (gattServer != null && connections.isEmpty() && serviceUuids.isEmpty()) {
                Timber.d("Closing BluetoothGattServer");
                workQueue.add(new BleGattServerOperationClose(gattServer));
                bluetoothGattServer.set(null);
            }
        }
    }

    @Subscribe
    public void onConnectionStateChanged(BleGattServerConnectionStateChangedEvent event) {
        synchronized (connections) {
            Connection connection = connections.get(event.getBluetoothDevice());
            if (connection != null) {
                if (connection.isConnected() && event.getNewState() ==
                    BluetoothProfile.STATE_DISCONNECTED) {
                    Timber.d("Connection lost");
                    connection.onDisconnected();
                } else if (!connection.isConnected() && event.getNewState() ==
                    BluetoothProfile.STATE_CONNECTED) {
                    Timber.d("Connection up");
                    connection.onConnected();
                }
            }
        }

        if (event.getNewState() == BluetoothProfile.STATE_DISCONNECTED) {
            closeServerIfUnused();
        }
    }

    /**
     * Class for interacting with BleGattServer instance.
     * <p>
     * Call {@link BleGattServer#createClient(String)} to get a client instance.
     */
    public class Client {
        private final String clientId;
        private Connection connection;
        private ConnectStrategy defaultConnectStrategy;

        private Client(@NonNull String clientId) {
            this.clientId = clientId;
        }

        public void setDefaultConnectStrategy(ConnectStrategy defaultConnectStrategy) {
            this.defaultConnectStrategy = defaultConnectStrategy;
        }

        /**
         * Adds the given service to the GATT server.
         *
         * @param gattService The BluetoothGattService to add
         * @return Promise
         */
        public Promise<Void, Throwable, Object> addService(
            @NonNull final BluetoothGattService gattService) {
            return ensureGattServer()
                .then((DonePipe<BluetoothGattServer, Void, Throwable, Object>) gattServer ->
                    doNext(new BleGattServerOperationAddService(gattServer, gattService)))
                .then((DonePipe<Void, Void, Throwable, Object>) result -> {
                    serviceUuids.add(gattService.getUuid());
                    return new DeferredObject<Void, Throwable, Object>().resolve(null);
                });
        }

        /**
         * Removes the given service from the GATT server.
         *
         * @param gattService The BluetoothGattService to remove
         * @return Promise
         */
        public Promise<Void, Throwable, Object> removeService(
            @NonNull BluetoothGattService gattService) {
            serviceUuids.remove(gattService.getUuid());
            BluetoothGattServer gattServer = bluetoothGattServer.get();
            if (gattServer != null) {
                // Remove the service as soon as possible: put the operation in front of the queue.
                return doNextAddFirst(
                    new BleGattServerOperationRemoveService(gattServer, gattService))
                    .always((state, resolved, rejected) -> closeServerIfUnused());
            } else {
                return new DeferredObject<Void, Throwable, Object>()
                    .reject(new GattServerException("Can not remove service"));
            }
        }

        /**
         * Turn bluetooth discover on for a brief moment to bring pairing dialog to front.
         *
         * @return Discover promise.
         */
        public Promise<Void, Throwable, Object> discover() {
            return doNext(new BluetoothOperationDiscover(context, bluetoothManager.getAdapter()));
        }

        /**
         * Blocks the work queue for the given time.
         *
         * @param delayMs Time to wait in milliseconds
         */
        public Promise<Void, Throwable, Object> delay(long delayMs) {
            return doNext(new QueueOperationDelay(delayMs));
        }

        /**
         * Connects the client to the given bluetooth device.
         * <p>
         * If there is another client with a connection to the same bluetooth device,
         * the same connection is reused by this client.
         *
         * @param bluetoothDevice BluetoothDevice to connect to
         * @param connectStrategy Connect strategy
         * @return Promise
         */
        public Promise<Void, Throwable, Object> connect(
            @NonNull final BluetoothDevice bluetoothDevice,
            @NonNull final ConnectStrategy connectStrategy) {

            synchronized (connections) {
                Timber.d("Connecting client: %s", clientId);

                if (connection != null &&
                    !connection.getBluetoothDevice().equals(bluetoothDevice)) {
                    Timber.e("Already has connection to different device");
                    return new DeferredObject<Void, Throwable, Object>()
                        .reject(new GattServerException("Already connected to different device"));
                }

                if (connections.get(bluetoothDevice) == null) {
                    Timber.d("Creating new connection");
                    connections.put(bluetoothDevice, new Connection(bluetoothDevice));
                } else {
                    Timber.d("Using existing connection");
                }

                final Connection connection = connections.get(bluetoothDevice);
                connection.addClient(this);
                setConnection(connection);
            }

            return ensureGattServer()
                .then((DonePipe<BluetoothGattServer, Void, Throwable, Object>) gattServer -> {
                    Timber.d("Connecting device");
                    synchronized (connections) {
                        return connection.connect(gattServer, connectStrategy);
                    }
                });
        }

        /**
         * Connects the client to the given bluetooth device using the default
         * connect strategy.
         * <p>
         * The default connection strategy must be set with {@link #setDefaultConnectStrategy}
         * before calling this function.
         *
         * @param bluetoothDevice BluetoothDevice to connect to
         * @return Promise
         */
        public Promise<Void, Throwable, Object> connect(
            @NonNull final BluetoothDevice bluetoothDevice) {
            return connect(bluetoothDevice, defaultConnectStrategy);
        }

        /**
         * Disconnects the client from the bluetooth device.
         * <p>
         * If the connection is still used by another client, the connection is not closed.
         *
         * @return Promise
         */
        public Promise<Void, Throwable, Object> disconnect() {
            Promise<Void, Throwable, Object> promise;

            synchronized (connections) {
                Timber.d("Disconnecting client: %s", clientId);
                // Cancel any queued operations initiated by this client.
                workQueue.cancel(this);

                if (connection != null) {
                    connection.removeClient(this);
                    if (!connection.hasClients()) {
                        Timber.d("Connection has no more clients");
                        connections.remove(connection.getBluetoothDevice());
                    }
                }

                // Disconnect even if there are other clients, because connected
                // device is not always aware of the service removal made by the
                // disconnecting client. The remaining client(s) will reconnect
                // when the connection is dropped.
                Timber.d("Disconnecting device");
                BluetoothGattServer gattServer = bluetoothGattServer.get();
                if (connection != null && gattServer != null) {
                    promise = connection.disconnect(gattServer);
                } else {
                    Timber.d("Already disconnected");
                    promise = new DeferredObject<Void, Throwable, Object>()
                        .resolve(null);
                }

                setConnection(null);
            }

            promise.then((DoneCallback<Void>) result -> closeServerIfUnused());

            return promise;
        }

        /**
         * Cancels a possible pending connect operation.
         */
        public void cancelConnect() {
            synchronized (connections) {
                if (connection != null) {
                    connection.cancelConnect();
                }
            }
        }

        /**
         * Sends a characteristic changed notification to the connected device.
         * <p>
         * If the given value is longer than MTU, the notification is split to
         * smaller chunks.
         *
         * @param characteristic Characteristic that has changed
         * @param confirm true to request confirmation from the client (indication),
         * false to send a notification
         * @param value Value for the characteristic. The characteristic is updated
         * with this value when the operation runs. The value must not
         * be modified before the operation completes.
         * @return Promise
         */
        public Promise<Integer, Throwable, Object> notifyCharacteristicChanged(
            @NonNull BluetoothGattCharacteristic characteristic,
            boolean confirm, byte[] value) {
            synchronized (connections) {
                BluetoothGattServer gattServer = bluetoothGattServer.get();
                if (gattServer != null && connection != null && connection.isConnected()) {
                    // Split the notify to smaller chunks if necessary.
                    List<Promise> notifyPromises = new ArrayList<>();
                    for (int i = 0; i < value.length; i += BleCore.DEFAULT_MTU_PACKET_SIZE) {
                        byte[] buf = Arrays.copyOfRange(value,
                            i, Math.min(i + BleCore.DEFAULT_MTU_PACKET_SIZE, value.length));
                        notifyPromises.add(
                            doNext(new BleGattServerOperationNotifyCharacteristicChanged(
                                gattServer, connection.bluetoothDevice,
                                characteristic, confirm, buf)));
                    }

                    // Create a promise that is resolved when all the notify promises are resolved.
                    return new MasterDeferredObject(
                        notifyPromises.toArray(new Promise[notifyPromises.size()]))
                        .then(result -> BluetoothGatt.GATT_SUCCESS,
                            (FailFilter<OneReject, Throwable>) result ->
                                (Throwable) result.getReject());
                } else {
                    Timber.e("Unable to notify, device connection not ready");
                    return new DeferredObject<Integer, Throwable, Object>().reject(
                        new GattServerException("Unable to notify, device connection not ready"));
                }
            }
        }

        /**
         * Sends a response to a read or write request to the connected device.
         *
         * @param requestId The ID of the request
         * @param status Status of the request to be sent to the device
         * @param offset Value offset for partial read/write response
         * @param value The value of the attribute that was read/written (optional).
         * The given value must not be modified before the operation completes.
         */
        public Promise<Integer, Throwable, Object> sendResponse(
            int requestId, int status, int offset, byte[] value) {
            synchronized (connections) {
                BluetoothGattServer gattServer = bluetoothGattServer.get();
                if (gattServer != null && connection != null && connection.isConnected()) {
                    return doNextAddFirst(new BleGattServerOperationSendResponse(gattServer,
                        connection.bluetoothDevice, requestId, status, offset, value));
                } else {
                    return new DeferredObject<Integer, Throwable, Object>().reject(
                        new GattServerException("Can not send response"));
                }
            }
        }

        public String getClientId() {
            return clientId;
        }

        public BluetoothDevice getBluetoothDevice() {
            return connection != null ? connection.getBluetoothDevice() : null;
        }

        protected Connection getConnection() {
            return connection;
        }

        private void setConnection(Connection connection) {
            this.connection = connection;
        }

        private <T> Promise<T, Throwable, Object> doNext(QueueOperation<T> operation) {
            operation.setTag(this);
            return workQueue.addPromise(operation);
        }

        private <T> Promise<T, Throwable, Object> doNextAddFirst(QueueOperation<T> operation) {
            operation.setTag(this);
            Promise<T, Throwable, Object> promise = operation.toPromise();
            workQueue.addFirst(operation);
            return promise;
        }
    }

    /**
     * Internal class representing a connection between the GATT server and a bluetooth device.
     * <p>
     * Multiple clients may share a single connection.
     */
    protected class Connection {
        private final BluetoothDevice bluetoothDevice;
        private boolean connected;
        private Set<Client> clients;
        private Promise<Void, Throwable, Object> connectPromise;
        private Promise<Void, Throwable, Object> disconnectPromise;
        private QueueOperation<Void> connectOperation;

        public Connection(BluetoothDevice bluetoothDevice) {
            this.bluetoothDevice = bluetoothDevice;
            this.clients = new HashSet<>();
        }

        public Promise<Void, Throwable, Object> connect(
            final BluetoothGattServer gattServer, final ConnectStrategy connectStrategy) {

            if (connected) {
                Timber.d("Already connected");
                return new DeferredObject<Void, Throwable, Object>()
                    .resolve(null);
            } else if (connectPromise != null && connectPromise.isPending()) {
                Timber.d("Already connecting");
                return connectPromise;
            }

            connectPromise = discoverIfInitialConnect(connectStrategy)
                .then(new DonePipe<Void, Void, Throwable, Object>() {
                    @Override
                    public Promise<Void, Throwable, Object> pipeDone(Void result) {
                        return connectStrategy.connect(bluetoothDevice,
                            new ConnectStrategy.ConnectDelegate() {
                                @Override
                                public Promise<Void, Throwable, Object> connect(
                                    boolean autoConnect, boolean discover, long timeout) {
                                    if (discover) {
                                        connectOperation =
                                            new BleGattServerOperationConnectAndDiscover(
                                                gattServer, bluetoothDevice, autoConnect,
                                                bluetoothManager.getAdapter(),
                                                androidBtEnvironment
                                                    .discoveryStartTimeAfterConnect(),
                                                androidBtEnvironment.maxDiscoveryTimeInConnect());
                                    } else {
                                        connectOperation = new BleGattServerOperationConnect(
                                            gattServer, bluetoothDevice, autoConnect);
                                    }
                                    connectOperation.setTimeout(timeout);
                                    connectOperation.setTag(this);

                                    return workQueue.addPromise(connectOperation);
                                }
                            });
                    }
                });

            return connectPromise;
        }

        private Promise<Void, Throwable, Object> discoverIfInitialConnect(ConnectStrategy connectStrategy) {
            Boolean initialConnect = connectStrategy.isInitialConnect();
            if (initialConnect == null ||
                !initialConnect) {
                return new DeferredObject<Void, Throwable, Object>().resolve(null);
            }
            QueueOperation<Void> discoverOperation = new BluetoothOperationDiscover(
                context, bluetoothManager.getAdapter());
            discoverOperation.setTag(this);
            return workQueue.addPromise(discoverOperation);
        }

        private void onConnected() {
            connected = true;
        }

        public Promise<Void, Throwable, Object> disconnect(BluetoothGattServer gattServer) {
            cancelConnect();

            if (disconnectPromise != null && disconnectPromise.isPending()) {
                Timber.d("Already disconnecting");
                return disconnectPromise;
            }

            disconnectPromise = workQueue.addPromise(new BleGattServerOperationDisconnect(
                gattServer, bluetoothDevice, connectionStateMonitor));

            onDisconnected();

            return disconnectPromise;
        }

        public void cancelConnect() {
            if (connectPromise != null && connectPromise.isPending() &&
                connectOperation != null) {
                Timber.d("Cancelling previous connect");
                connectOperation.cancel();
            }
            connectPromise = null;
        }

        private void onDisconnected() {
            connected = false;
            workQueue.cancel(this);
        }

        public boolean isConnected() {
            return connected;
        }

        public boolean isConnecting() {
            return connectPromise != null && connectPromise.isPending();
        }

        public BluetoothDevice getBluetoothDevice() {
            return bluetoothDevice;
        }

        public void addClient(Client client) {
            clients.add(client);
        }

        public void removeClient(Client client) {
            clients.remove(client);
        }

        public boolean hasClients() {
            return !clients.isEmpty();
        }
    }
}
