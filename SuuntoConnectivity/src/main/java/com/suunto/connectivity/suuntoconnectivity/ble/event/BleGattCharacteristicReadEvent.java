package com.suunto.connectivity.suuntoconnectivity.ble.event;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import androidx.annotation.NonNull;

public class BleGattCharacteristicReadEvent extends BleGattEvent {

    private final BluetoothGattCharacteristic characteristic;

    @NonNull
    private final byte[] value;

    public BleGattCharacteristicReadEvent(
        BluetoothGatt bluetoothGatt,
        BluetoothGattCharacteristic characteristic,
        @NonNull byte[] value,
        int status) {
        super(bluetoothGatt, status);

        this.characteristic = characteristic;
        this.value = value;
    }

    public BluetoothGattCharacteristic getCharacteristic() {
        return characteristic;
    }

    @NonNull
    public byte[] getValue() {
        return value;
    }
}
