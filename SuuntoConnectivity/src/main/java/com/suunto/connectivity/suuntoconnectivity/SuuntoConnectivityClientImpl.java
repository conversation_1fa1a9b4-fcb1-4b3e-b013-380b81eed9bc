package com.suunto.connectivity.suuntoconnectivity;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.suuntoconnectivity.ancs.messenger.AncsCommand;
import com.suunto.connectivity.suuntoconnectivity.ancs.messenger.AncsReply;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.android.schedulers.AndroidSchedulers;
import rx.subjects.BehaviorSubject;
import timber.log.Timber;

public class SuuntoConnectivityClientImpl
    implements SuuntoConnectivityListener, SuuntoConnectivityClient {

    private final Context context;
    private final SuuntoConnectivityServiceConnection connection;
    private final Handler listenerHandler;
    private final BehaviorSubject<Boolean> serviceStateSubject;
    private final AtomicReference<SuuntoConnectivity> suuntoConnectivity;
    private final Looper connectivityCallLooper;

    private SuuntoConnectivityListener ngListener;
    private SuuntoConnectivityListener legacyListener;
    private final HandlerThread listenerHandlerThread;

    public SuuntoConnectivityClientImpl(@NonNull Context context) {
        this.context = context;

        listenerHandlerThread =
            new HandlerThread("SuuntoConnectivityListenerHandler");
        listenerHandlerThread.start();
        listenerHandler = new Handler(listenerHandlerThread.getLooper());
        connectivityCallLooper = SuuntoConnectivityService.connectivityHandler().getLooper();
        serviceStateSubject = BehaviorSubject.create(false);
        suuntoConnectivity = new AtomicReference<>();

        connection = new SuuntoConnectivityServiceConnection();
        context.bindService(new Intent(context, SuuntoConnectivityService.class),
            connection, Context.BIND_AUTO_CREATE);
    }

    @Override
    public void onDestroy() {
        // Unregister us as a client
        SuuntoConnectivity connectivity = suuntoConnectivity.get();
        if (connectivity != null) {
            connectivity.unregisterClient(this);
        } else {
            Timber.e("Failed to unregister client");
        }
        listenerHandlerThread.quit();
        context.unbindService(connection);
    }

    private <T> Single.Transformer<T, T> applySchedulers() {
        return observable -> observable.subscribeOn(AndroidSchedulers.from(connectivityCallLooper));
    }
    @Override
    public void setNgListener(SuuntoConnectivityListener listener) {
        this.ngListener = listener;
    }

    @Override
    public void setLegacyListener(SuuntoConnectivityListener listener) {
        this.legacyListener = listener;
    }

    @Override
    public Single<AncsReply> ancsCommand(AncsCommand command) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.ancsCommand(command)
                .compose(applySchedulers()) :
            Single.error(new SuuntoConnectivityException("No service!"));
    }

    /**
     * Waits until suunto connectivity service connection is done. Can only be used once.
     *
     * @return Connection state observable.
     */
    @Override
    public Observable<Boolean> serviceStateObservable() {
        return serviceStateSubject.asObservable();
    }

    @Override
    public List<DeviceHandle> getHandles() {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ? service.getHandles() : Collections.emptyList();
    }

    @Override
    public boolean isConnected(@NonNull DeviceHandle handle) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null && service.isConnected(handle);
    }

    @Override
    public String getDeviceAddress(@NonNull DeviceHandle handle) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ? service.getDeviceAddress(handle) : null;
    }

    @Override
    public String getDeviceName(@NonNull DeviceHandle handle) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ? service.getDeviceName(handle) : null;
    }

    @Override
    public DeviceHandle getDeviceHandleFromAddress(@NonNull String deviceAddress) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ? service.getDeviceHandleFromAddress(deviceAddress) : null;
    }

    @Override
    public Single<Integer> connect(
        @NonNull String deviceAddress,
        SuuntoDeviceType deviceType,
        @NonNull ConnectMetadata connectMetadata) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.connect(deviceAddress, deviceType, connectMetadata)
                .compose(applySchedulers()) :
            Single.error(new SuuntoConnectivityException("No service!"));
    }

    @Override
    public Single<Integer> disconnect(@NonNull String deviceAddress) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.disconnect(deviceAddress)
                .compose(applySchedulers()) :
            Single.just(BleCore.BLE_ERR_UNKNOWN_ERROR);
    }

    @Override
    public Completable destroyBleDevice(@NonNull String deviceAddress) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.destroyBleDevice(deviceAddress)
                .subscribeOn(AndroidSchedulers.from(connectivityCallLooper)) :
            Completable.error(new SuuntoConnectivityException("No service!"));
    }

    @Override
    public Single<Integer> startDataNotify(@NonNull DeviceHandle handle) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.startDataNotify(handle)
                .compose(applySchedulers()) :
            Single.error(new SuuntoConnectivityException("No service!"));
    }

    @Override
    public Single<Integer> dataWrite(@NonNull DeviceHandle handle, @NonNull byte[] data) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ?
            service.dataWrite(handle, data)
                .compose(applySchedulers()) :
            Single.just(BleCore.BLE_ERR_UNKNOWN_ERROR);
    }

    @Override
    public @Nullable byte[] getData(@NonNull DeviceHandle handle) {
        SuuntoConnectivity service = suuntoConnectivity.get();
        return service != null ? service.getData(handle) : null;
    }

    @Override
    public void onDeviceLostSpontaneously(DeviceHandle handle) {
        final SuuntoConnectivityListener listener = getListener(handle);
        if (listener != null) {
            listenerHandler.post(() -> listener.onDeviceLostSpontaneously(handle));
        }
    }

    @Override
    public void onDeviceLostAsPerCommand(DeviceHandle handle) {
        final SuuntoConnectivityListener listener = getListener(handle);
        if (listener != null) {
            listenerHandler.post(() -> listener.onDeviceLostAsPerCommand(handle));
        }
    }

    @Override
    public void onDataAvailable(DeviceHandle handle) {
        final SuuntoConnectivityListener listener = getListener(handle);
        if (listener != null) {
            listenerHandler.post(() -> listener.onDataAvailable(handle));
        }
    }

    @Override
    public void onDeviceFound(DeviceHandle handle) {
        final SuuntoConnectivityListener listener = getListener(handle);
        if (listener != null) {
            listenerHandler.post(() -> listener.onDeviceFound(handle));
        }
    }

    private SuuntoConnectivityListener getListener(DeviceHandle handle) {
        if (handle != null) {
            return handle.isNg() ? ngListener : legacyListener;
        } else {
            return null;
        }
    }

    private class SuuntoConnectivityServiceConnection implements ServiceConnection {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Timber.d("onServiceConnected");

            // Get the service interface
            SuuntoConnectivity connectivity =
                ((SuuntoConnectivityService.SuuntoConnectivityServiceBinder) service).getService();
            suuntoConnectivity.set(connectivity);

            // Register us
            connectivity.registerClient(SuuntoConnectivityClientImpl.this);

            serviceStateSubject.onNext(true);
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Timber.d("onServiceDisconnected");
            suuntoConnectivity.set(null);
            serviceStateSubject.onNext(false);
        }
    }
}
