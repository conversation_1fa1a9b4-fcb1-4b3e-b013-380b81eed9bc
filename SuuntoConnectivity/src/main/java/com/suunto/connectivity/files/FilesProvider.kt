package com.suunto.connectivity.files

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_DELETE_FILE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_FILE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_FILE_LIST
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_LOG_FILES
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_PUT_FILE
import com.suunto.connectivity.repository.commands.DeleteFileQuery
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.EmptyResponse
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.GetFileListQuery
import com.suunto.connectivity.repository.commands.GetFileListResponse
import com.suunto.connectivity.repository.commands.GetFileQuery
import com.suunto.connectivity.repository.commands.GetLogFilesQuery
import com.suunto.connectivity.repository.commands.GetLogFilesResponse
import com.suunto.connectivity.repository.commands.PutFileQuery
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.util.handleDeviceSpecificQuery
import com.suunto.connectivity.watch.WatchBt
import rx.Observable
import rx.Single

class FilesProvider(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean =
        messageType in listOf(
            MSG_GET_FILE_LIST,
            MSG_GET_FILE,
            MSG_PUT_FILE,
            MSG_DELETE_FILE,
            MSG_GET_LOG_FILES,
        )

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> =
        when (messageType) {
            MSG_GET_FILE_LIST -> handleQuery<GetFileListQuery>(bundle) { watchBt, query ->
                watchBt.getFileList(query.path)
                    .map { mdsResponse ->
                        GetFileListResponse(
                            files = mdsResponse.files,
                            dirs = mdsResponse.dirs
                        )
                    }
            }

            MSG_GET_FILE -> handleQuery<GetFileQuery>(bundle) { watchBt, query ->
                watchBt.getFile(query.deviceFilePath, query.localFilePath)
                    .andThen(Single.just(EmptyResponse()))
            }

            MSG_PUT_FILE -> handleQuery<PutFileQuery>(bundle) { watchBt, query ->
                watchBt.putFile(query.localFilePath, query.deviceFilePath)
                    .andThen(Single.just(EmptyResponse()))
            }

            MSG_DELETE_FILE -> handleQuery<DeleteFileQuery>(bundle) { watchBt, query ->
                watchBt.deleteFile(query.deviceFilePath)
                    .andThen(Single.just(EmptyResponse()))
            }

            MSG_GET_LOG_FILES -> handleQuery<GetLogFilesQuery>(bundle) { watchBt, _ ->
                watchBt.logFiles.map { mdsResponse ->
                    GetLogFilesResponse(mdsResponse.list.map { it.name })
                }
            }

            else -> Observable.just(ErrorResponse("Unknown query"))
        }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Single<Response>
    ): Observable<Response> =
        handleDeviceSpecificQuery(suuntoRepositoryService.activeDevices, bundle, handler)
}
