package com.suunto.connectivity.mediacontrols.domain

import android.content.Context
import android.media.MediaMetadata
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaControllerCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow

class MediaControllerWrapper(
    context: Context,
    token: MediaSessionCompat.Token,
) {
    private val _mediaPlayerState = MutableSharedFlow<PlayerState>(
        replay = 1,
        extraBufferCapacity = 2,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    ).apply { tryEmit(PlayerState.Idle(null)) }
    val mediaPlayerState: SharedFlow<PlayerState> = _mediaPlayerState.asSharedFlow()

    private val _mediaMetadata = MutableStateFlow<MediaMetadata?>(null)
    val mediaMetadata: StateFlow<MediaMetadata?> = _mediaMetadata.asStateFlow()

    private val controllerCallback = object : MediaControllerCompat.Callback() {
        override fun onPlaybackStateChanged(state: PlaybackStateCompat?) {
            state?.toPlayerState()
                ?.let(_mediaPlayerState::tryEmit)
        }

        private fun PlaybackStateCompat.toPlayerState(): PlayerState = PlayerState.Active(
            packageName = controller.packageName,
            state = state,
            speed = playbackSpeed,
            positionInMillis = position,
        )

        override fun onMetadataChanged(metadata: MediaMetadataCompat?) {
            (metadata?.mediaMetadata as? MediaMetadata)
                ?.let { _mediaMetadata.value = it }
        }

        override fun onSessionReady() {
            val playbackState = controller.playbackState ?: return
            val mediaMetadata = (controller.metadata?.mediaMetadata as? MediaMetadata) ?: return
            _mediaPlayerState.tryEmit(playbackState.toPlayerState())
            _mediaMetadata.value = mediaMetadata
        }

        override fun onSessionDestroyed() {
            _mediaPlayerState.tryEmit(PlayerState.Idle(controller.packageName))
            _mediaMetadata.value = null
        }
    }
    val controller: MediaControllerCompat = MediaControllerCompat(context, token).apply {
        registerCallback(controllerCallback)
    }

    fun onDestroy() {
        controller.unregisterCallback(controllerCallback)
    }

    sealed class PlayerState(
        open val packageName: String?,
    ) {
        data class Idle(
            override val packageName: String?,
        ) : PlayerState(packageName)

        data class Active(
            override val packageName: String?,
            val state: Int,
            val speed: Float,
            val positionInMillis: Long,
        ) : PlayerState(packageName)
    }
}
