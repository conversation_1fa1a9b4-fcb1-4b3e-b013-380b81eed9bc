package com.suunto.connectivity.trenddata

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.movesense.mds.MdsResponse
import com.squareup.moshi.Types
import com.stt.android.moshi.MoshiProvider
import com.stt.android.testutils.CoroutinesTestRule
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource
import com.suunto.connectivity.STATUS_CONTINUE
import com.suunto.connectivity.STATUS_NEXT_PAGE
import com.suunto.connectivity.STATUS_NO_CONTENT
import com.suunto.connectivity.STATUS_OK
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.watch.SpartanBt
import com.suunto.connectivity.watch.SpartanSyncResult
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.stub
import rx.Single
import kotlin.random.Random

@RunWith(MockitoJUnitRunner::class)
class TrendDataResourceTest {
    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @JvmField
    @Rule
    val archTaskExecutorRule = InstantTaskExecutorRule()

    @Mock
    private lateinit var spartanBt: SpartanBt

    @Mock
    private lateinit var suuntoBtDevice: SuuntoBtDevice

    @Mock
    private lateinit var synchronizerStorage: SynchronizerStorage

    @Mock
    private lateinit var timelineResourceLocalDataSource: TimelineResourceLocalDataSource

    @Mock
    private lateinit var weChatTimelineResourceLocalDataSource: WeChatTimelineResourceLocalDataSource

    private val moshi = MoshiProvider.instance

    private val adapter = moshi.adapter<List<SuuntoTrendDataEntry>>(
        Types.newParameterizedType(
            List::class.java,
            SuuntoTrendDataEntry::class.java
        )
    )

    private lateinit var resource: TrendDataResource

    @Before
    fun setup() {
        `when`(suuntoBtDevice.macAddress).thenReturn("11:22:33")
        `when`(spartanBt.suuntoBtDevice).thenReturn(suuntoBtDevice)
        `when`(spartanBt.serial).thenReturn("123")
        timelineResourceLocalDataSource.stub {
            onBlocking {
                storeTrendDataEntries(any())
            }.doReturn(true)
        }
        weChatTimelineResourceLocalDataSource.stub {
            onBlocking {
                storeTrendDataEntries(any())
            }.then {}
        }
        resource = TrendDataResource(
            spartanBt,
            synchronizerStorage,
            moshi,
            timelineResourceLocalDataSource,
            weChatTimelineResourceLocalDataSource
        )
    }

    @Test
    fun `should fetch next page of data when getting 202 response code`() = runTest {
        val (r1, r2) = generateRandomTrendDataResponses(20, 2)
        `when`(spartanBt.fetchTrendData(anyLong())).thenReturn(
            Single.just(
                MdsResponse(
                    202,
                    adapter.toJson(r1)
                )
            ),
            Single.just(
                MdsResponse(
                    STATUS_OK,
                    adapter.toJson(r2)
                )
            )
        )
        val builder = SpartanSyncResult.builder()

        resource.sync("123", builder)
            .await()

        verify(spartanBt, times(2)).fetchTrendData(anyLong())
    }

    @Test
    fun `should not fetch next page of data when getting 200 response code`() = runTest {
        val (r1) = generateRandomTrendDataResponses(10, 1)
        `when`(spartanBt.fetchTrendData(anyLong())).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_OK,
                    adapter.toJson(r1)
                )
            )
        )
        val builder = SpartanSyncResult.builder()

        resource.sync("123", builder)
            .await()

        verify(spartanBt, times(1)).fetchTrendData(anyLong())
    }

    @Test
    fun `should not fail if last entry timestamp is older than previous page`() = runTest {
        val (r1, r2, r3) = generateRandomTrendDataResponses(60, 3)
        val stamp1 = 0L
        val stamp2 = r1.last().timestamp
        val stamp3 = r2.last().timestamp
        Mockito.lenient().`when`(synchronizerStorage.getLastTrendDataTimestamp(anyString()))
            .thenReturn(stamp1, stamp2, stamp3)
        `when`(spartanBt.fetchTrendData(stamp1)).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_NEXT_PAGE,
                    adapter.toJson(r1)
                )
            )
        )
        `when`(spartanBt.fetchTrendData(stamp2)).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_NEXT_PAGE,
                    adapter.toJson(r2)
                )
            )
        )
        `when`(spartanBt.fetchTrendData(stamp3)).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_OK,
                    adapter.toJson(r3)
                )
            )
        )
        val builder = SpartanSyncResult.builder()
            .syncStartTimestamp(1L)
            .syncEndTimestamp(1L)

        resource.sync("123", builder)
            .await()

        verify(spartanBt, times(3)).fetchTrendData(anyLong())
        assertThat(builder.build().trendDataResult.syncResult.isSuccess()).isTrue()
    }

    @Test
    fun `handle no content status`() = runTest {
        val (r1) = generateRandomTrendDataResponses(0, 1)
        `when`(spartanBt.fetchTrendData(anyLong())).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_NO_CONTENT,
                    adapter.toJson(r1)
                )
            )
        )

        val builder = SpartanSyncResult.builder()
            .syncStartTimestamp(1L)
            .syncEndTimestamp(1L)

        resource.sync("123", builder)
            .await()

        verify(spartanBt, times(1)).fetchTrendData(anyLong())
        assertThat(builder.build().trendDataResult.syncResult.isSuccess()).isTrue()
    }

    @Test
    fun `handle continue status`() = runTest {
        val (r1) = generateRandomTrendDataResponses(0, 1)
        `when`(spartanBt.fetchTrendData(anyLong())).thenReturn(
            Single.just(
                MdsResponse(
                    STATUS_CONTINUE,
                    adapter.toJson(r1)
                )
            ),
            Single.just(
                MdsResponse(
                    STATUS_OK,
                    adapter.toJson(r1)
                )
            )
        )

        val builder = SpartanSyncResult.builder()
            .syncStartTimestamp(1L)
            .syncEndTimestamp(1L)

        resource.sync("123", builder)
            .await()

        verify(spartanBt, times(2)).fetchTrendData(anyLong())
        assertThat(builder.build().trendDataResult.syncResult.isSuccess()).isTrue()
    }

    companion object {
        private const val MIN_STEPS = 0
        private const val MAX_STEPS = 600

        private const val MIN_ENERGY = 0f
        private const val MAX_ENERGY = 150000f

        private const val MIN_HR = 50f
        private const val MAX_HR = 180f

        private const val MIN_SPO2 = 90f
        private const val MAX_SPO2 = 100f

        private const val MIN_ALTITUDE = -500f
        private const val MAX_ALTITUDE = 8848f

        private const val MIN_HRV = 10
        private const val MAX_HRV = 200

        private const val TIMESTAMP_START = 1599112800000L
        private const val TIMESTAMP_INCREMENT = 600000L

        private fun generateRandomEntry(timestamp: Long): SuuntoTrendDataEntry {
            return SuuntoTrendDataEntry(
                serial = "SN${Random.nextInt(100000, 999999)}",
                energy = Random.nextFloat() * (MAX_ENERGY - MIN_ENERGY) + MIN_ENERGY,
                steps = Random.nextInt(MIN_STEPS, MAX_STEPS),
                hr = Random.nextFloat() * (MAX_HR - MIN_HR) + MIN_HR,
                hrMin = Random.nextFloat() * (MAX_HR - MIN_HR) + MIN_HR,
                hrMax = Random.nextFloat() * (MAX_HR - MIN_HR) + MIN_HR,
                spo2 = Random.nextFloat() * (MAX_SPO2 - MIN_SPO2) + MIN_SPO2,
                altitude = Random.nextFloat() * (MAX_ALTITUDE - MIN_ALTITUDE) + MIN_ALTITUDE,
                hrv = Random.nextInt(MIN_HRV, MAX_HRV),
                timestamp = timestamp,
                variantName = "Device-${Random.nextInt(100, 999)}"
            )
        }

        // Generates a reversed list of random SuuntoTrendDataEntry items. Reversed since ESW
        // does not guarantee chronological order.
        private fun generateRandomEntries(count: Int): List<SuuntoTrendDataEntry> {
            var currentTimestamp = TIMESTAMP_START
            return List(count) {
                val entry = generateRandomEntry(currentTimestamp)
                currentTimestamp += TIMESTAMP_INCREMENT
                entry
            }.reversed()
        }

        private fun <T> splitListIntoParts(list: List<T>, numberOfParts: Int): List<List<T>> {
            require(numberOfParts > 0) { "Number of parts must be greater than 0" }

            val partSize = list.size / numberOfParts
            val remainder = list.size % numberOfParts
            val parts = mutableListOf<List<T>>()

            var startIndex = 0
            for (i in 0 until numberOfParts) {
                // Calculate the size for this part
                val currentPartSize = partSize + if (i < remainder) 1 else 0
                val endIndex = startIndex + currentPartSize
                parts.add(list.subList(startIndex, endIndex))
                startIndex = endIndex
            }

            return parts
        }

        private fun generateRandomTrendDataResponses(
            totalEntryCount: Int,
            responseCount: Int
        ): List<List<SuuntoTrendDataEntry>> {
            val entries = generateRandomEntries(totalEntryCount)
            return splitListIntoParts(entries, responseCount)
        }
    }
}
