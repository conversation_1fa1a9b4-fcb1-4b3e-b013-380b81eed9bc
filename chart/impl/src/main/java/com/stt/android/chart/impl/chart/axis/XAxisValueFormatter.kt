package com.stt.android.chart.impl.chart.axis

import android.content.Context
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.firstDayOfEpochMonth
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.toEpochMilli
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import com.stt.android.R as BaseR

fun createXAxisValueFormatter(
    context: Context,
    chartGranularity: ChartGranularity,
    chartData: ChartData? = null
): CartesianValueFormatter = when (chartGranularity) {
    ChartGranularity.DAILY -> HourMinuteValueFormatter()
    ChartGranularity.WEEKLY -> WeekdayValueFormatter()
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.MONTHLY,
    ChartGranularity.THIRTY_DAYS -> DateValueFormatter()

    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS,
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> MonthDateValueFormatter(context)

    ChartGranularity.SIX_MONTHS -> ShortMonthValueFormatter(context)
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> YearlyMonthDayValueFormatter(context)
    ChartGranularity.YEARLY -> ShortestMonthValueFormatter(context)
    ChartGranularity.EIGHT_YEARS -> YearValueFormatter()
}

fun createXAxisValueFormatterForVo2Max(
    context: Context,
    chartGranularity: ChartGranularity,
): CartesianValueFormatter = when (chartGranularity) {
    ChartGranularity.DAILY -> HourMinuteValueFormatter()
    ChartGranularity.WEEKLY -> WeekdayValueFormatter()
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.MONTHLY,
    ChartGranularity.THIRTY_DAYS -> DateValueFormatter()

    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS,
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
    ChartGranularity.SIX_MONTHS,
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
    ChartGranularity.YEARLY -> MonthDateValueFormatter(context)

    ChartGranularity.EIGHT_YEARS -> YearValueFormatter()
}

private class HourMinuteValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence = buildString {
        val ranges = context.ranges
        val step = ranges.xStep
        val spacing = 36.0 * step
        val firstLabelThreshold = spacing * 0.9
        val displayValue = if (value - ranges.minX in 0.0..firstLabelThreshold) ranges.minX else value

        val minutesSinceEpoch = displayValue.roundToLong()
        val localDateTime = LocalDateTime.ofEpochSecond(minutesSinceEpoch * 60L, 0, OffsetDateTime.now().offset)
        val hours = localDateTime.hour
        val minutes = localDateTime.minute
        if (hours < 10 && hours > 0) {
            append('0')
        }
        append(hours)
        append(':')
        if (minutes < 10) {
            append('0')
        }
        append(minutes)
    }
}

private class WeekdayValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        return LocalDate.ofEpochDay(epochDay)
            .format(DateTimeFormatter.ofPattern("E"))
    }
}

private class DateValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        return LocalDate.ofEpochDay(epochDay)
            .format(DateTimeFormatter.ofPattern("M/d"))
    }
}

private class MonthDateValueFormatter(private val context: Context) : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val millisSinceEpoch = LocalDate.ofEpochDay(epochDay)
            .atStartOfDay()
            .toEpochMilli()
        return TextFormatter.formatDate(this.context, millisSinceEpoch, false)
    }
}

private class ShortMonthValueFormatter(private val context: Context) : CartesianValueFormatter {

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val month = LocalDate.ofEpochDay(epochDay).monthValue
        return this.context.resources.getStringArray(BaseR.array.abbreviated_months)[month - 1]
    }
}

private class YearlyMonthDayValueFormatter(val context: Context) : CartesianValueFormatter {

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val millisSinceEpoch = LocalDate.ofEpochDay(epochDay)
            .atStartOfDay()
            .toEpochMilli()
        return TextFormatter.formatDate(this.context, millisSinceEpoch, true)
    }
}

private class ShortestMonthValueFormatter(private val context: Context) : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochMonth = value.roundToInt()
        val month = firstDayOfEpochMonth(epochMonth).monthValue
        return this.context.resources.getStringArray(BaseR.array.abbreviated_months_shortest)[month - 1]
    }
}

private class YearValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val year = value.roundToInt()
        return year.toString()
    }
}

private class CustomMonthValueFormatter(
    private val context: Context,
    private val chartData: ChartData
) : CartesianValueFormatter {
    private var previousMonth: Int = -1
    private var firstMonthValue: Int? = null
    private var shouldHideFirstMonth: Boolean = false

    init {
        calculateMonthSpanFromChartData()
    }

    private fun calculateMonthSpanFromChartData() {
        val firstSeries = chartData.series.firstOrNull() ?: return
        val axisRange = firstSeries.axisRange

        val minX = axisRange.minX
        val maxX = axisRange.maxX

        val minEpochDay = minX.roundToLong()
        val maxEpochDay = maxX.roundToLong()

        val startDate = LocalDate.ofEpochDay(minEpochDay)
        val endDate = LocalDate.ofEpochDay(maxEpochDay)

        firstMonthValue = startDate.monthValue

        val monthSpan = if (startDate.year == endDate.year) {
            endDate.monthValue - startDate.monthValue + 1
        } else {
            val monthsInStartYear = 12 - startDate.monthValue + 1
            val monthsInEndYear = endDate.monthValue
            val fullYears = endDate.year - startDate.year - 1
            monthsInStartYear + monthsInEndYear + (fullYears * 12)
        }

        shouldHideFirstMonth = monthSpan > 6
    }

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val month = LocalDate.ofEpochDay(epochDay).monthValue

        if (shouldHideFirstMonth && month == firstMonthValue) {
            return "\u200B"
        }

        return if (month != previousMonth) {
            previousMonth = month
            this.context.resources.getStringArray(BaseR.array.abbreviated_months)[month - 1]
        } else {
            " "
        }
    }
}
