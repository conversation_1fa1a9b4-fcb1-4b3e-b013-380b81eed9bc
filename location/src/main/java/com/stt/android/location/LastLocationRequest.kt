package com.stt.android.location

data class LastLocationRequest internal constructor(
    @get:JvmName("skipPassiveProvider")
    val skipPassiveProvider: Boolean,
    @get:JvmName("timeInMilliSecondsSinceEpoch")
    val timeInMilliSecondsSinceEpoch: Long
) {

    class Builder internal constructor(
        private var skipPassiveProvider: Boolean = false,
        private var timeInMilliSecondsSinceEpoch: Long = 0L
    ) {
        fun skipPassiveProvider(skipPassiveProvider: Boolean): Builder =
            apply { this.skipPassiveProvider = skipPassiveProvider }

        fun timeInMilliSecondsSinceEpoch(timeInMilliSecondsSinceEpoch: Long): Builder =
            apply { this.timeInMilliSecondsSinceEpoch = timeInMilliSecondsSinceEpoch }

        fun build(): LastLocationRequest =
            LastLocationRequest(
                skipPassiveProvider = skipPassiveProvider,
                timeInMilliSecondsSinceEpoch = timeInMilliSecondsSinceEpoch
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
