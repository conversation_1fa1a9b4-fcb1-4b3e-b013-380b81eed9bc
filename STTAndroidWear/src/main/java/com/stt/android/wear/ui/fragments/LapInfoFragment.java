package com.stt.android.wear.ui.fragments;

import android.content.res.Resources;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.ResultCallback;
import com.google.android.gms.common.data.FreezableUtils;
import com.google.android.gms.wearable.DataApi;
import com.google.android.gms.wearable.DataEvent;
import com.google.android.gms.wearable.DataEventBuffer;
import com.google.android.gms.wearable.DataItem;
import com.google.android.gms.wearable.DataItemBuffer;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.DataMapItem;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.android.gms.wearable.Wearable;
import com.stt.android.core.R;
import com.stt.android.core.bridge.Encoder;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.core.bridge.WearHelper;
import com.stt.android.core.domain.MeasurementUnit;
import com.stt.android.core.ui.utils.TextFormatter;
import com.stt.android.databinding.FragmentLapInfoBinding;
import com.stt.android.wear.ui.utils.AmbientModeHelper;
import java.util.ArrayList;

public class LapInfoFragment extends BaseOngoingWorkoutStateFragment
    implements GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener,
    ResultCallback<DataItemBuffer>, DataApi.DataListener, View.OnClickListener {
    private static final Handler HANDLER = new Handler(Looper.getMainLooper());

    private FragmentLapInfoBinding binding;
    private GoogleApiClient googleApiClient;
    private byte recordingState = -1;
    private final Runnable recordingStateUpdatedRunnable = this::refreshUi;
    private long currentLapDuration;
    private int normalTextColor;
    private int ambientModeTextColor;

    public static LapInfoFragment newInstance() {
        return new LapInfoFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        googleApiClient =
            new GoogleApiClient.Builder(getActivity(), this, this).addApi(Wearable.API).build();
        googleApiClient.connect();

        Resources resources = getResources();
        normalTextColor = resources.getColor(R.color.lap);
        ambientModeTextColor = resources.getColor(R.color.value);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = FragmentLapInfoBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        binding.lapButton.setOnClickListener(this);
    }

    @Override
    public void onStart() {
        super.onStart();

        if (googleApiClient.isConnected()) {
            checkWorkoutStatus();
        }
    }

    private void checkWorkoutStatus() {
        Wearable.DataApi.getDataItems(googleApiClient).setResultCallback(this);
    }

    @Override
    public void onDestroy() {
        if (googleApiClient.isConnected()) {
            Wearable.DataApi.removeListener(googleApiClient, this);
        }
        googleApiClient.disconnect();
        googleApiClient = null;

        super.onDestroy();
    }

    @Override
    public void onWorkoutStateChanged(DataMap workoutState, boolean ambientModeOn) {
        if (binding == null) {
            return;
        }
        AmbientModeHelper.updateTextViewPaint(binding.lapDistance, ambientModeOn);
        AmbientModeHelper.updateTextViewPaint(binding.lapDuration, ambientModeOn);

        double lapDistance = 0.0;
        int lapDistanceUnit = 0;
        if (workoutState != null) {
            byte[] snapshot = workoutState.getByteArray(WearConstants.SNAPSHOT);
            if (snapshot != null) {
                lapDistance = Encoder.decodeLapDistanceFromSnapshot(snapshot);
                currentLapDuration = Encoder.decodeLapDurationFromSnapshot(snapshot);

                MeasurementUnit measurementUnit =
                    Encoder.decodeMeasurementUnitFromSnapshot(snapshot)
                        == WearConstants.MEASUREMENT_UNIT_METRIC ? MeasurementUnit.METRIC
                        : MeasurementUnit.IMPERIAL;
                lapDistanceUnit = measurementUnit.distanceUnitResId;
            }
        }

        binding.lapDistance.setText(TextFormatter.formatDistance(lapDistance));
        binding.lapDistanceUnit.setText(lapDistanceUnit);

        binding.lapDuration.setText(
            ambientModeOn ? TextFormatter.formatElapsedTime(currentLapDuration, true, false)
                : TextFormatter.formatElapsedTime(currentLapDuration));

        if (ambientModeOn) {
            binding.lapDistance.setTextColor(ambientModeTextColor);
            binding.lapDuration.setTextColor(ambientModeTextColor);
            binding.lapButton.setVisibility(View.GONE);
        } else {
            binding.lapDistance.setTextColor(normalTextColor);
            binding.lapDuration.setTextColor(normalTextColor);
            binding.lapButton.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onConnected(Bundle bundle) {
        Wearable.DataApi.addListener(googleApiClient, this)
            .setResultCallback(status -> checkWorkoutStatus());
    }

    @Override
    public void onConnectionSuspended(int cause) {
        if (binding != null) {
            binding.lapButton.setEnabled(false);
        }
    }

    @Override
    public void onConnectionFailed(ConnectionResult connectionResult) {
        if (binding != null) {
            binding.lapButton.setEnabled(false);
        }
    }

    @Override
    public void onResult(DataItemBuffer result) {
        int count = result.getCount();
        for (int i = 0; i < count; ++i) {
            DataItem dataItem = result.get(i);
            String path = dataItem.getUri().getPath();
            if (WearConstants.ONGOING_WORKOUT_RECORDING_STATE.equals(path)) {
                recordingState = DataMapItem.fromDataItem(dataItem)
                    .getDataMap()
                    .getByte(WearConstants.RECORDING_STATE, (byte) -1);
            }
        }
        refreshUi();
        result.release();
    }

    private void refreshUi() {
        if (binding != null) {
            binding.lapButton.setEnabled(recordingState == WearConstants.STARTED_OR_RESUMED
                || recordingState == WearConstants.AUTO_PAUSED);
        }
    }

    @Override
    public void onDataChanged(DataEventBuffer dataEvents) {
        ArrayList<DataEvent> events = FreezableUtils.freezeIterable(dataEvents);
        dataEvents.release();
        for (DataEvent dataEvent : events) {
            DataItem dataItem = dataEvent.getDataItem();
            Uri dataItemUri = dataItem.getUri();
            String path = dataItemUri.getPath();
            switch (dataEvent.getType()) {
                case DataEvent.TYPE_CHANGED:
                    if (WearConstants.ONGOING_WORKOUT_RECORDING_STATE.equals(path)) {
                        byte recordingState = DataMapItem.fromDataItem(dataItem)
                            .getDataMap()
                            .getByte(WearConstants.RECORDING_STATE, (byte) -1);
                        if (recordingState != -1) {
                            this.recordingState = recordingState;
                            HANDLER.post(recordingStateUpdatedRunnable);
                        }
                    }
                    break;
                case DataEvent.TYPE_DELETED:
                    // do nothing
                    break;
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (binding != null && v == binding.lapButton) {
            // the data item will be removed by the wearable listener on the phone
            PutDataMapRequest putDataMapRequest =
                PutDataMapRequest.create(WearConstants.ONGOING_WORKOUT_RECORDING_ACTION);
            DataMap dataMap = putDataMapRequest.getDataMap();
            dataMap.putByte(WearConstants.RECORDING_ACTION, WearConstants.ADD_LAP);
            WearHelper.sendDataItem(googleApiClient, putDataMapRequest);
        }
    }
}
