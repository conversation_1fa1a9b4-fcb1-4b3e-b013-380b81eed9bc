package com.stt.android.utils.test;

import com.stt.android.billing.Base64;
import com.stt.android.billing.Base64DecoderException;
import com.stt.android.billing.KeyObfuscator;

import java.util.Locale;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Bunch of tests for {@link com.stt.android.utils.CoordinateUtils} class.
 * Theses tests can be run with the following command:
 * <pre>./gradlew -Dtest.single=XorTests test</pre>
 */
public class XorTests {
    @Test
    public void basicXor() throws Base64DecoderException {
        String original = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        String key = "com.stt.android";
        byte[] xOrChars = KeyObfuscator.XorStrings(original, key);
        String encodedBase64 = Base64.encode(xOrChars);
        System.out.println(String.format(Locale.getDefault(), "Encoded base 64: '%s'", encodedBase64));
        assertNotEquals(original, encodedBase64);
        byte[] decodedBase64 = Base64.decode(encodedBase64);
        byte[] deXOrChars = KeyObfuscator.XorStrings(new String(decodedBase64), key);
        String expected = new String(deXOrChars);
        assertEquals(expected, original);
        System.out.println(String.format(Locale.getDefault(), "Expected '%s'. Original '%s'", expected, original));
    }
}
