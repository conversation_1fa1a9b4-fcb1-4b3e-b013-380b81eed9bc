package com.stt.android.utils;

import android.graphics.Paint;
import android.graphics.Typeface;
import androidx.annotation.NonNull;
import android.text.TextPaint;
import android.text.style.MetricAffectingSpan;

public class CustomFontStyleSpan extends MetricAffectingSpan {

    private final Typeface typeface;

    public CustomFontStyleSpan(@NonNull Typeface typeface) {
        this.typeface = typeface;
    }

    @Override
    public void updateMeasureState(TextPaint textPaint) {
        apply(textPaint);
    }

    @Override
    public void updateDrawState(TextPaint textPaint) {
        apply(textPaint);
    }

    private void apply(final Paint paint) {
        paint.setTypeface(typeface);
    }
}
