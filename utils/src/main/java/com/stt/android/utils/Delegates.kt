package com.stt.android.utils

import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KMutableProperty0
import kotlin.reflect.KProperty

fun <T, U> delegate(property: KMutableProperty0<T>): ReadWriteProperty<U, T> = DelegateVar(property)

private class DelegateVar<T : Any?>(
    private val delegate: KMutableProperty0<T>
) : ReadWriteProperty<Any?, T> {

    override fun getValue(thisRef: Any?, property: KProperty<*>): T {
        return delegate.get()
    }

    override fun setValue(thisRef: Any?, property: KProperty<*>, value: T) {
        delegate.set(value)
    }
}
