package com.stt.android.utils

/**
 * Filter out consecutive repeated elements from the sequence
 *
 * This is different from [distinct] in that [distinct] remembers all previous items, but this
 * [filterRepeated] only considers the immediate previous item.
 */
fun <T> Sequence<T>.filterRepeated(): Sequence<T> = sequence {
    var prev: T? = null
    var first = true
    for (item in asIterable()) {
        if (first || prev != item) {
            yield(item)
        }
        prev = item
        first = false
    }
}
