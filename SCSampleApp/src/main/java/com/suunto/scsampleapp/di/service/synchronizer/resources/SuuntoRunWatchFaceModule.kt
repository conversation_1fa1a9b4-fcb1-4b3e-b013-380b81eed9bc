package com.suunto.scsampleapp.di.service.synchronizer.resources

import com.suunto.connectivity.watchface.WatchFaceSyncTrigger
import com.suunto.scsampleapp.mock.MockSuuntoRunWatchFaceSyncLogic
import dagger.Binds
import dagger.Module

@Module
abstract class SuuntoRunWatchFaceModule {

    @Binds
    abstract fun bindSuuntoRunWatchFaceSyncTrigger(
        mockSuuntoRunWatchFaceSyncLogic: MockSuuntoRunWatchFaceSyncLogic
    ): WatchFaceSyncTrigger
}
