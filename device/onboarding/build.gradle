plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    alias libs.plugins.androidx.navigation.safeargs.kotlin
}

android {
    namespace 'com.stt.android.device.onboarding'

    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.timeline)
    implementation project(Deps.analytics)
    implementation project(Deps.datasource)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.remoteBase)
    implementation project(Deps.domain)
    implementation project(Deps.featureToggleApi)
    implementation libs.easypermissions
    implementation libs.lottie
    implementation libs.lottie.compose
    implementation libs.exoplayer
    implementation libs.androidx.browser
    implementation libs.rxjava2.android
    suuntoImplementation project(Deps.connectivity)
}
