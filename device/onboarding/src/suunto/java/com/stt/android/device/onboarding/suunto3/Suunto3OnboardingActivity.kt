package com.stt.android.device.onboarding.suunto3

import android.content.Context
import android.content.Intent
import android.widget.ImageView
import com.google.android.material.snackbar.Snackbar
import com.stt.android.device.onboarding.BaseOnboardingActivity
import com.stt.android.device.onboarding.OnboardingPage
import com.stt.android.device.onboarding.OnboardingPageFragment
import com.stt.android.device.onboarding.R
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class Suunto3OnboardingActivity :
    BaseOnboardingActivity<Suunto3OnboardingView, Suunto3OnboardingPresenter>(),
    Suunto3OnboardingView {

    @Inject
    override lateinit var presenter: Suunto3OnboardingPresenter

    override val view = this

    override fun getOnboardingPageChangeListener(bullets: Array<ImageView>) =
        OnboardingPageChangeListener(bullets)

    override fun showEnableCoachButton(show: Boolean) {
        listOf(
            Suunto3OnboardingGuidance,
            Suunto3FitnessOnboardingGuidance
        ).forEach {
            adapter.showPageButton(it, show)
        }
    }

    override fun showCoachEnablingWasSuccess() {
        Snackbar.make(
            binding.rootView,
            R.string.onboarding_coach_enabling_success,
            Snackbar.LENGTH_LONG
        )
            .show()
        listOf(
            Suunto3OnboardingGuidance,
            Suunto3FitnessOnboardingGuidance
        ).forEach {
            adapter.showPageButton(it, false)
        }
    }

    override fun showCoachEnablingFailed() {
        Snackbar.make(
            binding.rootView,
            R.string.onboarding_coach_enabling_failed,
            Snackbar.LENGTH_LONG
        )
            .setAction(BaseR.string.retry_action) { presenter.enableCoachClicked() }
            .show()
    }

    override fun onOnboardingPageCreated(page: OnboardingPage, fragment: OnboardingPageFragment) {
        super.onOnboardingPageCreated(page, fragment)

        if (page == Suunto3OnboardingGuidance || page == Suunto3FitnessOnboardingGuidance) {
            fragment.showPageButton(false)
            presenter.updateCoachEnabled()
        }
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context, deviceType: SuuntoDeviceType): Intent {
            return Intent(context, Suunto3OnboardingActivity::class.java)
                .putExtra(KEY_EXTRA_SUUNTO_DEVICE_TYPE, deviceType)
        }
    }
}
