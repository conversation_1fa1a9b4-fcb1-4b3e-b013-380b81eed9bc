package com.stt.android.device.onboarding.dive

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.device.onboarding.OnboardingPage
import com.stt.android.device.onboarding.R
import com.stt.android.R as BaseR

val DiveOnboardingAutoLocation = OnboardingPage(
    AnalyticsPropertyValue.OnboardingEonPageNameProperty.AUTOMATIC_LOCATION,
    R.string.onboarding_eon_auto_location_title,
    R.string.onboarding_eon_auto_location_detail,
    R.drawable.onboarding_eon_photo_auto_location
)

val EonCoreOnboardingMakeItYours = OnboardingPage(
    AnalyticsPropertyValue.OnboardingEonPageNameProperty.CUSTOMIZE,
    R.string.onboarding_eon_make_it_yours_title,
    R.string.onboarding_eon_make_it_yours_detail,
    R.drawable.onboarding_eon_core_connect_and_customize
)

val EonCoreOnboardingWirelessTankPressure = OnboardingPage(
    AnalyticsPropertyValue.OnboardingEonPageNameProperty.WIRELESS_TANK_PRESSURE,
    R.string.onboarding_eon_wireless_tank_pressure_title,
    R.string.onboarding_eon_wireless_tank_pressure_detail,
    R.drawable.onboarding_eon_core_wireless_tank_pressure
)

val EonCoreOnboardingReliveAndShare = OnboardingPage(
    AnalyticsPropertyValue.OnboardingEonPageNameProperty.RELIVE_AND_SHARE,
    R.string.onboarding_eon_relive_and_share_title,
    R.string.onboarding_eon_relive_and_share_detail,
    R.drawable.onboarding_eon_core_photo_relive_and_share
)

val EonCoreOnboardingEnd = OnboardingPage(
    AnalyticsPropertyValue.OnboardingEonPageNameProperty.END,
    R.string.onboarding_eon_final_title,
    R.string.onboarding_eon_final_detail,
    R.drawable.onboarding_eon_core_photo_final,
    primaryButtonResId = BaseR.string.ok,
    secondaryButtonResId = BaseR.string.onboarding_how_to_use
)

val EonCoreOnboardingPages = listOf(
    EonCoreOnboardingMakeItYours,
    DiveOnboardingAutoLocation,
    EonCoreOnboardingWirelessTankPressure,
    EonCoreOnboardingReliveAndShare,
    EonCoreOnboardingEnd
)
