package com.stt.android.device.suuntoplusguide

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.source.local.AppDatabase
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusPluginStatus
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusPluginType
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncEvent
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.WatchPluginStatusDataSource
import com.stt.android.device.datasource.suuntoplusfeature.SuuntoPlusFeaturesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.TrainingPlansLocalDataSource
import com.stt.android.device.domain.GetDefaultWatchfaceIdUseCase
import com.stt.android.device.domain.suuntoplusfeature.settings.SportsAppSettingsStateDataSource
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.remote.suuntoplusfeature.DummySuuntoPlusFeaturesRemoteAPI
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRemoteDataSource
import com.stt.android.device.remote.suuntoplusguide.DummySuuntoPlusGuideRemoteAPI
import com.stt.android.device.remote.suuntoplusguide.DummyTrainingPlanRemoteAPI
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteDataSource
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteSyncLogic
import com.stt.android.device.remote.suuntoplusguide.TrainingPlanRemoteDataSource
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.domain.featuretoggle.FeatureEnabledStateUseCase
import com.stt.android.testutils.CoroutinesTestRule
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideSyncLogicResult
import io.reactivex.Single
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnit
import org.mockito.junit.MockitoRule
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.io.IOException
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.concurrent.Executors

@RunWith(AndroidJUnit4::class)
class SuuntoPlusGuideRemoteSyncLogicTest {

    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @Rule
    @JvmField
    val archTaskExecutorRule = InstantTaskExecutorRule()

    @JvmField
    @Rule
    val mockitoRule: MockitoRule = MockitoJUnit.rule()

    private lateinit var db: AppDatabase

    private lateinit var guideRemoteAPI: DummySuuntoPlusGuideRemoteAPI

    private lateinit var trainingPlanRemoteAPI: DummyTrainingPlanRemoteAPI

    private lateinit var guideRemoteDataSource: SuuntoPlusGuideRemoteDataSource

    private lateinit var trainingPlansRemoteDataSource: TrainingPlanRemoteDataSource

    private lateinit var featuresRemoteAPI: DummySuuntoPlusFeaturesRemoteAPI

    private lateinit var featuresRemoteDataSource: SuuntoPlusFeaturesRemoteDataSource

    private lateinit var guideLocalDataSource: SuuntoPlusGuidesLocalDataSource

    private lateinit var localTrainingPlanDataSource: TrainingPlansLocalDataSource

    private lateinit var featuresLocalDataSource: SuuntoPlusFeaturesLocalDataSource

    private lateinit var pluginStatusDataSource: WatchPluginStatusDataSource

    private lateinit var settingsStateDataSource: SportsAppSettingsStateDataSource

    private lateinit var remoteSyncLogic: SuuntoPlusGuideRemoteSyncLogic

    private lateinit var zappStorage: TestGuideZAPPFileStorage

    private lateinit var capabilityStore: TestSuuntoWatchCapabilityStore

    private lateinit var syncStateRepository: SuuntoPlusSyncStateRepository

    @Mock
    lateinit var featureEnabledStateUseCase: FeatureEnabledStateUseCase

    @Mock
    lateinit var getDefaultWatchfaceIdUseCase: GetDefaultWatchfaceIdUseCase

    @Mock
    lateinit var deviceConnectionStateUseCase: DeviceConnectionStateUseCase

    @Before
    fun setup() {
        // Basic setup including Room
        val context = ApplicationProvider.getApplicationContext<Context>()
        db = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java)
            .setTransactionExecutor(Executors.newSingleThreadExecutor())
            .build()

        // ZAPP store
        zappStorage = TestGuideZAPPFileStorage()

        // Capabilities
        capabilityStore = TestSuuntoWatchCapabilityStore()

        // Sync state
        syncStateRepository = SuuntoPlusSyncStateRepository(
            db.suuntoPlusSyncStateDao()
        )

        // Guide DAO and data source
        val guideDao = db.suuntoPlusGuideDao()
        val guideSyncLogEventDao = db.suuntoPlusGuideSyncLogEventDao()
        val pluginDeviceStatusDao = db.suuntoPlusPluginDeviceStatusDao()
        val deviceStatusDao = db.suuntoPlusPluginDeviceStatusDao()
        guideRemoteAPI = DummySuuntoPlusGuideRemoteAPI()
        guideRemoteDataSource = SuuntoPlusGuideRemoteDataSource(guideRemoteAPI)
        trainingPlanRemoteAPI = DummyTrainingPlanRemoteAPI()
        trainingPlansRemoteDataSource = TrainingPlanRemoteDataSource(trainingPlanRemoteAPI)
        val planDao = db.trainingPlanDao()

        guideLocalDataSource = SuuntoPlusGuidesLocalDataSource(guideDao, pluginDeviceStatusDao)
        pluginStatusDataSource = WatchPluginStatusDataSource(deviceStatusDao, guideDao)
        localTrainingPlanDataSource = TrainingPlansLocalDataSource(planDao, deviceStatusDao, pluginStatusDataSource)

        // Features DAO and data source
        val featuresDao = db.suuntoPlusFeatureDao()
        featuresRemoteAPI = DummySuuntoPlusFeaturesRemoteAPI()
        featuresRemoteDataSource = SuuntoPlusFeaturesRemoteDataSource(featuresRemoteAPI, getDefaultWatchfaceIdUseCase)
        featuresLocalDataSource = SuuntoPlusFeaturesLocalDataSource(featuresDao, pluginDeviceStatusDao)
        settingsStateDataSource = SportsAppSettingsStateDataSource(db.sportsAppSettingsStateDao())

        whenever(featureEnabledStateUseCase.fetchFeatureEnabledState(any(), any()))
            .thenReturn(Single.just(false))

        // Sync logic
        remoteSyncLogic = SuuntoPlusGuideRemoteSyncLogic(
            remoteGuideDataSource = guideRemoteDataSource,
            localGuideDataSource = guideLocalDataSource,
            trainingPlanRemoteDataSource = trainingPlansRemoteDataSource,
            localTrainingPlanDataSource = localTrainingPlanDataSource,
            pluginStatusDataSource = pluginStatusDataSource,
            remoteFeaturesDataSource = featuresRemoteDataSource,
            localFeaturesDataSource = featuresLocalDataSource,
            syncLogEventDao = guideSyncLogEventDao,
            syncStateRepository = syncStateRepository,
            zappStorage = zappStorage,
            capabilityStore = capabilityStore,
            settingsStateDataSource = settingsStateDataSource,
            clock = Clock.fixed(Instant.parse("2021-10-12T12:09:37Z"), ZoneId.of("UTC")),
            deviceConnectionStateUseCase = deviceConnectionStateUseCase,
        )
    }

    @After
    @Throws(IOException::class)
    fun tearDown() {
        db.close()
    }

    @Test
    fun backendSyncShouldFailIfWatchSyncIsRunning(): Unit = runBlocking {
        db.suuntoPlusGuideSyncLogEventDao().insert(
            localSuuntoPlusGuideSyncEvent(SuuntoPlusGuideSyncEvent.WATCH_SYNC_STARTED)
        )

        db.suuntoPlusSyncStateDao().insert(
            LocalSuuntoPlusSyncState(
                watchSerial = "123",
                syncState = SuuntoPlusSyncState.WATCH_SYNC_ONGOING,
                previousWatchSyncCapabilities = capabilityStore.current!!,
                previousRemoteSyncCapabilities = capabilityStore.current!!
            )
        )
        val result = remoteSyncLogic.syncWithBackend("123")
        val lastLog = db.suuntoPlusGuideSyncLogEventDao().fetchLastSyncEvent()

        assertThat(lastLog?.event).isEqualTo(SuuntoPlusGuideSyncEvent.WATCH_SYNC_STARTED)
        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Failure::class.java)
    }

    @Test
    fun backendSyncShouldFailIfBackendSyncIsRunning(): Unit = runBlocking {
        db.suuntoPlusGuideSyncLogEventDao().insert(
            localSuuntoPlusGuideSyncEvent(SuuntoPlusGuideSyncEvent.BACKEND_SYNC_STARTED)
        )

        db.suuntoPlusSyncStateDao().insert(
            LocalSuuntoPlusSyncState(
                watchSerial = "123",
                syncState = SuuntoPlusSyncState.REMOTE_SYNC_ONGOING,
                previousWatchSyncCapabilities = capabilityStore.current!!,
                previousRemoteSyncCapabilities = capabilityStore.current!!
            )
        )

        val result = remoteSyncLogic.syncWithBackend("123")
        val lastLog = db.suuntoPlusGuideSyncLogEventDao().fetchLastSyncEvent()

        assertThat(lastLog?.event).isEqualTo(SuuntoPlusGuideSyncEvent.BACKEND_SYNC_STARTED)
        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Failure::class.java)
    }

    @Test
    fun shouldLocallyDeleteGuidesRemovedFromBackend(): Unit = runBlocking {
        val localToBeDeleted = localSuuntoPlusGuide(id = "lw9yigou8")
        db.suuntoPlusGuideDao().insert(localToBeDeleted)

        val result = remoteSyncLogic.syncWithBackend("123")
        val guides = guideLocalDataSource.listAllSuuntoPlusGuides().first()

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)
        assertThat(guides).hasSize(4)
        assertThat(guides).doesNotContain(localToBeDeleted)
    }

    @Test
    fun shouldInsertNewGuidesFromBackendLocally(): Unit = runBlocking {
        val result = remoteSyncLogic.syncWithBackend("123")
        val localIds = guideLocalDataSource.listAllSuuntoPlusGuides().first().map { it.id }

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)
        assertThat(localIds).containsExactly(
            SuuntoPlusGuideId("thje6p3km"),
            SuuntoPlusGuideId("rpc0ilwte"),
            SuuntoPlusGuideId("oxbo4nlz6"),
            SuuntoPlusGuideId("arey36ehg")
        )
    }

    @Test
    fun localGuideWithMoreRecentModifiedTimeShouldBeKept(): Unit = runBlocking {
        val modified = LocalDateTime.of(2022, 5, 1, 12, 0).toInstant(ZoneOffset.UTC).toEpochMilli()
        val localMoreRecent = localSuuntoPlusGuide(
            id = "thje6p3km",
            modified = modified,
            description = "this is more recent"
        )
        db.suuntoPlusGuideDao().insert(localMoreRecent)
        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = "thje6p3km",
                modifiedMillis = modified,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        val result = remoteSyncLogic.syncWithBackend("123")
        val localDeviceState = db.suuntoPlusPluginDeviceStatusDao().findWatchState(
            watchSerial = "123",
            pluginId = "thje6p3km"
        )

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)

        // Should be still in IN_WATCH state because the sync won't downgrade the Zapp plug-in
        assertThat(localDeviceState?.status).isEqualTo(LocalSuuntoPlusPluginStatus.IN_WATCH)
    }

    @Test
    fun localGuideWithOlderModifiedTimeShouldBeUpdated(): Unit = runBlocking {
        val localOlder = localSuuntoPlusGuide(
            id = "thje6p3km",
            modified = 1588334400000L, // 2020-05-01 12:00 UTC
            description = "this is very old already"
        )
        db.suuntoPlusGuideDao().insert(localOlder)
        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = "thje6p3km",
                modifiedMillis = 1588334400000L,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        val result = remoteSyncLogic.syncWithBackend("123")
        val localResult = guideLocalDataSource.findSuuntoPlusGuideWithWatchStatus(
            watchSerial = "123",
            guideId = SuuntoPlusGuideId("thje6p3km")
        ).first()

        val localDeviceState = db.suuntoPlusPluginDeviceStatusDao().findWatchState(
            watchSerial = "123",
            pluginId = "thje6p3km"
        )

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)

        // Should match description and timestamp from mock data in DummySuuntoPlusGuideRemoteAPI
        assertThat(localResult.first?.description).isEqualTo("Plan for race pacing.")
        assertThat(localResult.second).isEqualTo(SuuntoPlusPluginStatus.DOWNLOADED)
        assertThat(localDeviceState?.modifiedMillis).isEqualTo(1621383000000L)
    }

    @Test
    fun triggerWatchSyncShouldBeTrueWhenAddingNewGuides() = runBlocking {
        val result = remoteSyncLogic.syncWithBackend("123")
        assertThat(result.triggerWatchSync).isTrue()
    }

    @Test
    fun triggerWatchSyncShouldBeFalseIfNoChanges() = runBlocking {
        // Make sure ZAPP storage reports all files as cached
        zappStorage.hasEverything = true

        // First sync, populate local database based on remote
        remoteSyncLogic.syncWithBackend("123")

        // Simulate watch sync by updating plug-in status to IN_WATCH for all enabled features
        for (feature in featuresLocalDataSource.listSuuntoPlusFeatures().first()) {
            db.suuntoPlusPluginDeviceStatusDao().upsert(
                localPluginDeviceStatus(
                    pluginId = feature.pluginId!!,
                    modifiedMillis = feature.modifiedMillis,
                    status = if (feature.enabled) {
                        LocalSuuntoPlusPluginStatus.IN_WATCH
                    } else {
                        LocalSuuntoPlusPluginStatus.UNKNOWN
                    }
                )
            )
        }

        // Second sync with same remote data -> no changes
        val result = remoteSyncLogic.syncWithBackend("123")

        assertThat(result.triggerWatchSync).isFalse()
    }

    @Test
    fun shouldAddDeviceStatusForNewGuide(): Unit = runBlocking {
        zappStorage.hasEverything = true
        remoteSyncLogic.syncWithBackend("123") // Add guides to db

        val statuses = db.suuntoPlusPluginDeviceStatusDao().findAll()
        val watchSerials = statuses.map { it.watchSerial }.toSet()
        val statusesByGuideId = statuses
            .filter { it.type == LocalSuuntoPlusPluginType.GUIDE }
            .groupBy { it.pluginId }
            .mapValues { it.value.first().status }

        assertThat(watchSerials).containsExactly("123")
        assertThat(statusesByGuideId).containsExactlyEntriesIn(
            mapOf(
                "thje6p3km" to LocalSuuntoPlusPluginStatus.DOWNLOADED,
                "rpc0ilwte" to LocalSuuntoPlusPluginStatus.DOWNLOADED,
                "oxbo4nlz6" to LocalSuuntoPlusPluginStatus.DOWNLOADED,
                "arey36ehg" to LocalSuuntoPlusPluginStatus.DOWNLOADED,
            )
        )
    }

    @Test
    fun shouldRemoveDeviceStatusForGuideDeletedOnBackend(): Unit = runBlocking {
        zappStorage.hasEverything = true
        remoteSyncLogic.syncWithBackend("123") // Add guides to db
        // Delete other predefined guides but leave "thje6p3km"
        guideRemoteAPI.delete("rpc0ilwte")
        guideRemoteAPI.delete("oxbo4nlz6")
        guideRemoteAPI.delete("arey36ehg")
        remoteSyncLogic.syncWithBackend("123") // Remove guides from db

        val statuses = db.suuntoPlusPluginDeviceStatusDao().findAll()
        val watchSerials = statuses.map { it.watchSerial }.toSet()
        val statusesByGuideId = statuses
            .filter { it.type == LocalSuuntoPlusPluginType.GUIDE }
            .groupBy { it.pluginId }
            .mapValues { it.value.first().status }

        assertThat(watchSerials).containsExactly("123")
        assertThat(statusesByGuideId).containsExactly(
            "thje6p3km",
            LocalSuuntoPlusPluginStatus.DOWNLOADED
        )
    }

    @Test
    fun shouldUpdateCatalogueIdIfChanged(): Unit = runBlocking {
        zappStorage.hasEverything = true

        // Use identical value for 'modifiedMillis' so that the normal update logic will not be
        // triggered. The 'modifiedMillis' values did not change when the catalogue ID was
        // introduced.
        val remoteModifiedMillis = guideRemoteAPI.guides.first { it.id == "thje6p3km" }.modifiedMillis

        val local = localSuuntoPlusGuide(
            id = "thje6p3km",
            modified = remoteModifiedMillis,
        )
        db.suuntoPlusGuideDao().insert(local)

        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = "thje6p3km",
                modifiedMillis = remoteModifiedMillis,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        remoteSyncLogic.syncWithBackend("123")

        // Catalogue ID should have been updated from null to "j6yf3yq9"
        assertThat(guideLocalDataSource.findById(SuuntoPlusGuideId("thje6p3km"))?.catalogueId)
            .isEqualTo("j6yf3yq9")
    }

    @Test
    fun shouldUpdateOwnerIdIfNullInDatabase(): Unit = runBlocking {
        zappStorage.hasEverything = true

        // Use identical value for 'modifiedMillis' so that the normal update logic will not be
        // triggered. The 'modifiedMillis' values did not change when the owner ID was
        // introduced.
        val remoteModifiedMillis = guideRemoteAPI.guides.first { it.id == "thje6p3km" }.modifiedMillis

        val local = localSuuntoPlusGuide(
            id = "thje6p3km",
            modified = remoteModifiedMillis,
        )
        db.suuntoPlusGuideDao().insert(local)

        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = "thje6p3km",
                modifiedMillis = remoteModifiedMillis,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        remoteSyncLogic.syncWithBackend("123")

        // Owner ID should have been updated from null to "j6yf3yq9"
        assertThat(guideLocalDataSource.findById(SuuntoPlusGuideId("thje6p3km"))?.ownerId)
            .isEqualTo("2b70f789-9e73-4ba1-ae18-579bf39d15fd")
    }

    @Test
    fun localFeatureWithOlderModifiedTimeShouldBeUpdated(): Unit = runBlocking {
        val localOlder = localSuuntoPlusFeature(
            id = "zztrpp01",
            pluginId = "zztrppen",
            modified = 1588334400000L, // 2020-05-01 12:00 UTC
            description = "this is very old already"
        )
        db.suuntoPlusFeatureDao().insert(localOlder)
        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = localOlder.pluginId!!,
                modifiedMillis = 1588334400000L,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        val result = remoteSyncLogic.syncWithBackend("123")

        val localDeviceState = db.suuntoPlusPluginDeviceStatusDao().findWatchState(
            watchSerial = "123",
            pluginId = "zztrppen"
        )

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)

        // Should match description and timestamp from mock data in DummySuuntoPlusGuideRemoteAPI
        assertThat(localDeviceState?.status).isEqualTo(LocalSuuntoPlusPluginStatus.DOWNLOADED)
        assertThat(localDeviceState?.modifiedMillis).isEqualTo(1621383000000L)
    }

    @Test
    fun localFeatureWithMoreRecentModifiedTimeShouldBeKept(): Unit = runBlocking {
        zappStorage.hasEverything = true
        val localOlder = localSuuntoPlusFeature(
            id = "zztrpp01",
            pluginId = "zztrppen",
            modified = 1621384000000L, // 2021-05-19 00:26:40 UTC
            description = "newer than the one from remote"
        )
        db.suuntoPlusFeatureDao().insert(localOlder)
        db.suuntoPlusPluginDeviceStatusDao().insert(
            localPluginDeviceStatus(
                pluginId = localOlder.pluginId!!,
                modifiedMillis = 1621384000000L,
                status = LocalSuuntoPlusPluginStatus.IN_WATCH,
            )
        )

        val result = remoteSyncLogic.syncWithBackend("123")

        val localDeviceState = db.suuntoPlusPluginDeviceStatusDao().findWatchState(
            watchSerial = "123",
            pluginId = "zztrppen"
        )

        assertThat(result).isInstanceOf(SuuntoPlusGuideSyncLogicResult.Success::class.java)

        // Should match the timestamp set up above since the plugin from
        // DummySuuntoPlusGuideRemoteAPI is older.
        assertThat(localDeviceState?.modifiedMillis).isEqualTo(1621384000000L)
        assertThat(localDeviceState?.status).isEqualTo(LocalSuuntoPlusPluginStatus.IN_WATCH)
    }
}
