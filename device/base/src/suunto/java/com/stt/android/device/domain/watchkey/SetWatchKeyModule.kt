package com.stt.android.device.domain.watchkey

import com.stt.android.offlinemaps.domain.SetWatchKeyUseCase
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SetWatchKeyModule {
    @Binds
    abstract fun bindFetchWatchKeyUseCase(useCase: SetWatchKeyForWatchUseCase): SetWatchKeyUseCase
}
