package com.stt.android.device.domain.suuntoplusfeature.settings

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonDataException
import com.squareup.moshi.Moshi
import com.stt.android.device.remote.suuntoplusfeature.manifest.SuuntoPlusManifest
import com.stt.android.device.remote.suuntoplusfeature.manifest.toDomain
import com.stt.android.device.suuntoplusfeature.settings.SettingsJson
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import javax.inject.Inject

class ParseSportsAppSettingsFromManifestUseCase
@Inject constructor(
    private val moshi: <PERSON>shi
) {
    private val adapter: JsonAdapter<SuuntoPlusManifest> =
        moshi.adapter(SuuntoPlusManifest::class.java)

    data class Result(
        val settingsJson: SettingsJson,
        val variables: ImmutableList<SuuntoPlusVariable>,
        val settings: ImmutableList<SuuntoPlusSetting>
    )

    fun parse(
        settingsJsonString: String,
        manifestJson: String,
    ): Result = parse(SettingsJson.parse(settingsJsonString), manifestJson)

    fun parse(
        settingsJson: SettingsJson,
        manifestJson: String,
    ): Result {
        val manifest = adapter.fromJson(manifestJson) ?: throw JsonDataException("Trying to parse manifest JSON returned null!")

        return Result(
            settingsJson = settingsJson,
            variables = manifest.variables.orEmpty().mapNotNull { it.toDomain(settingsJson) }.toImmutableList(),
            settings = manifest.settings.orEmpty().mapNotNull { it.toDomain(settingsJson) }.toImmutableList()
        )
    }
}
