package com.stt.android.device.watch

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.domain.watch.IsWatchBusyUseCase
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

enum class WatchBusyState {
    IDLE,
    SYNC_ONGOING,
    BUSY
}

interface SuuntoPlusWatchStateListener {
    val watchBusyState: Flow<WatchBusyState>
    val anySyncOngoing: Flow<Boolean> // SuuntoPlus remote or watch sync ongoing
    suspend fun anySyncOngoingNow(): Boolean
}

class SuuntoPlusWatchStateListenerImpl(
    private val isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase
) : SuuntoPlusWatchStateListener {

    @OptIn(FlowPreview::class)
    override val watchBusyState: Flow<WatchBusyState>
        get() {
            val syncOngoing = isSyncOngoingUseCase.getSyncStateFlow()
                .map { it == SuuntoPlusSyncState.WATCH_SYNC_ONGOING }
                .distinctUntilChanged()
                .debounce(SHOW_WATCH_SYNC_INDICATOR_DEBOUNCE_TIME)

            val watchBusy = isWatchBusyUseCase()

            return combine(syncOngoing, watchBusy) { syncing, busy ->
                when {
                    syncing -> WatchBusyState.SYNC_ONGOING
                    busy -> WatchBusyState.BUSY
                    else -> WatchBusyState.IDLE
                }
            }
        }

    override val anySyncOngoing: Flow<Boolean>
        get() = isSyncOngoingUseCase.getSyncStateFlow()
            .map { it != SuuntoPlusSyncState.IDLE }
            .distinctUntilChanged()

    override suspend fun anySyncOngoingNow() =
        isSyncOngoingUseCase.getSyncStateFlow().first() != SuuntoPlusSyncState.IDLE
}

private const val SHOW_WATCH_SYNC_INDICATOR_DEBOUNCE_TIME = 1000L
