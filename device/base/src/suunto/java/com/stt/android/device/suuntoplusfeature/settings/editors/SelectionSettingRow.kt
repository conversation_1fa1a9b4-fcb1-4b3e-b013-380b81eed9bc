package com.stt.android.device.suuntoplusfeature.settings.editors

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.DropdownMenu
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.device.domain.suuntoplusfeature.settings.SelectionSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.SelectionSettingOption

@Composable
internal fun SelectionSettingRow(
    setting: SelectionSetting,
    onSettingChange: (newIndex: Int) -> Unit,
    enabled: <PERSON>olean,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    var currentValue by remember(setting.value) { mutableStateOf(setting.value) }

    SettingRow(
        name = setting.displayName,
        modifier = modifier,
        onClick = {
            if (enabled) {
                expanded = true
            }
        },
        enabled = enabled,
    ) {
        Box {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = if (enabled) {
                    Modifier.clickable { expanded = true }
                } else {
                    Modifier
                }
            ) {
                Text(
                    text = currentValue?.displayName.orEmpty(),
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    color = MaterialTheme.colors.primary
                )
            }

            DropdownMenu(
                expanded = expanded && enabled,
                onDismissRequest = { expanded = false }
            ) {
                for (option in setting.options) {
                    DropdownMenuItem(
                        onClick = {
                            currentValue = option
                            expanded = false
                            onSettingChange(option.index)
                        }
                    ) {
                        Text(option.displayName)
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SelectionSettingRowPreview() {
    AppTheme {
        Surface {
            SelectionSettingRow(
                setting = SelectionSetting(
                    path = "key",
                    displayName = "Selection",
                    options = listOf(
                        SelectionSettingOption("option1", "First option", 0),
                        SelectionSettingOption("option2", "Second option", 1),
                    ),
                    value = SelectionSettingOption("option1", "First option", 0),
                    mandatoryMessage = null,
                ),
                onSettingChange = {},
                enabled = true
            )
        }
    }
}
