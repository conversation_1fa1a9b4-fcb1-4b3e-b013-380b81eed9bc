package com.stt.android.device.domain.widget.entities

import com.stt.android.device.R
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

class WatchWidgetHelpProvider(
    private val deviceType: SuuntoDeviceType
) {
    val watchWidgetHelp: WatchWidgetHelp?
        get() = when {
            deviceType.isSuuntoSparrow -> suuntoSparrowWidgetHelp()
            deviceType.isSuuntoVertical2 -> suuntoVertical2WidgetHelp()
            deviceType.isSuuntoVertical -> suuntoVerticalWidgetHelp()
            deviceType.isSuuntoRace2 -> suuntoRace2WidgetHelp()
            deviceType.isSuuntoRace -> suuntoRaceWidgetHelp()
            deviceType.isSuuntoRaceS -> suuntoRacesWidgetHelp()
            deviceType.isSuuntoOcean -> suuntoOceanWidgetHelp()
            deviceType.isSuuntoRun -> suuntoRunWidgetHelp()
            else -> null
        }

    private fun suuntoWidgetHelp(
        descriptionRes: Int = R.string.watch_widget_help_description_v1,
        imageRes: Int
    ): WatchWidgetHelp {
        return WatchWidgetHelp(descriptionRes = descriptionRes, imageRes = imageRes)
    }

    private fun suuntoSparrowWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_sparrow_help)

    private fun suuntoVertical2WidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_vertical2_help)

    private fun suuntoVerticalWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_vertical_help)

    private fun suuntoRace2WidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_race2_help)

    private fun suuntoRaceWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_race_help)

    private fun suuntoRacesWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_race_s_help)

    private fun suuntoOceanWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_seal_help)

    private fun suuntoRunWidgetHelp(): WatchWidgetHelp =
        suuntoWidgetHelp(imageRes = R.drawable.watch_widget_run_help)
}
