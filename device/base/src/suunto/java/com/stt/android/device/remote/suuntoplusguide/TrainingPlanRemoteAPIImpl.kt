package com.stt.android.device.remote.suuntoplusguide

import javax.inject.Inject

class TrainingPlanRemoteAPIImpl @Inject constructor(
    private val restAPI: TrainingPlanRestAPI
) : TrainingPlanRemoteAPI {

    override suspend fun fetchAll(): List<RemoteTrainingPlanInfo> =
        restAPI.fetchAll().payloadOrThrow()

    override suspend fun updatePinnedStatus(id: String, pinned: Boolean) {
        restAPI.updatePinnedStatus(id, pinned)
    }

    override suspend fun fetchZAPPFile(planId: String, guideId: String, capabilities: String): ByteArray {
        return restAPI.downloadZAPPFile(planId, guideId, capabilities)
            .readValidatedBytesOrThrow("planId: $planId, guideId: $guideId", "ZAPP")
    }

    override suspend fun deleteMyPlan(planId: String) {
        restAPI.deleteMyPlan(planId)
    }
}
