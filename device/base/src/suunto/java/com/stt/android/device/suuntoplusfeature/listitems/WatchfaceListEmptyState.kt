package com.stt.android.device.suuntoplusfeature.listitems

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R

@Composable
fun WatchfaceListEmptyState(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.smaller),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_suunto_plus_watch_face),
            tint = MaterialTheme.colors.mediumGrey,
            contentDescription = null
        )

        Text(
            text = stringResource(id = R.string.suunto_plus_watchface_empty_state_text),
            color = MaterialTheme.colors.darkGreyText,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.large),
        )
    }
}

@Preview
@Composable
private fun WatchfaceListEmptyStatePreview() {
    AppTheme {
        WatchfaceListEmptyState()
    }
}
