package com.stt.android.device.suuntoplusguide.details

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BottomSheetHandle
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.body

@Composable
fun HowToUsePlanSheet(
    howToUsePlanSheetType: HowToUsePlanSheetType,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .padding(
                start = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.xsmall,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.xlarge
            ),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
    ) {
        BottomSheetHandle()
        howToUsePlanSheetType.startInfoItems.forEachIndexed { index, item ->
            StartInfoItemContent(
                number = index + 1,
                textRes = item.text,
                highlightRes = item.highlight,
                imageRes = item.image,
                howToUsePlanSheetType = howToUsePlanSheetType
            )
        }
    }
}

@Composable
private fun StartInfoItemContent(
    howToUsePlanSheetType: HowToUsePlanSheetType,
    number: Int,
    @StringRes textRes: Int,
    @StringRes highlightRes: Int?,
    @DrawableRes imageRes: Int?,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall)
    ) {
        val textWithNumber = "$number. ${stringResource(textRes)}"
        Text(
            text = with(AnnotatedString.Builder(textWithNumber)) {
                highlightRes?.let {
                    val boldText = stringResource(highlightRes)
                    val startIndex = textWithNumber.indexOf(boldText)
                    addStyle(
                        style = SpanStyle(fontWeight = FontWeight.Bold),
                        start = startIndex,
                        end = startIndex + boldText.length
                    )
                }
                toAnnotatedString()
            },
            style = MaterialTheme.typography.body
        )
        imageRes?.let {
            Spacer(modifier = Modifier.height(
                when {
                    number == 1 -> MaterialTheme.spacing.xlarge
                    howToUsePlanSheetType is HowToUsePlanSheetType.SuuntoRun -> MaterialTheme.spacing.medium
                    else -> 0.dp
                }
            ))
            Image(
                painter = painterResource(id = it),
                contentDescription = textWithNumber,
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        if (number == 1) Modifier.wrapContentHeight()
                        else Modifier.height(140.dp)
                    ),
                contentScale = ContentScale.Fit
            )
            if (number == 1) {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            }
        }
    }
}

data class StartInfoItem(@StringRes val text: Int, @StringRes val highlight: Int? = null, @DrawableRes val image: Int? = null)

@Preview(showBackground = true)
@Composable
private fun GuideInfoSheetPreview() {
    AppTheme {
        HowToUsePlanSheet(howToUsePlanSheetType = HowToUsePlanSheetType.SuuntoRun)
    }
}

@Preview(showBackground = true)
@Composable
private fun PlanInfoSheetPreview() {
    AppTheme {
        HowToUsePlanSheet(howToUsePlanSheetType = HowToUsePlanSheetType.NG3)
    }
}
