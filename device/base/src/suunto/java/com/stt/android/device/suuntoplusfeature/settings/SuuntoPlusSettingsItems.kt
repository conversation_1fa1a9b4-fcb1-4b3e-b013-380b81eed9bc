package com.stt.android.device.suuntoplusfeature.settings

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.device.domain.suuntoplusfeature.settings.BooleanSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.BooleanVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.FloatingPointSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.FloatingPointVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.IntegerSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.IntegerVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.SelectionSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.SelectionVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.StringSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.StringVariable
import com.stt.android.device.domain.suuntoplusfeature.settings.SuuntoPlusSetting
import com.stt.android.device.domain.suuntoplusfeature.settings.SuuntoPlusVariable
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusItemDetailNote
import com.stt.android.device.suuntoplusfeature.settings.editors.BooleanSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.FloatingPointSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.IntegerSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.SelectionSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.SliderSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.StringSettingRow
import com.stt.android.device.suuntoplusfeature.settings.editors.VariableRow
import kotlinx.collections.immutable.ImmutableList
import java.util.Locale
import kotlin.math.roundToInt

fun LazyListScope.settingsItems(
    enabled: Boolean,
    variables: ImmutableList<SuuntoPlusVariable>,
    settings: ImmutableList<SuuntoPlusSetting>,
    settingsNonce: Int, // If changed, reset all inputs to the value from settings objects
    showWatchNotConnectedMessage: Boolean,
    showWatchBusyMessage: Boolean,
    onSettingChange: (path: String, newValue: Any?) -> Unit,
) {
    val localAlphaValue = LocalContentAlpha provides if (enabled) 1.0f else 0.6f
    if (enabled && settings.any { it.isMandatory && !it.isValid }) {
        item("mandatoryValueMissing") {
            SuuntoPlusItemDetailNote(
                note = SuuntoPlusDetailsNote.MandatorySettingValueMissing,
                onNoteAction = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }
    }

    if (showWatchNotConnectedMessage) {
        item("watchNotConnectedMessage") {
            SuuntoPlusItemDetailNote(
                note = SuuntoPlusDetailsNote.WatchNotConnected,
                onNoteAction = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }
    }

    if (showWatchBusyMessage) {
        item("watchBusyMessage") {
            SuuntoPlusItemDetailNote(
                note = SuuntoPlusDetailsNote.WatchBusy,
                onNoteAction = {},
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
        }
    }

    if (variables.isNotEmpty()) {
        item("statisticsHeader") {
            CompositionLocalProvider(localAlphaValue) {
                Text(
                    text = stringResource(R.string.suunto_plus_sports_app_details_statistics_title)
                        .uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodyBold,
                    modifier = Modifier.padding(MaterialTheme.spacing.medium)
                )

                Divider(color = MaterialTheme.colors.dividerColor)
            }
        }
    }

    items(items = variables, key = { it.key }) { variable ->
        CompositionLocalProvider(localAlphaValue) {
            CompositionLocalProvider(LocalTextStyle provides MaterialTheme.typography.bodyLarge) {
                when (variable) {
                    is FloatingPointVariable ->
                        VariableRow(
                            displayName = variable.displayName,
                            unit = variable.unit,
                            value = SettingFormatter.format(variable.value)
                        )

                    is IntegerVariable ->
                        VariableRow(
                            displayName = variable.displayName,
                            unit = variable.unit,
                            value = variable.value?.toString().orEmpty()
                        )

                    is SelectionVariable -> {
                        /* Not supported */
                    }

                    is StringVariable ->
                        VariableRow(
                            displayName = variable.displayName,
                            unit = null,
                            value = variable.value ?: ""
                        )

                    is BooleanVariable ->
                        VariableRow(
                            displayName = variable.displayName,
                            value = variable.value ?: false
                        )
                }
            }

            Divider(color = MaterialTheme.colors.dividerColor)
        }
    }

    if (settings.isNotEmpty()) {
        item("settingsHeader") {
            CompositionLocalProvider(localAlphaValue) {
                Text(
                    text = stringResource(id = R.string.suunto_plus_sports_app_details_settings_title)
                        .uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodyBold,
                    modifier = Modifier.padding(MaterialTheme.spacing.medium)
                )

                Divider(color = MaterialTheme.colors.dividerColor)
            }
        }
    }

    items(items = settings, key = { it.key }) { setting ->
        CompositionLocalProvider(localAlphaValue) {
            CompositionLocalProvider(LocalTextStyle provides MaterialTheme.typography.bodyLarge) {
                when {
                    setting is FloatingPointSetting && setting.useSlider -> {
                        var value by remember { mutableStateOf(setting.value) }
                        SliderSettingRow(
                            name = setting.displayName,
                            fraction = setting.valueAsFraction(value) ?: 0f,
                            unit = setting.unit,
                            displayedValue = SettingFormatter.format(setting.value),
                            onSettingChange = {
                                value = setting.fractionAsValue(it)
                                onSettingChange(setting.path, setting.fractionAsValue(it))
                            },
                            enabled = enabled
                        )
                    }

                    setting is FloatingPointSetting -> FloatingPointSettingRow(
                        name = setting.displayName,
                        initialValue = setting.value,
                        nonce = settingsNonce,
                        minimum = setting.minimum,
                        maximum = setting.maximum,
                        unit = setting.unit,
                        mandatoryMessage = setting.mandatoryMessage,
                        onSettingChange = { onSettingChange(setting.path, it) },
                        enabled = enabled,
                    )

                    setting is IntegerSetting && setting.useSlider -> {
                        var value by remember { mutableStateOf(setting.value) }
                        SliderSettingRow(
                            name = setting.displayName,
                            fraction = setting.valueAsFraction(value) ?: 0f,
                            unit = setting.unit,
                            displayedValue = value?.toString().orEmpty(),
                            onSettingChange = {
                                value = setting.fractionAsValue(it)
                                onSettingChange(setting.path, setting.fractionAsValue(it))
                            },
                            enabled = enabled
                        )
                    }

                    setting is IntegerSetting -> IntegerSettingRow(
                        name = setting.displayName,
                        initialValue = setting.value,
                        nonce = settingsNonce,
                        minimum = setting.minimum,
                        maximum = setting.maximum,
                        unit = setting.unit,
                        mandatoryMessage = setting.mandatoryMessage,
                        onSettingChange = { onSettingChange(setting.path, it) },
                        enabled = enabled
                    )

                    setting is StringSetting -> StringSettingRow(
                        name = setting.displayName,
                        initialValue = setting.value,
                        nonce = settingsNonce,
                        maxStringLengthBytes = setting.maxStringLengthBytes,
                        mandatoryMessage = setting.mandatoryMessage,
                        onSettingChange = { onSettingChange(setting.path, it) },
                        enabled = enabled
                    )

                    setting is SelectionSetting -> SelectionSettingRow(
                        setting = setting,
                        onSettingChange = { onSettingChange(setting.path, it) },
                        enabled = enabled
                    )

                    setting is BooleanSetting -> {
                        var value by remember { mutableStateOf(setting.value ?: false) }
                        BooleanSettingRow(
                            name = setting.displayName,
                            value = value,
                            onSettingChange = {
                                value = it
                                onSettingChange(setting.path, it)
                            },
                            enabled = enabled
                        )
                    }
                }
            }

            Divider(color = MaterialTheme.colors.dividerColor)
        }
    }
}

@Preview(widthDp = 512, heightDp = 700)
@Composable
private fun SuuntoPlusDisabledSettingsItemsPreview() {
    AppTheme {
        Surface {
            LazyColumn {
                settingsItems(
                    enabled = false,
                    variables = SuuntoPlusSettingsPreviewData.variables,
                    settings = SuuntoPlusSettingsPreviewData.settings,
                    settingsNonce = 0,
                    onSettingChange = { _, _ -> },
                    showWatchNotConnectedMessage = true,
                    showWatchBusyMessage = false,
                )
            }
        }
    }
}

@Preview(widthDp = 512, heightDp = 700)
@Composable
private fun SuuntoPlusSettingsValidationErrorsPreview() {
    AppTheme {
        Surface {
            LazyColumn {
                settingsItems(
                    enabled = true,
                    variables = SuuntoPlusSettingsPreviewData.variables,
                    settings = SuuntoPlusSettingsPreviewData.settingsWithValidationIssues,
                    settingsNonce = 0,
                    onSettingChange = { _, _ -> },
                    showWatchNotConnectedMessage = false,
                    showWatchBusyMessage = true,
                )
            }
        }
    }
}

private fun FloatingPointSetting.valueAsFraction(value: Double?): Float? =
    if (value != null && minimum != null && maximum != null && maximum > minimum) {
        (value.minus(minimum) / maximum.minus(minimum)).toFloat()
    } else {
        null
    }

private fun FloatingPointSetting.fractionAsValue(fraction: Float): Double? =
    if (minimum != null && maximum != null && maximum > minimum) {
        minimum + fraction * maximum.minus(minimum)
    } else {
        null
    }

private fun IntegerSetting.valueAsFraction(value: Int?): Float? =
    if (value != null && minimum != null && maximum != null && maximum > minimum) {
        value.minus(minimum).toFloat() / maximum.minus(minimum).toFloat()
    } else {
        null
    }

private fun IntegerSetting.fractionAsValue(fraction: Float): Int? =
    if (minimum != null && maximum != null && maximum > minimum) {
        (minimum + fraction * maximum.minus(minimum)).roundToInt()
    } else {
        null
    }

private val SuuntoPlusVariable.key: String
    get() = "$path-$displayName"

private val SuuntoPlusSetting.key: String
    get() = "$path-$displayName"
