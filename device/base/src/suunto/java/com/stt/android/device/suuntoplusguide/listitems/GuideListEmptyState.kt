package com.stt.android.device.suuntoplusguide.listitems

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.suuntoplus.ui.R as SuuntoPlusUIR

@Composable
fun GuideListEmptyState(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.smaller),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(id = SuuntoPlusUIR.drawable.ic_watch_guides),
            tint = MaterialTheme.colors.mediumGrey,
            contentDescription = null,
            modifier = Modifier
                .clip(CircleShape)
                .background(MaterialTheme.colors.mediumGrey.copy(alpha = 0.2f))
                .padding(MaterialTheme.spacing.small)
        )

        Text(
            text = stringResource(id = R.string.suunto_plus_guides_empty_state_text),
            color = MaterialTheme.colors.darkGreyText,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.large),
        )
    }
}

@Preview
@Composable
private fun GuideListEmptyStatePreview() {
    AppTheme {
        GuideListEmptyState()
    }
}
