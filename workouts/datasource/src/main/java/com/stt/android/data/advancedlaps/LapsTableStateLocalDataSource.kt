package com.stt.android.data.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.data.graphanalysis.toDomain
import com.stt.android.data.graphanalysis.toLocal
import com.stt.android.data.source.local.advancedlaps.AdvancedLapsSharedPrefStorage
import com.stt.android.data.source.local.advancedlaps.LocalLapsTableDataType
import javax.inject.Inject

class LapsTableStateLocalDataSource
@Inject constructor(
    private val advancedLapsSharedPrefStorage: AdvancedLapsSharedPrefStorage
) {
    fun fetchColumnsState(key: String): List<LapsTableDataType>? =
        advancedLapsSharedPrefStorage.fetchColumnsState(key)
            ?.let { types ->
                types.map { it.toDomain() }
            }

    fun saveColumnsState(key: String, columns: List<LapsTableDataType>) {
        advancedLapsSharedPrefStorage.saveColumnsState(key, columns.map { it.toLocal() })
    }

    fun saveIsLapsTableColouringEnabled(enabled: Boolean) {
        return advancedLapsSharedPrefStorage.saveIsLapsTableColouringEnabled(enabled)
    }

    fun fetchIsLapsTableColouringEnabled(): Boolean {
        return advancedLapsSharedPrefStorage.fetchIsLapsTableColouringEnabled()
    }
}

private fun LocalLapsTableDataType.toDomain(): LapsTableDataType = when (this) {
    is LocalLapsTableDataType.Summary -> LapsTableDataType.Summary(summaryItem)
    is LocalLapsTableDataType.SuuntoPlus -> LapsTableDataType.SuuntoPlus(suuntoPlusChannel.toDomain())
}

private fun LapsTableDataType.toLocal(): LocalLapsTableDataType = when (this) {
    is LapsTableDataType.Summary -> LocalLapsTableDataType.Summary(summaryItem)
    is LapsTableDataType.SuuntoPlus -> LocalLapsTableDataType.SuuntoPlus(suuntoPlusChannel.toLocal())
}
