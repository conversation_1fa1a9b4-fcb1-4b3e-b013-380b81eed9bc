# workouts-remote-api


This module provides remote API for workouts, extensions and smlzip.

## To include it in your project:

```markdown
implementation "com.stt.android.remote:remote-base:1.0.0"
implementation "com.stt.android.remote:workouts-remote-api:1.0.0"
```

## Publishing

Make sure you have Gradle properties `artifactory_user` and `artifactory_password` configured
in your `~/.gradle/gradle.properties` file.

Run the following command to publish:

```bash
./gradlew :workoutsremote:uploadArchives
```

>Before publishing, make sure to increment the version in this module's [gradle.properties](gradle.properties).

