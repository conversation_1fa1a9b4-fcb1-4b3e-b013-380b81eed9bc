package com.stt.android.domain.sml.reader

import androidx.annotation.WorkerThread
import androidx.collection.LruCache
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workouts.extensions.SMLExtension
import com.stt.android.logbook.SmlGZIPUtils

object SmlReader {
    // Limit SML cache size based on number of samples
    private const val SML_CACHE_SIZE_IN_SAMPLES = 50_000

    // Add a small constant to cache size estimation per workout. This is just to ensure that
    // we won't ever have thousands of tiny workouts in the cache.
    private const val SML_CACHE_PER_WORKOUT_CONSTANT = 1_000

    private val smlCache = object : LruCache<Int, Sml>(SML_CACHE_SIZE_IN_SAMPLES) {
        // Cap the result to size of the cache to always keep at least the newest SML in cache
        override fun sizeOf(key: Int, value: Sml) =
            (value.streamData.samplePoint.size + SML_CACHE_PER_WORKOUT_CONSTANT)
                .coerceAtMost(SML_CACHE_SIZE_IN_SAMPLES)
    }

    @WorkerThread
    @JvmStatic
    fun read(smlExtension: SMLExtension?): Sml {
        if (smlExtension == null) return SmlFactory.EMPTY

        return smlCache[smlExtension.workoutId] ?: run {
            smlExtension.smlZip?.let {
                SmlGZIPUtils.gunzipSml(it)
            }?.let {
                SmlFactory.create(
                    it.summaryContent,
                    it.data.samples,
                    it.data.statistics
                ).apply {
                    smlCache.put(smlExtension.workoutId, this)
                }
            } ?: SmlFactory.EMPTY
        }
    }

    fun clearCache() {
        smlCache.evictAll()
    }
}
