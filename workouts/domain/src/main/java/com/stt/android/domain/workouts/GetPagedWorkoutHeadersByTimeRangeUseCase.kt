package com.stt.android.domain.workouts

import com.stt.android.domain.CoroutineUseCase
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetPagedWorkoutHeadersByTimeRangeUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) : CoroutineUseCase<List<WorkoutHeader>, GetPagedWorkoutHeadersByTimeRangeUseCase.Params> {

    override suspend fun run(params: Params): List<WorkoutHeader> = withContext(IO) {
        workoutHeaderDataSource.findPagedByTimeRange(
            ownerUsername = params.username,
            sinceMs = params.fromTimeMs,
            untilMs = params.toTimeMs,
            includeActivityTypeId = params.includeActivityTypeId,
            excludeActivityTypeIds = params.excludeActivityTypeIds,
            page = params.page,
            firstPageSize = params.firstPageSize,
            pageSize = params.pageSize
        )
    }

    data class Params(
        val username: String,
        val fromTimeMs: Long,
        val toTimeMs: Long,
        val page: Int,
        val includeActivityTypeId: Int? = null,
        val excludeActivityTypeIds: Set<Int> = emptySet(),
        val firstPageSize: Int = 5,
        val pageSize: Int = 10
    )
}
