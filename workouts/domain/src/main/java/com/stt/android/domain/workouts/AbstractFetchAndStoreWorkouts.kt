package com.stt.android.domain.workouts

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.ranking.RankingDataSource
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tag.TagConstants.Companion.AUTO_TAGGED_WORKOUT_KEY
import com.stt.android.domain.workouts.tag.TagConstants.Companion.HAS_SHOWN_AUTO_TAGGED_DIALOG
import com.stt.android.domain.workouts.videos.VideoDataSource
import com.stt.android.utils.runSuspendCatchingEach
import timber.log.Timber

abstract class AbstractFetchAndStoreWorkouts(
    private val picturesDataSource: PicturesDataSource,
    private val videoDataSource: VideoDataSource,
    private val reactionDataSource: ReactionSummaryDataSource,
    private val rankingDataSource: RankingDataSource,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val workoutCommentDataSource: WorkoutCommentDataSource,
    private val extensionsDataSource: ExtensionsDataSource,
    private val sharedPreferences: SharedPreferences,
    private val userTagsRepository: UserTagsRepository
) {
    protected suspend fun store(
        workouts: List<DomainWorkout>,
        ownWorkouts: Boolean
    ): List<Result<DomainWorkout>> {
        Timber.d("Storing workouts")
        val hasShownAutoTaggedDialog =
            sharedPreferences.getBoolean(HAS_SHOWN_AUTO_TAGGED_DIALOG, false)

        var latestTaggedWorkoutStartTime: Long = -1
        return workouts.runSuspendCatchingEach { workout ->
            val workoutKey = workout.header.key
            var previouslyStored = false
            val workoutHeader = if (workoutKey != null) {
                val localWorkoutHeader = workoutHeaderDataSource.findByKey(workoutKey)
                if (localWorkoutHeader != null) {
                    previouslyStored = true
                    rankingDataSource.deleteRankings(workoutKey)
                    reactionDataSource.removeReactionsByWorkoutKey(workoutKey)
                    workoutCommentDataSource.removeByWorkoutKey(workoutKey)
                    if (ownWorkouts && !hasShownAutoTaggedDialog) {
                        latestTaggedWorkoutStartTime = checkAutoTagging(
                            remoteWorkoutHeader = workout.header,
                            localWorkoutHeader = localWorkoutHeader,
                            latestTaggedWorkoutStartTime = latestTaggedWorkoutStartTime,
                        )
                    }
                }
                workout.header.copy(
                    id = localWorkoutHeader?.id ?: workout.header.id,
                    seen = localWorkoutHeader?.seen ?: workout.header.seen
                )
            } else {
                workout.header
            }.let { header ->
                if (previouslyStored) {
                    header
                } else {
                    // Make sure header has ascent & descent so other parts of the app don't need
                    // to read the deprecated extension values
                    val summaryExtension =
                        workout.extensions?.get(SummaryExtension::class) as? SummaryExtension
                    @Suppress("DEPRECATION")
                    header.copy(
                        totalAscent = summaryExtension?.ascent ?: header.totalAscent,
                        totalDescent = summaryExtension?.descent ?: header.totalDescent
                    )
                }
            }

            workoutHeaderDataSource.storeWorkout(workoutHeader)

            val storedWorkoutId = workoutHeader.id
            val storedWorkout = workout.copy(
                header = workoutHeader,
                extensions = workout.extensions?.values?.map {
                    it.copyWithNewWorkoutId(
                        storedWorkoutId
                    )
                }?.associateBy { it::class },
                pictures = workout.pictures?.map { it.copy(workoutId = storedWorkoutId) },
                videos = workout.videos?.map { it.copy(workoutId = storedWorkoutId) }
            )

            storedWorkout.extensions?.values?.forEach {
                extensionsDataSource.upsertExtension(
                    storedWorkoutId,
                    it.copyWithNewWorkoutId(storedWorkoutId)
                )
            }

            if (!storedWorkout.comments.isNullOrEmpty()) {
                workoutCommentDataSource.saveComment(workoutKey, storedWorkout.comments)
            }
            if (!storedWorkout.reactions.isNullOrEmpty()) {
                reactionDataSource.storeReactions(storedWorkout.reactions)
            }
            storedWorkout.rankings?.let {
                rankingDataSource.insertRankings(listOf(it))
            }

            val localSyncedPics = if (previouslyStored) {
                picturesDataSource.findByWorkoutId(storedWorkoutId)
                    .filterNot { it.locallyChanged }
                    .associateBy { it.key }
            } else {
                emptyMap()
            }

            if (!storedWorkout.pictures.isNullOrEmpty()) {
                storedWorkout.pictures.forEach { remotePicture ->
                    if (localSyncedPics.containsKey(remotePicture.key)) {
                        val localPic = localSyncedPics[remotePicture.key]
                        picturesDataSource.savePicture(
                            remotePicture.copy(
                                id = localPic?.id,
                                fileName = localPic?.fileName
                            )
                        )
                    } else {
                        picturesDataSource.savePicture(remotePicture)
                    }
                }
                // remove deleted pictures
                val availablePicturesKeys = storedWorkout.pictures.map { it.key }.toSet()
                localSyncedPics.values
                    .filter { it.key !in availablePicturesKeys }
                    .forEach { picturesDataSource.deletePicture(it) }
            } else if (localSyncedPics.isNotEmpty()) {
                localSyncedPics.values
                    .forEach { picturesDataSource.deletePicture(it) }
            }
            if (!storedWorkout.videos.isNullOrEmpty()) {
                val localSyncedVideos = if (previouslyStored) {
                    videoDataSource.findByWorkoutId(storedWorkoutId)
                        .filterNot { it.locallyChanged }
                        .associateBy { it.key }
                } else {
                    emptyMap()
                }

                storedWorkout.videos.forEach { remoteVideo ->
                    if (localSyncedVideos.containsKey(remoteVideo.key)) {
                        val localVideo = localSyncedVideos[remoteVideo.key]
                        videoDataSource.saveVideo(
                            remoteVideo.copy(
                                id = localVideo?.id,
                                filename = localVideo?.filename,
                                thumbnailFilename = localVideo?.thumbnailFilename
                            )
                        )
                    } else {
                        videoDataSource.saveVideo(remoteVideo)
                    }
                }
                // remove deleted videos
                val availableVideosKeys = storedWorkout.videos.map { it.key }.toSet()
                localSyncedVideos.values
                    .filter { it.key !in availableVideosKeys }
                    .forEach { videoDataSource.deleteVideo(it) }
            }

            handleUserTags(storedWorkout)

            storedWorkout
        }
    }

    private suspend fun handleUserTags(
        storedWorkout: DomainWorkout
    ) {
        val storedWorkoutId = storedWorkout.header.id
        userTagsRepository.getAllUserTagsForWorkoutId(workoutId = storedWorkoutId)
            .filter { !it.key.isNullOrBlank() }
            .filter { it.key !in storedWorkout.header.userTags.map { remoteUserTag -> remoteUserTag.key } }
            .forEach {
                userTagsRepository.deleteUserTagsFromAWorkout(
                    workoutId = storedWorkoutId,
                    userTagId = it.id!! // user tag loaded from local
                )
            }

        for (userTag in storedWorkout.header.userTags) {
            userTagsRepository.upsertUserTag(userTag)
            val userTagKey = userTag.key
            if (!userTagKey.isNullOrBlank()) {
                userTagsRepository.addUserTagToWorkoutHeader(
                    workoutId = storedWorkoutId,
                    userTagKey = userTagKey,
                    isSynced = true // records from backend are always considered as synced
                )
            }
        }
    }

    private fun checkAutoTagging(
        remoteWorkoutHeader: WorkoutHeader,
        localWorkoutHeader: WorkoutHeader,
        latestTaggedWorkoutStartTime: Long,
    ): Long {
        if (isAutoTaggedFromBackend(
                remoteWorkoutHeader.suuntoTags,
                localWorkoutHeader.suuntoTags
            )
        ) {
            // Check for the most recent workout and save its key
            if (remoteWorkoutHeader.startTime > latestTaggedWorkoutStartTime) {
                sharedPreferences.edit {
                    // Save flag to show auto tagging dialog, and this will be queried from the activity responsible for showing the dialog
                    putString(AUTO_TAGGED_WORKOUT_KEY, remoteWorkoutHeader.key)
                }
                return remoteWorkoutHeader.startTime
            }
        }
        return latestTaggedWorkoutStartTime
    }

    // Remote workout has commute tag, check for local, if it doesn't have the tag then workout is auto tagged
    private fun isAutoTaggedFromBackend(
        remoteWorkoutSuuntoTags: List<SuuntoTag>,
        localWorkoutSuuntoTags: List<SuuntoTag>
    ) = remoteWorkoutSuuntoTags.contains(SuuntoTag.COMMUTE) &&
        !localWorkoutSuuntoTags.contains(SuuntoTag.COMMUTE)
}
