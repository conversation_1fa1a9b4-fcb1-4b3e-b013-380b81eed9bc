package com.stt.android.domain.workouts

import javax.inject.Inject

/**
 * Use case responsible of storing a workout header
 */
class SaveWorkoutHeaderUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) {
    suspend operator fun invoke(workoutHeader: WorkoutHeader) {
        workoutHeaderDataSource.storeWorkout(workoutHeader.copy(locallyChanged = true))
    }
}
