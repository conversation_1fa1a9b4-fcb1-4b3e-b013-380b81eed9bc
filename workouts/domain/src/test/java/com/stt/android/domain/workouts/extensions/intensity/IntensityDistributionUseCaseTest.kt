package com.stt.android.domain.workouts.extensions.intensity

import com.google.common.truth.Truth.assertThat
import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.extensions.FakeExtensionsDataSource
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class IntensityDistributionUseCaseTest {
    private val fakeExtensionsDataSource = FakeExtensionsDataSource()

    @Mock
    lateinit var workout1: BasicWorkoutHeader

    @Mock
    lateinit var workout2: BasicWorkoutHeader

    lateinit var intensityDistributionUseCase: IntensityDistributionUseCase

    @Before
    fun setUp() {
        intensityDistributionUseCase = IntensityDistributionUseCase(
            GetIntensityExtensionUseCase(fakeExtensionsDataSource),
        )
        whenever(workout1.activityTypeId).thenReturn(CoreActivityType.RUNNING.id)
        whenever(workout1.id).thenReturn(1)
        whenever(workout2.activityTypeId).thenReturn(CoreActivityType.RUNNING.id)
        whenever(workout2.id).thenReturn(2)
    }

    // these test examples refer to documentation in
    // this excel file https://amersportsonline.sharepoint.com/:x:/r/sites/Partnerstotouchpoints/_layouts/15/Doc.aspx?sourcedoc=%7B9710CDF2-3422-43AF-8AEF-315C9D0D0EE5%7D&file=Training%20Insights%20V1.xlsx&action=default&mobileredirect=true

    @Test
    fun calculateIntensityDistribution() = runTest {
        fakeExtensionsDataSource.extensions.apply {
            put(1, listOf(intensity1))
            put(2, listOf(intensity2))
        }
        assertThat(
            intensityDistributionUseCase.calculate(
                workouts = listOf(workout1, workout2),
                avgFactor = 1f
            )
        ).isEqualTo(
            IntensityDistribution(
                hrZones = IntensityDistribution.ZoneDuration(
                    zone1 = 3f,
                    zone2 = 3f,
                    zone3 = 3f,
                    zone4 = 7f,
                    zone5 = 7f
                ),
                speedZones = IntensityDistribution.ZoneDuration(
                    zone1 = 1f,
                    zone2 = 2f,
                    zone3 = 4f,
                    zone4 = 5f,
                    zone5 = 3f
                ),
                runningPowerZones = IntensityDistribution.ZoneDuration(
                    zone1 = 4f,
                    zone2 = 2f,
                    zone3 = 4f,
                    zone4 = 6f,
                    zone5 = 8f
                ),
                cyclingPowerZones = IntensityDistribution.ZoneDuration(
                    zone1 = 0f,
                    zone2 = 0f,
                    zone3 = 0f,
                    zone4 = 0f,
                    zone5 = 0f
                )
            )
        )
    }

    private val intensity1 = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                1f, 2f, 3f, 4f, 5f,
                0f, 0f, 0f, 0f
            ),
            speed = IntensityZonesData(
                1f, 2f, 4f, 5f, 3f,
                0f, 0f, 0f, 0f
            ),
            power = IntensityZonesData(
                3f, 2f, 1f, 5f, 4f,
                0f, 0f, 0f, 0f
            ),
        ),
    )

    private val intensity2 = IntensityExtension(
        workoutId = 2,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                2f, 1f, 0f, 3f, 2f,
                0f, 0f, 0f, 0f
            ),
            speed = null,
            power = IntensityZonesData(
                1f, 0f, 3f, 1f, 4f,
                0f, 0f, 0f, 0f
            ),
        ),
    )
}
