package com.stt.android.domain.sml.reader

import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.Sml
import com.stt.android.logbook.SmlFromJson
import com.stt.android.logbook.SmlParser
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.util.zip.GZIPInputStream
import kotlin.math.roundToInt

@RunWith(MockitoJUnitRunner::class)
class SmlFactoryTest {

    private lateinit var sml: Sml
    private val parser = SmlParser()

    @Before
    fun setup() {
    }

    private fun snowboardingSml(): Sml {
        val smlFromJson: SmlFromJson = parser.parseSml(
            GZIPInputStream(
                javaClass.classLoader?.getResourceAsStream("sml/reader/snowboarding_with_pauses.gzipped")
            )
        )
        return SmlFactory.create(smlFromJson.summaryContent, smlFromJson.data.samples, smlFromJson.data.statistics)
    }

    private fun diveWithoutStatusEventsSml(): Sml {
        val smlFromJson: SmlFromJson = parser.parseSml(
            GZIPInputStream(
                javaClass.classLoader?.getResourceAsStream("sml/reader/dive_no_status_events.gzipped")
            )
        )
        return SmlFactory.create(smlFromJson.summaryContent, smlFromJson.data.samples, smlFromJson.data.statistics)
    }

    @Test
    fun `test creation of recording status events`() {
        sml = snowboardingSml()

        assertEquals(
            7, // 7 recording status changes [start, pause, resume, pause, resume, pause, stop]
            sml
                .streamData
                .events
                .mapNotNull { it as? RecordingStatusEvent }
                .count()
        )
    }

    @Test
    fun `test creation of events`() {
        sml = snowboardingSml()

        assertEquals(
            80, // 80 events, .json has 81, but one is an empty array, should be discarded
            sml
                .streamData
                .events
                .count()
        )
    }

    @Test
    fun `test event duration without pauses calculation`() {
        sml = snowboardingSml()

        // Duration from the watch
        val durationFromActivityWindow: Float = sml.getActivityWindow(null)!!.duration!!
        // Duration calculated by SmlFactory
        val durationFromFinalEvent = sml
            .streamData
            .events
            .last()
            .data
            .duration!!

        // Round to same precision
        val rounded: Float = (durationFromFinalEvent.toFloat() / 100f).roundToInt() / 10f
        // Compare with the duration inside "Activity window"
        assertEquals(
            durationFromActivityWindow,
            rounded
        )
    }

    @Test
    fun `test event elapsed calculation`() {
        sml = snowboardingSml()

        val events = sml
            .streamData
            .events

        val firstEvent = events.first()
        val lastEvent = events.last()

        assertEquals(
            lastEvent.data.timestamp - firstEvent.data.timestamp,
            lastEvent.data.elapsed
        )
    }

    @Test
    fun `test status events created for dives`() {
        // Dives do not have status events as per design
        sml = diveWithoutStatusEventsSml()

        assertEquals(
            33, // .json has 31 plus we add the start and stop status events
            sml
                .streamData
                .events
                .count()
        )
    }
}
