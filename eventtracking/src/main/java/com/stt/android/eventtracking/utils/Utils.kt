package com.stt.android.eventtracking.utils

import android.util.Base64
import org.json.JSONObject
import java.math.BigInteger
import java.security.MessageDigest
import java.util.UUID
import java.util.concurrent.TimeUnit
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

private const val HMAC_ALGORITHM = "HmacSHA256"

private fun ByteArray.base64Encode(): String {
    return Base64.encodeToString(this, Base64.URL_SAFE or Base64.NO_WRAP or Base64.NO_PADDING)
}

private fun String.base64Encode(): String = toByteArray().base64Encode()

private fun String.hs256(secret: String): String {
    val keySpec = SecretKeySpec(secret.toByteArray(), HMAC_ALGORITHM)
    val mac = Mac.getInstance(HMAC_ALGORITHM)
    mac.init(keySpec)
    return mac.doFinal(toByteArray()).base64Encode()
}

internal fun String.md5(): String {
    val md = MessageDigest.getInstance("MD5")
    val md5Data = BigInteger(1, md.digest(toByteArray()))
    return String.format("%032x", md5Data)
}

internal fun currentTimestamp(): Long {
    return System.currentTimeMillis()
}

internal fun uuidString(): String {
    return UUID.randomUUID().toString()
}

internal fun jwt(secret: String, deviceId: String, correctionMillis: Long): String {
    val header = JSONObject().apply {
        put("alg", "HS256")
        put("typ", "JWT")
    }.toString().base64Encode()

    val millis = currentTimestamp()
    val offsetMillis = TimeUnit.MINUTES.toMillis(10L)
    val iat = TimeUnit.MILLISECONDS.toSeconds(millis + correctionMillis - offsetMillis)
    val exp = TimeUnit.MILLISECONDS.toSeconds(millis + correctionMillis + offsetMillis)
    val payload = JSONObject().apply {
        put("jti", "suunto-event")
        put("aud", "suuntoapp")
        put("iss", "suunto")
        put("iat", iat)
        put("exp", exp)
        put("deviceId", deviceId)
    }.toString().base64Encode()

    val signature = "$header.$payload".hs256(secret)

    return "$header.$payload.$signature"
}
