plugins {
    id "stt.android.plugin.library"
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
    id "stt.android.plugin.moshi"
    alias libs.plugins.androidx.navigation.safeargs
}

android {
    namespace 'com.stt.android'

    buildFeatures {
        dataBinding true
        buildConfig = true
    }

    libraryVariants.all { variant ->
        if (variant.productFlavors.get(0).name.equals("sportstracker")) {
            if (variant.buildType.name.equals("debug")) {
                resValue "string", "appId", "com.stt.android.debug"
                resValue "string", "app_name", "ST-" + rootProject.getWorkingBranch()
            } else if (variant.buildType.name.equals("release")) {
                resValue "string", "appId", "com.stt.android"
                resValue "string", "app_name", "Sports Tracker"
            }
        } else if (variant.productFlavors.get(0).name.equals("suunto") && variant.productFlavors.get(1).name.equals("china")) {
            if (variant.buildType.name.equals("debug")) {
                resValue "string", "appId", "com.stt.android.suunto.china.debug"
                resValue "string", "app_name", "SuuntoChina-" + rootProject.getWorkingBranch()
            } else if (variant.buildType.name.equals("release")) {
                resValue "string", "appId", "com.stt.android.suunto.china"
                resValue "string", "app_name", "Suunto"
            }
        } else if (variant.productFlavors.get(0).name.equals("suunto") && variant.productFlavors.get(1).name.equals("playstore")) {
            if (variant.buildType.name.equals("debug")) {
                resValue "string", "appId", "com.stt.android.suunto.debug"
                resValue "string", "app_name", "Suunto-" + rootProject.getWorkingBranch()
            } else if (variant.buildType.name.equals("release")) {
                resValue "string", "appId", "com.stt.android.suunto"
                resValue "string", "app_name", "Suunto"
            }
        }
    }

    buildTypes {
        debug {
            resValue "string", "fb_app_id", "com.facebook.app.FacebookContentProvider478823072139720"
        }
        release {
            resValue "string", "fb_app_id", "com.facebook.app.FacebookContentProvider452329015213"
        }
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
            all {
                if (JavaVersion.current().isJava8Compatible()) {
                    // option MaxPermSize support was removed in 8.0
                    jvmArgs "-XX:MaxMetaspaceSize=768m"
                } else {
                    jvmArgs "-XX:MaxPermSize=768m"
                }
            }
        }
    }
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.composeUi)
    implementation project(Deps.location)
    implementation project(Deps.bluetooth)
    implementation project(Deps.divetrack)
    implementation project(Deps.timeline)
    implementation project(Deps.graphlib)
    implementation project(Deps.infoModel)
    implementation project(Deps.analytics)
    implementation project(Deps.remote)
    implementation project(Deps.datasource)
    implementation project(Deps.persistence)
    implementation project(Deps.domain)
    implementation project(Deps.remoteBase)
    implementation project(Deps.workoutsRemote)
    implementation project(Deps.sessionDomain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.workoutsDataSource)
    implementation project(Deps.userRemote)
    implementation project(Deps.userDomain)
    implementation project(Deps.userDataSource)
    implementation project(Deps.menstrualCycleDomain)
    implementation project(Deps.menstrualCycleRemote)
    implementation project(Deps.menstrualCycleDatasource)
    implementation project(Deps.tutorialApi)
    chinaImplementation project(Deps.analyticschina)
    implementation project(Deps.croplib)
    implementation project(Deps.remoteConfigApi)
    implementation project(Deps.featureToggleApi)

    // uncomment to enable Tencent SDK in debug
    // debugApi project(Deps.tencentAnalytics)

    suuntoImplementation project(Deps.connectivity)
    implementation project(Deps.sharingPlatform)

    implementation project(Deps.chartApi)
    implementation project(Deps.nfcApi)

    // -- BEGIN DEPENDENCIES FOR SUUNTO CONNECTIVITY LIBRARY --
    // Greenrobot EventBus
    // related proguard file: proguard-greenrobot-eventbus.pro
    suuntoImplementation libs.eventbus
    // JDeferred for inversion of control in simple async operations
    // Related proguard file: proguard-jdeferred.pro
    suuntoImplementation libs.jdeferred
    suuntoImplementation project(Deps.mds)
    // -- END DEPENDENCIES FOR SUUNTO CONNECTIVITY LIBRARY --

    // SIM formatter
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }

    // AndroidX libraries
    implementation libs.androidx.gridlayout
    implementation libs.androidx.recyclerview
    implementation libs.androidx.percentlayout
    implementation libs.androidx.cardview
    implementation libs.androidx.preference
    implementation libs.androidx.sqlitektx
    implementation libs.androidx.collection
    implementation libs.androidx.lifecycle.service
    implementation libs.androidx.lifecycle.process
    implementation libs.androidx.lifecycle.livedata.ktx
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.lifecycle.reactive.streams.ktx
    implementation libs.androidx.lifecycle.common.java8
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.androidx.compose.runtime.livedata
    implementation libs.androidx.savedstate
    implementation libs.androidx.exif
    implementation libs.androidx.paging
    implementation libs.androidx.paging.compose
    implementation libs.androidx.window
    implementation libs.androidx.splashscreen
    implementation libs.androidx.viewpager2
    implementation libs.androidx.dynamicanimation
    implementation libs.androidx.tracing

    // End Androidx libraries

    implementation libs.gravity.snap.helper

    // Google Play services
    // Base also provides: Google Actions
    // See also: https://developer.android.com/google/play-services/setup.html#table1
    implementation libs.play.base
    implementation libs.play.location
    implementation libs.play.maps
    implementation libs.play.wearable
    implementation libs.play.auth
    sportstrackerImplementation libs.play.billing
    sportstrackerImplementation libs.play.billing.ktx
    // End Google Play services

    // Firebase
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.analytics
    implementation libs.firebase.messaging
    // let's add firebase in-app messaging only in global apps
    playstoreImplementation libs.firebase.in.app.messaging
    // End Firebase

    // Google Map Android implementation utilities: https://github.com/googlemaps/android-maps-utils
    implementation libs.maps.utils
    // End Google Map Android implementation utilities

    // Maps abstraction layer
    implementation project(Deps.maps)
    implementation project(Deps.mapsProviderMapbox)
    implementation libs.mapbox.search

    // AMap 3dMap
    chinaImplementation project(Deps.mapsProviderAmap)

    sportstrackerImplementation project(Deps.mapsProviderGoogle)
    // End Maps abstraction layer

    // ORM: http://ormlite.com/
    implementation libs.ormlite
    // End ORM

    // Gson: https://github.com/google/gson
    implementation libs.gson
    // End Gson

    // Facebook SDK: https://github.com/facebook/facebook-android-sdk
    implementation libs.facebook.sdk
    // End Facebook SDK

    // Networking: https://github.com/square/okhttp
    implementation libs.okhttp
    // End networking

    // Chart library: https://github.com/PhilJay/MPAndroidChart
    implementation libs.mpandroid
    // End chart library

    // RxAndroid https://github.com/ReactiveX/RxAndroid
    implementation libs.rxandroid
    // And ProGuard rules for RxJava!
    implementation libs.rxjava.proguard
    // End RxAndroid

    implementation libs.rxjava2
    implementation libs.rxjava2.android
    // End RxJava2

    // RxRelay
    implementation libs.rxrelay

    // Simple Tool Tip https://github.com/xizzhu/simple-tool-tip
    implementation libs.simple.tooltip
    // End Simple Tool Tip

    // Google's libphonenumber lib for phone validation
    implementation libs.phone.number

    implementation libs.firebase.crashlytics

    // http://www.helpshift.com
    implementation libs.helpshift
    // End Helpshift

    // Photo View https://github.com/chrisbanes/PhotoView
    implementation libs.photoview
    // End Photo View

    implementation libs.glide.disklrucache

    // Easy permissions https://github.com/googlesamples/easypermissions
    implementation libs.easypermissions
    // End Easy permissions https://github.com/googlesamples/easypermissions

    // Lottie https://github.com/airbnb/lottie-android
    implementation libs.lottie
    implementation libs.lottie.compose

    // ExoPlayer https://github.com/google/ExoPlayer
    implementation libs.exoplayer
    // End ExoPlayer https://github.com/google/ExoPlayer

    // Koptional https://github.com/gojuno/koptional
    implementation libs.koptional

    // Groupie for recycle views
    implementation libs.groupie.databinding
    implementation libs.groupie
    implementation libs.groupie.kotlin

    // DiscreteScrollView
    implementation libs.discrete.scrollview

    implementation libs.circleindicator

    // Duktape
    suuntoImplementation libs.duktape

    // Android FAB speed dial menu
    implementation libs.androidfab

    // more advanced links in TextViews
    implementation libs.better.link.movement.method

    // MapBox services for geocoding and reverse geocoding
    implementation libs.mapbox.services

    implementation libs.play.in.app.review

    // TSS calculations
    implementation libs.soy.algorithms

    implementation libs.transcoder

    // OSSLicenses
    implementation libs.play.osslicenses

    implementation libs.coil
    implementation libs.coil.compose
    implementation libs.coil.gif
    implementation libs.coil.okhttp
    implementation libs.coil.video

    implementation libs.androidx.work
    implementation libs.emarsys
    implementation libs.emarsys.firebase

    implementation libs.flexbox

    implementation libs.commonmark

    // https://github.com/tyzlmjj/TinyPinyin
    chinaImplementation libs.tinypinyin

    suuntoImplementation libs.barcode.scanning
    implementation libs.androidx.camera.camera2
    implementation libs.androidx.camera.lifecycle
    implementation libs.androidx.camera.view

    implementation(libs.androidx.glance.appwidget)
    playstoreImplementation(libs.jsbridge)

    implementation libs.vico
    implementation libs.telephoto
}
