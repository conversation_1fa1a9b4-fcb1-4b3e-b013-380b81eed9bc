package com.stt.android.domain.summaries;

import android.content.res.Resources;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.domain.localization.Localizable;

public enum SummaryHighlightedProperty implements Localizable {
    TOTALS(R.string.totals, false, AnalyticsPropertyValue.SummaryHighlightedProperty.TOTALS),
    TIME(R.string.duration, true, AnalyticsPropertyValue.SummaryHighlightedProperty.TIME),
    DISTANCE(R.string.distance, true, AnalyticsPropertyValue.SummaryHighlightedProperty.DISTANCE),
    ENERGY(R.string.energy, true, AnalyticsPropertyValue.SummaryHighlightedProperty.ENERGY),
    AVG_HR(R.string.avg_heart_rate, true, AnalyticsPropertyValue.SummaryHighlightedProperty.AVG_HR),
    AVG_SPEED(R.string.avg_speed, true, AnalyticsPropertyValue.SummaryHighlightedProperty.AVG_SPEED),
    AVG_PACE(R.string.avg_pace, true, AnalyticsPropertyValue.SummaryHighlightedProperty.AVG_PACE),
    WORKOUTS(R.string.workouts, true, AnalyticsPropertyValue.SummaryHighlightedProperty.WORKOUTS),
    ASCENT(com.stt.android.core.R.string.all_ascent, true, AnalyticsPropertyValue.SummaryHighlightedProperty.ASCENT);
    public static final SummaryHighlightedProperty DEFAULT = TOTALS;

    private final int stringId;
    private final boolean compactMode;
    private final String analyticsPropertyValue;

    SummaryHighlightedProperty(int stringId, boolean compactMode, String analyticsPropertyValue) {
        this.stringId = stringId;
        this.compactMode = compactMode;
        this.analyticsPropertyValue = analyticsPropertyValue;
    }

    public boolean isCompactMode() {
        return compactMode;
    }

    @Override
    public String toString(@NonNull Resources resources) {
        String string = resources.getString(stringId);
        if (!Character.isUpperCase(string.charAt(0))) {
            string = string.substring(0, 1).toUpperCase() + string.substring(1);
        }
        return string;
    }

    public String getAnalyticsPropertyValue() {
        return analyticsPropertyValue;
    }

    @Nullable
    public static SummaryHighlightedProperty fromOrdinal(int ordinal) {
        SummaryHighlightedProperty[] allValues = values();
        if (ordinal >= 0 && ordinal < allValues.length) {
            return allValues[ordinal];
        } else {
            return null;
        }
    }
}
