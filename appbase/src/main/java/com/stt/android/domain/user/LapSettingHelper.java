package com.stt.android.domain.user;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import android.text.TextUtils;
import com.stt.android.laps.Laps;
import com.stt.android.utils.STTConstants;
import java.util.Map;

public class LapSettingHelper {
    public static final Laps.Type DEFAULT_LAP = Laps.Type.ONE;

    // this is used to represents lap setting for all activities
    private static final int ALL_ACTIVITY_ID = -1;

    private static String buildKey(int activityTypeId) {
        return STTConstants.DefaultPreferences.KEY_LAP_SETTING_PREFIX + activityTypeId;
    }

    public static Laps.Type readLapType(Context context, int activityTypeId) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        String value = preferences.getString(buildKey(activityTypeId), null);
        if (TextUtils.isEmpty(value)) {
            value = preferences.getString(buildKey(ALL_ACTIVITY_ID), null);
        }
        return TextUtils.isEmpty(value) ? DEFAULT_LAP : Laps.Type.valueOf(value);
    }

    public static void saveLapType(Context context, int activityTypeId, Laps.Type lapType) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();
        if (activityTypeId == ALL_ACTIVITY_ID) {
            // when the user applies settings to all, we just remove all the lap settings first
            for (Map.Entry<String, ?> entry : preferences.getAll().entrySet()) {
                String key = entry.getKey();
                if (key.startsWith("lap_")) {
                    editor.remove(key);
                }
            }
        }
        editor.putString(buildKey(activityTypeId), lapType.toString()).apply();
    }
}
