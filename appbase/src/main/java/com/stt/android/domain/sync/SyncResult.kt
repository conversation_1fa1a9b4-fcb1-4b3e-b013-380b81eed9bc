package com.stt.android.domain.sync

// null means that that type of sync was not run
data class SyncResult(
    val pushResults: List<Result<Pair<Any, String>>>?,
    val pullOwnWorkoutsResult: Result<Unit>?,
    val pullFolloweesResult: Result<Unit>?,
    val pullFolloweesWorkoutsResult: Result<Unit>?,
    val pullFeedResult: Result<Unit>?,
    val cancelled: Boolean
) {
    val pushErrors: List<Throwable>
        get() = pushResults?.mapNotNull { it.exceptionOrNull() } ?: emptyList()
}
