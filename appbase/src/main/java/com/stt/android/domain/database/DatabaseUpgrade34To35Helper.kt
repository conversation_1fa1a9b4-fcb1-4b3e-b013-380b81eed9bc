package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import java.sql.SQLException

class DatabaseUpgrade34To35Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(
            db,
            DatabaseUpgrade49To50Helper.SUMMARY_EXTENSION_TABLE_NAME,
            DatabaseUpgrade49To50Helper.DbFields.ASCENT
        )
        DatabaseHelper.addColumnIfNotExist(
            db,
            DatabaseUpgrade49To50Helper.SUMMARY_EXTENSION_TABLE_NAME,
            DatabaseUpgrade49To50Helper.DbFields.DESCENT
        )
        DatabaseHelper.addColumnIfNotExist(
            db,
            DatabaseUpgrade49To50Helper.SUMMARY_EXTENSION_TABLE_NAME,
            DatabaseUpgrade49To50Helper.DbFields.RECOVERY_TIME
        )
    }
}
