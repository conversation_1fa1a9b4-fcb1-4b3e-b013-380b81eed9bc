package com.stt.android.domain.summaries;

import android.content.res.Resources;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.domain.localization.Localizable;

public enum SummaryTimeFrameUnit implements Localizable {
    WEEKLY(R.string.weekly, AnalyticsPropertyValue.DiaryCalendarGranularity.WEEKLY),
    MONTHLY(R.string.monthly, AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY);

    public static final SummaryTimeFrameUnit DEFAULT = MONTHLY;
    private final int stringId;
    private final String analyticsPropertyValue;

    SummaryTimeFrameUnit(int stringId, String analyticsPropertyValue) {
        this.stringId = stringId;
        this.analyticsPropertyValue = analyticsPropertyValue;
    }

    @Override
    public String toString(@NonNull Resources resources) {
        return resources.getString(stringId);
    }

    public String getAnalyticsPropertyValue() {
        return analyticsPropertyValue;
    }

    @Nullable
    public static SummaryTimeFrameUnit fromOrdinal(int ordinal) {
        SummaryTimeFrameUnit[] allValues = values();
        if (ordinal >= 0 && ordinal < allValues.length) {
            return allValues[ordinal];
        } else {
            return null;
        }
    }
}
