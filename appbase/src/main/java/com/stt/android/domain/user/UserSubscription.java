package com.stt.android.domain.user;

import android.text.format.DateUtils;

import androidx.annotation.NonNull;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.lang.reflect.Type;

/**
 * Represents a subscription owned by a user.
 */
public class UserSubscription implements Serializable {
    private static final long serialVersionUID = 2530215081045312089L;
    @SerializedName(value = "type")
    private final SubscriptionInfo.SubscriptionType type;
    @SerializedName(value = "length")
    private final SubscriptionInfo.SubscriptionLength length;
    @SerializedName(value = "daysLeft")
    private final int daysLeft;
    @SerializedName(value = "autoRenew")
    private final boolean autoRenew;
    /**
     * When this subscription information was fetched. Hidden from Json and populated on constructor
     */
    @Expose(deserialize = false, serialize = false)
    private final long timestamp;

    public UserSubscription(
        SubscriptionInfo.SubscriptionType subscriptionType,
        SubscriptionInfo.SubscriptionLength subscriptionLength,
        int daysLeft,
        boolean autoRenew,
        long timestamp
    ) {
        type = subscriptionType;
        length = subscriptionLength;
        this.daysLeft = daysLeft;
        this.autoRenew = autoRenew;
        this.timestamp = timestamp;
    }

    private UserSubscription(SubscriptionInfo.SubscriptionType subscriptionType,
        SubscriptionInfo.SubscriptionLength subscriptionLength, int daysLeft, boolean autoRenew) {
        type = subscriptionType;
        length = subscriptionLength;
        this.daysLeft = daysLeft;
        this.autoRenew = autoRenew;
        this.timestamp = System.currentTimeMillis();
    }

    public SubscriptionInfo.SubscriptionType getType() {
        return type;
    }

    public int getDaysLeft() {
        return daysLeft;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public SubscriptionInfo.SubscriptionLength getLength() {
        return length;
    }

    /**
     * If it's {@link #autoRenew} subscription then it's valid,  nothing else is checked. If not
     * auto renew then we check the amount of {@link #daysLeft}
     *
     * @return true if subscription is currently valid. Otherwise, false.
     */
    public boolean isValid() {
        return autoRenew || (daysLeft >= 0 && timestamp + (1 + daysLeft) * DateUtils
                .DAY_IN_MILLIS >= System.currentTimeMillis());
    }

    public boolean isPremium() {
        return type == SubscriptionInfo.SubscriptionType.ACTIVE || type == SubscriptionInfo.SubscriptionType.IN_GRACE_PERIOD;
    }

    @NonNull
    @Override
    public String toString() {
        return "UserSubscription["
            + "type=" + type
            + ", length=" + length
            + ", daysLeft=" + daysLeft
            + ", autoRenew=" + autoRenew
            + "]";
    }

    public static class Deserializer implements JsonDeserializer<UserSubscription> {
        @Override
        public UserSubscription deserialize(JsonElement json, Type typeOfT,
                                            JsonDeserializationContext context) throws
                JsonParseException {
            JsonObject jsonObject = json.getAsJsonObject();
            String subscriptionTypeStr = context.deserialize(jsonObject.get("type"), String.class);
            SubscriptionInfo.SubscriptionType subscriptionType = SubscriptionInfo
                    .SubscriptionType.forValue(subscriptionTypeStr);
            String subscriptionLengthStr = context.deserialize(jsonObject.get("length"),
                    String.class);
            SubscriptionInfo.SubscriptionLength subscriptionLength = SubscriptionInfo
                    .SubscriptionLength.forValue(subscriptionLengthStr);
            int daysLeft = context.deserialize(jsonObject.get("daysLeft"), int.class);
            boolean autoRenew = context.deserialize(jsonObject.get("autoRenew"), boolean.class);
            return new UserSubscription(subscriptionType, subscriptionLength, daysLeft, autoRenew);
        }
    }
}

