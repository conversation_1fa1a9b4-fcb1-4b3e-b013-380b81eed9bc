package com.stt.android.home.diary.diarycalendar.bubbles

import java.time.LocalDate
import java.time.YearMonth

/**
 * Holds bubble data for a time period
 */
data class DiaryBubbleData(
    val startDate: LocalDate,
    val endDate: LocalDate,
    val bubbles: List<DiaryBubbleContainer>,
    val weekDayLabels: List<String>,
    val onMonthClicked: (YearMonth) -> Unit, // Don't pass lambda to ensure equals() works as expected
    val isClickable: Boolean = true
) {
    companion object {
        val EMPTY = DiaryBubbleData(
            startDate = LocalDate.now(),
            endDate = LocalDate.now(),
            bubbles = emptyList(),
            weekDayLabels = emptyList(),
            onMonthClicked = {}
        )
    }
}
