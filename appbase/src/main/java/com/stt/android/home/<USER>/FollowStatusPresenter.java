package com.stt.android.home.people;

import androidx.annotation.NonNull;
import android.view.View;
import com.stt.android.domain.STTErrorCodes;
import com.stt.android.exceptions.BackendException;
import com.stt.android.follow.FollowDirection;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.presenters.MVPPresenter;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

public abstract class FollowStatusPresenter<V extends FollowStatusView> extends MVPPresenter<V> {
    protected final PeopleController peopleController;
    /**
     * Observable subscribes to UserFollowStatus updates either in
     * {@link FollowDirection#FOLLOWING} or {@link FollowDirection#FOLLOWER} direction.
     * See {@link PeopleModule}
     */
    private final Observable<UserFollowStatus> followStatusObservable;

    public FollowStatusPresenter(PeopleController peopleController,
        Observable<UserFollowStatus> followStatusObservable) {
        this.peopleController = peopleController;
        this.followStatusObservable = followStatusObservable;
    }

    @Override
    protected void onViewTaken() {
        super.onViewTaken();
        subscription.add(
            followStatusObservable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<UserFollowStatus>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                        Timber.w(e, "Unable to handle follow status update");
                    }

                    @Override
                    public void onNext(UserFollowStatus userFollowStatus) {
                        onUserFollowStatusUpdate(userFollowStatus);
                    }
                })
        );
    }

    protected abstract void onUserFollowStatusUpdate(UserFollowStatus userFollowStatus);

    public void showUserProfile(@NonNull String username) {
        V view = getView();
        if (view != null) {
            view.showUserProfile(username);
        }
    }

    public void askToUnfollow(UserFollowStatus userFollowStatus) {
        V view = getView();
        if (view != null) {
            view.showUnfollowDialog(userFollowStatus);
        }
    }

    public void unfollow(final UserFollowStatus userFollowStatus) {
        V view = getView();
        if (view != null) {
            view.showFollowActionSpinner(userFollowStatus);
        }

        subscription.add(
            peopleController.unfollow(userFollowStatus)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(() -> {
                    // Nothing to do
                }, throwable -> {
                    V view1 = getView();
                    if (view1 != null) {
                        View.OnClickListener tryAgainAction = v -> unfollow(userFollowStatus);
                        view1.showActionError(userFollowStatus, tryAgainAction);
                    }
                })
        );
    }

    public void follow(final UserFollowStatus userFollowStatus,
        final String viewSourceForAnalytics) {
        V view = getView();
        if (view != null) {
            view.showFollowActionSpinner(userFollowStatus);
        }

        subscription.add(
            peopleController.follow(userFollowStatus, viewSourceForAnalytics)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(() -> {
                    // Nothing to do
                }, throwable -> {
                    BackendException backendException = throwable instanceof BackendException
                        ? (BackendException)throwable
                        : null;

                    //if user is still in friends model and not yet migrated to follow via sync
                    // backend returns null payload. Let's log this
                    if (backendException != null && backendException.getError() == STTErrorCodes.UNKNOWN) {
                        Boolean isUserInFollowModel =
                            peopleController.currentUserController.getCurrentUser().getFollowModel();
                        if (isUserInFollowModel == null || !isUserInFollowModel) {
                            Timber.w(throwable, "User pressed follow while still in friends model");
                        }
                    }

                    V view1 = getView();
                    if (view1 != null) {
                        if (backendException != null &&
                            backendException.getError() == STTErrorCodes.TOO_MANY_FOLLOW_REQUESTS) {
                            view1.showError(backendException.getError());
                        } else {
                            View.OnClickListener tryAgainAction =
                                v -> follow(userFollowStatus, viewSourceForAnalytics);
                            view1.showActionError(userFollowStatus, tryAgainAction);
                        }
                    }
                })
        );
    }

    void onMultiSelectionModeStarted() {
        // Implemented in FollowersPresenter
    }

    void onSelectionCountUpdated(int count) {
        // Implemented in FollowersPresenter
    }

    void switchToEmptyView() {
        V view = getView();
        if (view != null) {
            view.showEmptyView();
        }
    }

    boolean isUserLoggedIn() {
        return peopleController.currentUserController.isLoggedIn();
    }

    boolean isCurrentUserUfs(UserFollowStatus userFollowStatus) {
        return peopleController.isCurrentUserUfs(userFollowStatus);
    }
}
