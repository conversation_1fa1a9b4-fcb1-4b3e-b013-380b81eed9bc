package com.stt.android.home.people;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.home.people.widgets.FollowStatusWidget;

public class FollowStatusViewHolder extends RecyclerView.ViewHolder {
    private FollowStatusWidget followStatusWidget;

    public FollowStatusViewHolder(View itemView, FollowStatusWidget.Listener listener) {
        super(itemView);
        followStatusWidget = (FollowStatusWidget) itemView;
        followStatusWidget.setListener(listener);
    }

    public void bind(UserFollowStatus status, boolean selectionMode, boolean selected) {
        followStatusWidget.setUserFollowStatus(status, selectionMode, selected);
    }

    public void showActionSpinner() {
        followStatusWidget.showActionSpinner();
    }

    public void hideActions() {
        followStatusWidget.hideActions();
    }
}
