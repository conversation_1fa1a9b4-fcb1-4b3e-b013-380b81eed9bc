package com.stt.android.home.workouts

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.commit
import com.stt.android.R
import com.stt.android.databinding.ActivityDiarySummariesBinding
import com.stt.android.home.diary.DiarySummariesFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DiaryTotalsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val binding = ActivityDiarySummariesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.diarySummariesToolbar)
        supportActionBar?.setTitle(R.string.totals)

        if (supportFragmentManager.findFragmentByTag(DIARY_SUMMARIES_FRAGMENT_TAG) != null) {
            return
        }
        val diarySummariesFragment = DiarySummariesFragment
            .newInstance(intent.getStringExtra(ARG_INITIAL_FILTER_VALUE).orEmpty())
        supportFragmentManager.commit {
            replace(
                R.id.diary_summaries_fragment,
                diarySummariesFragment,
                DIARY_SUMMARIES_FRAGMENT_TAG
            )
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressedDispatcher.onBackPressed()
        return true
    }

    companion object {
        fun newStartIntent(context: Context): Intent {
            return Intent(context, DiaryTotalsActivity::class.java)
        }

        fun newStartIntent(context: Context, initialFilterValue: String): Intent {
            return Intent(context, DiaryTotalsActivity::class.java)
                .putExtra(ARG_INITIAL_FILTER_VALUE, initialFilterValue)
        }

        private const val ARG_INITIAL_FILTER_VALUE =
            "DiarySummariesActivity.ARG_INITIAL_FILTER_VALUE"
        private const val DIARY_SUMMARIES_FRAGMENT_TAG = "DiarySummariesFragment"
    }
}
