package com.stt.android.home.dashboard.widget

import com.stt.android.ui.map.selection.MyTracksGranularity

enum class WidgetType(
    val granularity: MyTracksGranularity.Type?,
    val isDynamic: Boolean,
    /**
     * If null, not reported to analytics. These are mainly widgets that user can't place
     * themselves, like the old dynamic ones or the temporary add new widget
     */
    val analyticsName: String?
) {
    TOTAL_DURATION_THIS_WEEK(MyTracksGranularity.Type.THIS_WEEK, false, "DurationThisWeek"),
    TOTAL_DURATION_THIS_MONTH(MyTracksGranularity.Type.THIS_MONTH, false, "DurationThisMonth"),
    TOTAL_DURATION_LAST_30_DAYS(MyTracksGranularity.Type.LAST_30_DAYS, false, "DurationLast30Days"),
    CALENDAR_THIS_WEEK(MyTracksGranularity.Type.THIS_WEEK, false, "CalendarThisWeek"),
    CALENDAR_THIS_MONTH(MyTracksGranularity.Type.THIS_MONTH, false, "CalendarThisMonth"),
    CALENDAR_LAST_30_DAYS(MyTracksGranularity.Type.LAST_30_DAYS, false, "CalendarLast30Days"),
    MAP_THIS_WEEK(MyTracksGranularity.Type.THIS_WEEK, false, "MapThisWeek"),
    MAP_THIS_MONTH(MyTracksGranularity.Type.THIS_MONTH, false, "MapThisMonth"),
    MAP_LAST_30_DAYS(MyTracksGranularity.Type.LAST_30_DAYS, false, "MapLast30Days"),
    DURATION_BY_ACTIVITY_GROUP_THIS_WEEK(MyTracksGranularity.Type.THIS_WEEK, false, "ActivitiesThisWeek"),
    DURATION_BY_ACTIVITY_GROUP_THIS_MONTH(MyTracksGranularity.Type.THIS_MONTH, false, "ActivitiesThisMonth"),
    DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS(MyTracksGranularity.Type.LAST_30_DAYS, false, "ActivitiesLast30Days"),
    TRAINING(MyTracksGranularity.Type.PAST_WEEK, false, "Training"),
    PROGRESS(null, false, "Progress"),
    RESOURCES(MyTracksGranularity.Type.PAST_WEEK, false, "Resources"),
    SLEEP(MyTracksGranularity.Type.PAST_WEEK, false, "Sleep"),
    SLEEP_HRV(null, false, "SleepHRV"),
    STEPS(MyTracksGranularity.Type.PAST_WEEK, false, "Steps"),
    CALORIES(MyTracksGranularity.Type.PAST_WEEK, false, "Calories"),
    GOAL(MyTracksGranularity.Type.THIS_WEEK, false, "Goal"),
    COMMUTE_THIS_MONTH(MyTracksGranularity.Type.THIS_MONTH, false, "CommuteThisMonth"),
    ASCENT(MyTracksGranularity.Type.PAST_WEEK, false, "Ascent"),
    MINIMUM_HEART_RATE(MyTracksGranularity.Type.PAST_WEEK, false, "MinimumHeartRate"),
    ADD_NEW_WIDGET(null, false, null)
}
