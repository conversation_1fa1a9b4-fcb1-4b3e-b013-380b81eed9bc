package com.stt.android.home.diary.diarycalendar.sharesummary

import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.TotalValues
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData

data class DiaryCalendarShareSummaryData(
    val dailyWorkoutStatisticsWithSummary: DailyWorkoutStatisticsWithSummary,
    val totalValues: TotalValues,
    val bubbleData: List<DiaryBubbleData>,
    val granularity: DiaryCalendarListContainer.Granularity,
    val activityStatsWithTotals: List<Pair<ActivityType, TotalValues>>,
    val maxDurationForSingleActivity: Double,
    val title: CharSequence,
)
