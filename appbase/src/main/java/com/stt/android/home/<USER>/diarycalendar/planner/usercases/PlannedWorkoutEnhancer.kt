package com.stt.android.home.diary.diarycalendar.planner.usercases

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.diary.diarycalendar.planner.domain.models.PlannedWorkout
import com.stt.android.remoteconfig.api.RemoteConfig
import timber.log.Timber
import java.time.LocalDate
import java.util.concurrent.locks.ReentrantReadWriteLock
import javax.inject.Inject
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * Utility class for enhancing workout statistics data with planned workouts from training plans.
 */
class PlannedWorkoutEnhancer @Inject constructor(
    private val getLastPlanUseCase: GetLastPlanUseCase,
    private val activityGroupMapper: ActivityGroupMapper,
    private val remoteConfig: RemoteConfig,
) {

    companion object {
        private const val CACHE_DURATION_MILLIS = 5 * 60 * 1000L // 5 minutes
        private val cacheLock = ReentrantReadWriteLock()
        private var cachedResult: GetLastPlanUseCaseResult? = null
        private var cacheTimestamp: Long = 0L

        private fun isCacheValid(): Boolean {
            return System.currentTimeMillis() - cacheTimestamp < CACHE_DURATION_MILLIS
        }

        private suspend fun getCachedOrFetch(getLastPlanUseCase: GetLastPlanUseCase): GetLastPlanUseCaseResult {
            // First try to read from cache
            cacheLock.read {
                if (cachedResult != null && isCacheValid()) {
                    return cachedResult!!
                }
            }

            // Cache is invalid or empty, fetch new data (fetch outside the lock)
            val result = getLastPlanUseCase()
            // Now update cache only if still invalid, inside write lock
            cacheLock.write {
                if (cachedResult == null || !isCacheValid()) {
                    cachedResult = result
                    cacheTimestamp = System.currentTimeMillis()
                }
                return cachedResult!!
            }
        }

        /**
         * Clears the cache. This method is intended for testing purposes.
         */
        internal fun clearCache() {
            cacheLock.write {
                cachedResult = null
                cacheTimestamp = 0L
            }
        }
    }

    /**
     * Enhances the provided workout statistics data with planned workouts from the active training plan.
     * Only adds planned workouts for dates that don't already have actual workouts.
     *
     * @param data The original workout statistics data
     * @param startDate The start date of the date range to enhance
     * @param endDate The end date of the date range to enhance
     * @return Enhanced workout statistics data with planned workouts included
     */
    suspend fun enhanceWithPlannedWorkouts(
        data: DailyWorkoutStatisticsWithSummary,
        startDate: LocalDate,
        endDate: LocalDate
    ): DailyWorkoutStatisticsWithSummary {
        if (!remoteConfig.isAiPlannerEnabled()) return data

        return try {
            val lastPlanResult = getCachedOrFetch(getLastPlanUseCase)
            if (lastPlanResult is GetLastPlanUseCaseResult.ActivePlan) {
                val trainingPlan = lastPlanResult.trainingPlan
                val today = LocalDate.now()

                // Get planned workouts for today and upcoming days within the requested date range
                val plannedWorkoutsByDate = trainingPlan.weeklyPrograms
                    .flatMap { it.plannedWorkouts }
                    .filter { it.trainingDate in startDate..endDate && it.trainingDate >= today }
                    .groupBy { it.trainingDate }

                // Enhance daily data with planned workouts
                val enhancedDailyData = data.dailyData.toMutableMap()

                for ((date, plannedWorkouts) in plannedWorkoutsByDate) {
                    val existingData = enhancedDailyData[date]

                    // Only add planned workouts if no actual workouts exist for this date
                    if (existingData == null || existingData.durationByActivityGroup.isEmpty()) {
                        val plannedDurationByGroup = plannedWorkouts
                            .groupBy { activityGroupMapper.activityTypeIdToGroup(it.activityType.id) }
                            .mapValues { (_, workouts) ->
                                workouts.sumOf { it.durationInSeconds * 1000L } // Convert to milliseconds
                            }

                        enhancedDailyData[date] = DiaryCalendarDailyData(
                            durationByActivityGroup = plannedDurationByGroup,
                            workoutIds = existingData?.workoutIds ?: emptyList(),
                            menstrualDateType = existingData?.menstrualDateType
                                ?: com.stt.android.menstrualcycle.domain.MenstrualDateType.NOTHING
                        )
                    }
                }

                data.copy(dailyData = enhancedDailyData)
            } else {
                data
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to enhance data with planned workouts")
            data
        }
    }

    /**
     * Gets planned workouts for a specific date.
     *
     * @param date The date for which to retrieve planned workouts
     * @return List of planned workouts for the specified date, or an empty list if none are found
     */
    suspend fun getPlannedWorkouts(date: LocalDate): List<PlannedWorkout> {
        if (!remoteConfig.isAiPlannerEnabled()) return emptyList()

        return runSuspendCatching {
            val lastPlanResult = getCachedOrFetch(getLastPlanUseCase)
            if (lastPlanResult is GetLastPlanUseCaseResult.ActivePlan) {
                val trainingPlan = lastPlanResult.trainingPlan

                trainingPlan.weeklyPrograms
                    .flatMap { it.plannedWorkouts }
                    .filter { it.trainingDate == date }
            } else {
                emptyList()
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to get planned workouts for date: $date")
        }.getOrElse { emptyList() }
    }
}
