package com.stt.android.home.dashboardv2.usecase

import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.ActivitiesTabViewModel.ShareWorkout
import com.stt.android.ui.components.workout.WorkoutShareInfo
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.setSharingLinkIfPrivate
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import timber.log.Timber
import javax.inject.Inject

class GenerateShareWorkoutUseCase @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutShareHelper: WorkoutShareHelper,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
    private val syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
) {
    suspend fun generate(
        workoutHeader: WorkoutHeader,
        workoutShareInfo: WorkoutShareInfo
    ): ShareWorkout {
        val hasImage = workoutHeader.pictureCount > 0
        val hasMap = !workoutHeader.isPolylineEmpty
        val imageIndex = when (val selected = workoutShareInfo.selected) {
            is WorkoutShareInfo.Selected.None,
            is WorkoutShareInfo.Selected.Map -> 0

            is WorkoutShareInfo.Selected.Image -> if (hasMap) {
                selected.index + 1
            } else {
                selected.index
            }
        }

        return when {
            workoutShareHelper.showMultipleWorkoutShareWays() -> {
                val watchName = runSuspendCatching {
                    summaryExtensionDataModel.findByWorkoutId(workoutHeader.id)
                        ?.displayName
                        .orEmpty()
                }.getOrElse { e ->
                    Timber.w(e, "Error while loading workout summary extension")
                    ""
                }
                ShareWorkout.MultipleWorkout(
                    workoutHeader = workoutHeader,
                    imageIndex = imageIndex,
                    watchName = watchName,
                )
            }

            hasImage || hasMap || workoutHeader.activityType.isDiving -> ShareWorkout.Workout(
                workoutHeader = workoutHeader,
                imageIndex = imageIndex,
            )

            else -> {
                // TODO loading spinner

                runSuspendCatching {
                    setSharingLinkIfPrivate(
                        workoutHeader = workoutHeader,
                        workoutHeaderController = workoutHeaderController,
                        syncSingleWorkoutUseCase = syncSingleWorkoutUseCase,
                    )

                    ShareWorkout.Link(workoutHeader)
                }.getOrElse { e ->
                    Timber.w(e, "Error while updating workout sharing options")
                    ShareWorkout.Error(workoutHeader)
                }
            }
        }
    }
}
