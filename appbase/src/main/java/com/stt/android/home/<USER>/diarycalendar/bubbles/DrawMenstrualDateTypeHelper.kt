package com.stt.android.home.diary.diarycalendar.bubbles

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.toColorInt
import com.stt.android.R
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import com.stt.android.utils.BrandFlavourConstants
import kotlin.math.min

class DrawMenstrualDateTypeHelper {

    private var lineWidth = 4F

    private val typePaint: Paint = Paint().apply {
        isAntiAlias = true
        color = "#F0F0F0".toColorInt()
        strokeWidth = lineWidth
    }

    fun setWidthForPaint(strokeWidth: Float) {
        // To avoid drawing the border line too thick.
        lineWidth = min(4F, strokeWidth)
        typePaint.strokeWidth = lineWidth
    }

    fun drawMenstrualDateType(
        context: Context,
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        dateType: MenstrualDateType,
        borderRadius: Float
    ) {
        if (!BrandFlavourConstants.PROVIDE_MC_FEATURE) return
        resetMenstrualDateTypePaint(context, dateType)
        // Avoid drawing borders of inconsistent thickness.
        val lineWidth = typePaint.strokeWidth
        when (dateType) {
            MenstrualDateType.IN_HISTORY_AFTER_TODAY,
            MenstrualDateType.IN_PREDICTION -> drawInPredictionMenstrualDateType(
                canvas = canvas,
                left = left,
                top = top + lineWidth / 2,
                right = right,
                bottom = bottom - lineWidth / 2
            )

            MenstrualDateType.IN_HISTORY_NORMAL -> drawInHistoryMenstrualDateType(
                canvas = canvas,
                left = left,
                top = top + lineWidth / 2,
                right = right,
                bottom = bottom - lineWidth / 2
            )

            MenstrualDateType.START_OF_PREDICTION,
            MenstrualDateType.START_OF_HISTORY -> drawStartMenstrualDateType(
                canvas = canvas,
                left = left + lineWidth / 2,
                top = top + lineWidth / 2,
                right = right,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius
            )

            MenstrualDateType.END_OF_PREDICTION,
            MenstrualDateType.END_OF_HISTORY_AFTER_TODAY,
            MenstrualDateType.END_OF_HISTORY_NORMAL -> drawEndMenstrualDateType(
                canvas = canvas,
                left = left,
                top = top + lineWidth / 2,
                right = right - lineWidth / 2,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius
            )

            MenstrualDateType.BOTH_START_AND_END_IN_HISTORY,
            MenstrualDateType.BOTH_START_AND_END_IN_PREDICTION -> drawStartAndEndMenstrualDateType(
                canvas = canvas,
                left = left,
                top = top + lineWidth / 2,
                right = right - lineWidth / 2,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius
            )

            MenstrualDateType.NOTHING -> { /* Don't draw anything */
            }
        }
    }

    private fun resetMenstrualDateTypePaint(context: Context, dateType: MenstrualDateType) {
        when (dateType) {
            MenstrualDateType.IN_PREDICTION,
            MenstrualDateType.START_OF_PREDICTION,
            MenstrualDateType.END_OF_PREDICTION,
            MenstrualDateType.BOTH_START_AND_END_IN_PREDICTION,
            MenstrualDateType.END_OF_HISTORY_AFTER_TODAY,
            MenstrualDateType.IN_HISTORY_AFTER_TODAY -> typePaint.apply {
                style = Paint.Style.STROKE
                strokeWidth = lineWidth
                color = ResourcesCompat.getColor(context.resources, R.color.cloudy_grey, context.theme)
            }

            MenstrualDateType.IN_HISTORY_NORMAL,
            MenstrualDateType.START_OF_HISTORY,
            MenstrualDateType.END_OF_HISTORY_NORMAL,
            MenstrualDateType.BOTH_START_AND_END_IN_HISTORY,
            MenstrualDateType.NOTHING -> typePaint.apply {
                style = Paint.Style.FILL
                strokeWidth = 0F
                color = ResourcesCompat.getColor(context.resources, R.color.light_grey, context.theme)
            }
        }
    }

    private fun drawInPredictionMenstrualDateType(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float
    ) {
        canvas.drawLine(left, top, right, top, typePaint)
        canvas.drawLine(left, bottom, right, bottom, typePaint)
    }

    private fun drawInHistoryMenstrualDateType(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float
    ) {
        canvas.drawRect(left, top, right, bottom, typePaint)
    }

    private fun drawStartMenstrualDateType(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        borderRadius: Float
    ) {
        val path = Path().apply {
            moveTo(right, bottom)
            lineTo(left + borderRadius, bottom)
            arcTo(RectF(left, bottom - borderRadius * 2, left + borderRadius * 2, bottom), 90F, 90F)
            lineTo(left, top + borderRadius)
            arcTo(RectF(left, top, left + borderRadius * 2, top + borderRadius * 2), 180F, 90F)
            lineTo(right, top)
        }
        canvas.drawPath(path, typePaint)
    }

    private fun drawEndMenstrualDateType(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        borderRadius: Float
    ) {
        val path = Path().apply {
            moveTo(left, top)
            lineTo(right - borderRadius, top)
            arcTo(RectF(right - borderRadius * 2, top, right, top + borderRadius * 2), 270F, 90F)
            lineTo(right, bottom - borderRadius)
            arcTo(
                RectF(right - borderRadius * 2, bottom - borderRadius * 2, right, bottom),
                0F,
                90F
            )
            lineTo(left, bottom)
        }
        canvas.drawPath(path, typePaint)
    }

    private fun drawStartAndEndMenstrualDateType(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        borderRadius: Float
    ) {
        val path = Path().apply {
            moveTo(left + borderRadius, top)
            lineTo(right - borderRadius, top)
            arcTo(RectF(right - borderRadius * 2, top, right, top + borderRadius * 2), 270F, 90F)
            lineTo(right, bottom - borderRadius)
            arcTo(
                RectF(right - borderRadius * 2, bottom - borderRadius * 2, right, bottom),
                0F,
                90F
            )
            lineTo(left + borderRadius, bottom)
            arcTo(RectF(left, bottom - borderRadius * 2, left + borderRadius * 2, bottom), 90F, 90F)
            lineTo(left, top + borderRadius)
            arcTo(RectF(left, top, left + borderRadius * 2, top + borderRadius * 2), 180F, 90F)
            close()
        }
        canvas.drawPath(path, typePaint)
    }
}
