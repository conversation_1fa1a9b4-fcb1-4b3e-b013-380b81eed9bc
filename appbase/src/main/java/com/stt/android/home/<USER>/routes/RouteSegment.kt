package com.stt.android.home.explore.routes

import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonParseException
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.google.maps.android.PolyUtil
import com.stt.android.domain.Point
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Type

/**
 * @param startPoint start point
 * @param endPoint end point
 * @param ascent Ascent info is only available through Graphhoper API. Straight segments don't have ascent
 *          info so those segments don't have ascent info. Furthermore, ascent info added in later versions.
 *          Hence, legacy routes don't have ascent info.
 * @param routePoints The points in this route segment.
 * @param position The position of this segment in the route
 */
@Parcelize
data class RouteSegment(
    @SerializedName("start") val startPoint: LatLng,
    @SerializedName("end") val endPoint: LatLng,
    @SerializedName("position") val position: Int,
    @SerializedName("routePoints") val routePoints: List<Point>,
    @SerializedName("ascent") val ascent: Double? = null
) : Parcelable {

    @IgnoredOnParcel
    val routePointsAsLatLngs: List<LatLng> by lazy {
        routePoints.map { LatLng(it.latitude, it.longitude) }
    }

    companion object {
        private fun createRoutePointsFromStraightSegment(start: LatLng, end: LatLng): List<Point> {
            return listOf(
                Point(start.longitude, start.latitude),
                Point(end.longitude, end.latitude)
            )
        }

        @JvmStatic
        fun newStraightSegment(start: LatLng, end: LatLng, position: Int): RouteSegment {
            val pathPoints = ArrayList<LatLng>()
            pathPoints.add(start)
            pathPoints.add(end)
            return RouteSegment(
                startPoint = start,
                endPoint = end,
                position = position,
                routePoints = createRoutePointsFromStraightSegment(
                    start,
                    end
                )
            )
        }
    }

    class RouteSegmentSerializer : JsonSerializer<RouteSegment>, JsonDeserializer<RouteSegment> {
        companion object {
            private val classType = object : TypeToken<ArrayList<Point>>() {}.type
        }

        override fun serialize(
            src: RouteSegment,
            typeOfSrc: Type,
            context: JsonSerializationContext
        ): JsonElement {
            val jsonObject = JsonObject()
            val startPoint = src.startPoint
            jsonObject.add(
                "start",
                context.serialize(Point(startPoint.longitude, startPoint.latitude))
            )

            val stopPoint = src.endPoint
            jsonObject.add(
                "end",
                context.serialize(Point(stopPoint.longitude, stopPoint.latitude))
            )

            // TODO: We still need to send polyline with empty string.
            // If we don't send it, the request will fail. We might be able to remove it
            // in the unforseeable future - who knows.
            jsonObject.add("polyline", JsonPrimitive(""))

            jsonObject.add("position", JsonPrimitive(src.position))
            jsonObject.add("routePoints", context.serialize(src.routePoints))
            if (src.ascent != null) {
                jsonObject.add("ascent", JsonPrimitive(src.ascent))
            }
            return jsonObject
        }

        @Throws(JsonParseException::class)
        override fun deserialize(
            json: JsonElement,
            typeOfT: Type,
            context: JsonDeserializationContext
        ): RouteSegment {
            val jsonObject = json.asJsonObject

            val startPoint = context.deserialize<Point>(jsonObject.get("start"), Point::class.java)
            val start = LatLng(startPoint.latitude, startPoint.longitude)

            val endPoint = context.deserialize<Point>(jsonObject.get("end"), Point::class.java)
            val end = LatLng(endPoint.latitude, endPoint.longitude)

            val position = context.deserialize<Int>(jsonObject.get("position"), Int::class.java)
            val ascent = context.deserialize<Double>(jsonObject.get("ascent"), Double::class.java)

            // this is here for legacy reasons. old users might have route segments that contain
            // encoded polylines. so in this case, we deserialize it and convert it right away
            // to route points.
            val legacyPath = context.deserialize<String>(jsonObject.get("polyline"), String::class.java)
            val routePoints: List<Point>
            routePoints = if (legacyPath != null && legacyPath.isNotEmpty()) {
                PolyUtil.decode(legacyPath).map {
                    Point(it.longitude, it.latitude)
                }
            } else {
                context.deserialize<List<Point>>(
                    jsonObject.get("routePoints"),
                    classType
                )
            }
            return RouteSegment(
                startPoint = start,
                endPoint = end,
                routePoints = routePoints,
                position = position,
                ascent = ascent
            )
        }
    }
}
