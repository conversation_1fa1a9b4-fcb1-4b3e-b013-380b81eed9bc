package com.stt.android.home.dashboard

import android.app.Activity
import android.content.SharedPreferences
import androidx.annotation.MainThread
import androidx.core.content.edit
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEvent.DASHBOARD_WIDGET_ADD_WIDGET
import com.stt.android.analytics.AnalyticsEvent.DASHBOARD_WIDGET_EDIT_MODE
import com.stt.android.analytics.AnalyticsEvent.DASHBOARD_WIDGET_REMOVE_WIDGET
import com.stt.android.analytics.AnalyticsEvent.HOME_SCREEN
import com.stt.android.analytics.AnalyticsEvent.HOME_SCREEN_BROWSE_DASHBOARD
import com.stt.android.analytics.AnalyticsEvent.HOME_SCREEN_DASHBOARD_NAVIGATION
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsEventProperty.ADD_OR_REPLACE
import com.stt.android.analytics.AnalyticsEventProperty.CALORIES_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.DASHBOARD_PAGE
import com.stt.android.analytics.AnalyticsEventProperty.NEW_DASHBOARD_PAGE
import com.stt.android.analytics.AnalyticsEventProperty.NUMBER_OF_DASHBOARD_PAGES
import com.stt.android.analytics.AnalyticsEventProperty.PROGRESS_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.RESOURCES_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.SLEEP_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.STEPS_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.TRAINING_DATA_AVAILABLE
import com.stt.android.analytics.AnalyticsEventProperty.WIDGET
import com.stt.android.analytics.AnalyticsEventProperty.WIDGETS
import com.stt.android.analytics.AnalyticsEventProperty.WIDGET_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.WIDGET_TAPPED
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.movescount.MovescountAppInfoUseCase
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboard.widget.LoadedWidgetData.WidgetShowType
import com.stt.android.home.dashboard.widget.WidgetType
import com.stt.android.ui.utils.WindowSizeClass
import com.stt.android.utils.STTConstants
import com.stt.android.utils.WindowsSubsystemForAndroidUtils
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import com.stt.android.home.dashboardv2.widgets.WidgetType as DashboardV2WidgetType

@Singleton
class DashboardAnalytics @Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val sharedPreferences: SharedPreferences,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val movescountAppInfoUseCase: MovescountAppInfoUseCase,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
) {
    private var latestDashboardIndex: Int? = null

    private var dashboardStatus: DashboardStatus? = null
    private var useNoWatchPairedDataAvailabilityStatus = false

    @MainThread
    fun setCurrentDashboard(zeroBasedDashboardIndex: Int) {
        val oneBasedDashboardIndex = zeroBasedDashboardIndex + 1
        if (latestDashboardIndex == null || dashboardStatus == null) {
            // App starting, no need to send analytics.
            Timber.d("Dashboard index initialised. $oneBasedDashboardIndex")
            latestDashboardIndex = oneBasedDashboardIndex
            return
        } else if (latestDashboardIndex == oneBasedDashboardIndex) {
            // Dashboard not changed.
            return
        }

        dashboardStatus?.let { status ->
            datahubAnalyticsTracker.trackEvent(
                HOME_SCREEN_BROWSE_DASHBOARD,
                AnalyticsProperties().apply {
                    put(NEW_DASHBOARD_PAGE, oneBasedDashboardIndex)
                    status.fillAnalyticsProperties(this, useNoWatchPairedDataAvailabilityStatus)
                }
            )
        }

        latestDashboardIndex = oneBasedDashboardIndex
    }

    fun updateDashboardStatus(status: DashboardStatus) {
        dashboardStatus = status
    }

    fun updateUseNoWatchPairedStatus(useNoWatchPaired: Boolean) {
        useNoWatchPairedDataAvailabilityStatus = useNoWatchPaired
    }

    @MainThread
    fun sendHomeScreenDashboardNavigationEvent(widgetTapped: WidgetType) {
        if (widgetTapped.analyticsName == null) {
            Timber.w("sendHomeScreenDashboardNavigationEvent called with non-supported widget ${widgetTapped.name}")
            return
        }

        val properties = AnalyticsProperties().apply {
            put(WIDGET_TAPPED, widgetTapped.analyticsName)
        }
        datahubAnalyticsTracker.trackEvent(
            HOME_SCREEN_DASHBOARD_NAVIGATION,
            properties
        )
    }

    @MainThread
    internal fun sendHomeScreenDashboardV2NavigationEvent(widgetTapped: DashboardV2WidgetType) {
        val properties = AnalyticsProperties().apply {
            put(WIDGET_TAPPED, widgetTapped.analyticsName)
        }
        datahubAnalyticsTracker.trackEvent(
            HOME_SCREEN_DASHBOARD_NAVIGATION,
            properties
        )
    }

    fun sendOver5WorkoutsEventIfNeeded(username: String, count: Long) {
        if (count >= 5) {
            // one-time value should be username dependant
            val prefKey = STTConstants.MiscPreferences
                .KEY_DASHBOARD_USER_ACTIVE_ANALYTICS_EVENT_SENT + username
            if (!sharedPreferences.getBoolean(prefKey, false)) {
                firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.OVER_5_WORKOUTS)
                sharedPreferences.edit { putBoolean(prefKey, true) }
            }
        }
    }

    @MainThread
    fun trackHomeScreenAnalytics(
        zeroBasedDashboardPageEntered: Int,
        currentFitnessValue: Int?,
        currentFatigueValue: Int?,
        currentFormValue: Int?,
        deviceCategory: DeviceCategory,
        currentWidgets: List<WidgetType>
    ) {
        val oneBasedDashboardPageEntered = zeroBasedDashboardPageEntered + 1
        try {
            val movescountInstalled = movescountAppInfoUseCase.isMovescountAppInstalledOnPhone()

            val firstHomeScreenSinceSignup = sharedPreferences.getBoolean(
                STTConstants.DefaultPreferences.KEY_JUST_SIGNED_UP_USER,
                false
            )

            // resetting the key
            if (firstHomeScreenSinceSignup) {
                sharedPreferences.edit {
                    putBoolean(STTConstants.DefaultPreferences.KEY_JUST_SIGNED_UP_USER, false)
                }
            }

            val properties = AnalyticsProperties()
                .putYesNo(
                    AnalyticsEventProperty.FIRST_TIME_SINCE_SIGNUP,
                    firstHomeScreenSinceSignup
                )
                .put(AnalyticsEventProperty.DASHBOARD_PAGE_ENTERED, oneBasedDashboardPageEntered)
                .put(AnalyticsEventProperty.CURRENT_CTL_VALUE, currentFitnessValue)
                .put(AnalyticsEventProperty.CURRENT_ATL_VALUE, currentFatigueValue)
                .put(AnalyticsEventProperty.CURRENT_FORM_VALUE, currentFormValue)
                .put(AnalyticsEventProperty.DEVICE_CATEGORY, deviceCategory.propertyValue)

            dashboardStatus?.apply {
                fillAnalyticsProperties(
                    properties = properties,
                    useNoWatchPaired = useNoWatchPairedDataAvailabilityStatus,
                )
            }

            val widgetNames = currentWidgets.mapNotNull { it.analyticsName }
            properties
                .put(WIDGET_COUNT, widgetNames.size)
                .put(WIDGETS, widgetNames)

            Timber.d("$HOME_SCREEN ${properties.map}")
            emarsysAnalytics.trackEventWithProperties(
                HOME_SCREEN,
                properties.map
            )
            // tracking this property only in amplitude
            properties.putYesNo(AnalyticsEventProperty.MOVESCOUNT_INSTALLED, movescountInstalled)
            datahubAnalyticsTracker.trackEvent(HOME_SCREEN, properties)

            val movescountInstalledStr =
                if (movescountInstalled) AnalyticsPropertyValue.YES else AnalyticsPropertyValue.NO
            datahubAnalyticsTracker.trackUserProperty(
                AnalyticsUserProperty.SUUNTO_MOVESCOUNT_APP_INSTALLED_ON_SAME_PHONE,
                movescountInstalledStr
            )
            emarsysAnalytics.trackStringUserProperty(
                AnalyticsUserProperty.SUUNTO_MOVESCOUNT_APP_INSTALLED_ON_SAME_PHONE,
                movescountInstalledStr
            )
        } catch (e: Throwable) {
            Timber.d(e, "Error during track home screen analytics")
        }
    }

    suspend fun trackReactionAnalytics(workoutHeader: WorkoutHeader, reactionSummary: ReactionSummary?) {
        val sharing = when {
            workoutHeader.sharingOptions.contains(SharingOption.EVERYONE) -> "Public"
            workoutHeader.sharingOptions.contains(SharingOption.FOLLOWERS) -> "Followers"
            else -> "Private"
        }

        val achievementCount = workoutHeader.key
            ?.let { getAchievementUseCase(it) }
            ?.count
            ?: 0

        val properties = AnalyticsProperties()
            .put("Source", "Feed")
            .put("TargetAccountType", "Normal")
            .put("TargetWorkoutVisibility", sharing)
            .put("NumberOfPhotos", workoutHeader.pictureCount)
            .put("NumberOfLikes", workoutHeader.reactionCount)
            .put("NumberOfComments", workoutHeader.commentCount)
            .put("NumberOfAchievements", achievementCount)
            .putYesNo("HasDescription", !workoutHeader.description.isNullOrBlank())
            .put("ActivityType", workoutHeader.activityType.simpleName)
            .put("DurationInMinutes", workoutHeader.totalTime)
            .put("DistanceInMeters", workoutHeader.totalDistance)

        if (reactionSummary?.isUserReacted != true) {
            // isUserReacted is null or false -> this is a like action
            datahubAnalyticsTracker.trackEvent(
                AnalyticsEvent.LIKE_WORKOUT,
                properties
            )
        }
    }

    fun trackOpenDashboardWidgetCustomizationMode(currentWidgets: List<WidgetType>) {
        AnalyticsProperties().apply {
            val widgetNames = currentWidgets.mapNotNull { it.analyticsName }
            put(WIDGET_COUNT, widgetNames.size)
            put(WIDGETS, widgetNames)
            datahubAnalyticsTracker.trackEvent(DASHBOARD_WIDGET_EDIT_MODE, this)
        }
    }

    fun trackAddWidget(
        widgetType: WidgetType,
        newTotalWidgetCount: Int,
        zeroBasedDashboardIndex: Int,
    ) = trackAddWidgetInternal(
        widgetType = widgetType,
        newTotalWidgetCount = newTotalWidgetCount,
        zeroBasedDashboardIndex = zeroBasedDashboardIndex,
        operationType = AnalyticsPropertyValue.ADD
    )

    fun trackReplaceWidget(
        widgetType: WidgetType,
        newTotalWidgetCount: Int,
        zeroBasedDashboardIndex: Int
    ) = trackAddWidgetInternal(
        widgetType = widgetType,
        newTotalWidgetCount = newTotalWidgetCount,
        zeroBasedDashboardIndex = zeroBasedDashboardIndex,
        operationType = AnalyticsPropertyValue.REPLACE
    )

    private fun trackAddWidgetInternal(
        widgetType: WidgetType,
        newTotalWidgetCount: Int,
        zeroBasedDashboardIndex: Int,
        operationType: String
    ) {
        if (widgetType.analyticsName == null) {
            Timber.w("trackAddWidgetInternal called with non-supported widget ${widgetType.name}")
            return
        }

        AnalyticsProperties().apply {
            put(WIDGET, widgetType.analyticsName)
            put(WIDGET_COUNT, newTotalWidgetCount)
            put(DASHBOARD_PAGE, zeroBasedDashboardIndex + 1)
            put(ADD_OR_REPLACE, operationType)
            datahubAnalyticsTracker.trackEvent(DASHBOARD_WIDGET_ADD_WIDGET, this)
        }
    }

    fun trackRemoveWidget(
        widgetType: WidgetType,
        newTotalWidgetCount: Int,
        zeroBasedDashboardIndex: Int
    ) {
        if (widgetType.analyticsName == null) {
            Timber.w("trackRemoveWidget called with non-supported widget ${widgetType.name}")
            return
        }

        AnalyticsProperties().apply {
            put(WIDGET, widgetType.analyticsName)
            put(WIDGET_COUNT, newTotalWidgetCount)
            put(DASHBOARD_PAGE, zeroBasedDashboardIndex + 1)
            datahubAnalyticsTracker.trackEvent(DASHBOARD_WIDGET_REMOVE_WIDGET, this)
        }
    }

    enum class DeviceCategory(val propertyValue: String) {
        PHONE(AnalyticsPropertyValue.DeviceCategory.PHONE),
        TABLET(AnalyticsPropertyValue.DeviceCategory.TABLET),
        COMPUTER(AnalyticsPropertyValue.DeviceCategory.COMPUTER);

        companion object {
            fun fromActivity(activity: Activity): DeviceCategory {
                return if (WindowsSubsystemForAndroidUtils.isWindowsSubsystemForAndroid) {
                    COMPUTER
                } else {
                    WindowSizeClass.computeLowestDimensionWindowSizeClass(activity)?.let {
                        when (it) {
                            WindowSizeClass.Compact -> PHONE
                            WindowSizeClass.Medium -> TABLET
                            // TODO: 17.3.2022 Consider changing this to COMPUTER once Chrome OS
                            // devices are supported
                            WindowSizeClass.Expanded -> TABLET
                        }
                    } ?: PHONE
                }
            }
        }
    }
}

data class DashboardStatus(
    val numberOfPages: Int,
    val sleepShowType: WidgetShowType,
    val stepsShowType: WidgetShowType,
    val resourcesShowType: WidgetShowType,
    val caloriesShowType: WidgetShowType,
    val trainingShowType: WidgetShowType,
    val progressShowType: WidgetShowType
) {
    fun fillAnalyticsProperties(
        properties: AnalyticsProperties,
        useNoWatchPaired: Boolean
    ) = properties.apply {
        fun getAnalyticsPropertyString(showType: WidgetShowType) = when {
            useNoWatchPaired -> AnalyticsPropertyValue.NO_WATCH_PAIRED
            showType == WidgetShowType.NORMAL -> AnalyticsPropertyValue.YES
            else -> AnalyticsPropertyValue.NO
        }

        put(NUMBER_OF_DASHBOARD_PAGES, numberOfPages)
        put(SLEEP_DATA_AVAILABLE, getAnalyticsPropertyString(sleepShowType))
        put(STEPS_DATA_AVAILABLE, getAnalyticsPropertyString(stepsShowType))
        put(RESOURCES_DATA_AVAILABLE, getAnalyticsPropertyString(resourcesShowType))
        put(CALORIES_DATA_AVAILABLE, getAnalyticsPropertyString(caloriesShowType))
        put(TRAINING_DATA_AVAILABLE, getAnalyticsPropertyString(trainingShowType))
        put(PROGRESS_DATA_AVAILABLE, getAnalyticsPropertyString(progressShowType))
    }
}
