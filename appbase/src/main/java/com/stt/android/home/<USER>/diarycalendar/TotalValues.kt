package com.stt.android.home.diary.diarycalendar

import android.content.Context
import android.view.Gravity
import androidx.annotation.PluralsRes
import androidx.annotation.StringRes

data class TotalValueItem(
    val value: String = "0",
    @StringRes val unitRes: Int? = null,
    val unitString: String? = null,
    @StringRes val labelRes: Int? = null,
    @PluralsRes val labelQuantityRes: Int? = null,
    val labelQuantityValue: Int? = null
) {
    fun getUnitLabel(context: Context): String = unitString
        ?: unitRes?.let { context.getString(it) }
        ?: ""
}

data class TotalValues(
    val duration: Double,
    val distance: Double = 0.0,
    val values: List<TotalValueItem>,
    val workoutIds: List<Int>
) {
    val size = values.size

    fun valueAtIndex(index: Int): String? = values.getOrNull(index)?.value

    fun unitAtIndex(index: Int, context: Context): String =
        values.getOrNull(index)?.getUnitLabel(context) ?: ""

    fun labelAtIndex(index: Int, context: Context): String? =
        values.getOrNull(index)?.run {
            if (labelQuantityRes != null && labelQuantityValue != null) {
                context.resources.getQuantityString(labelQuantityRes, labelQuantityValue)
            } else if (labelRes != null) {
                context.getString(labelRes)
            } else {
                null
            }
        }

    fun gravityAtIndex(index: Int): Int =
        Gravity.BOTTOM or when (index) {
            // First value aligned to start
            0 -> Gravity.START
            // Last value aligned to end
            values.size - 1 -> Gravity.END
            // Values in the middle are centered
            else -> Gravity.CENTER_HORIZONTAL
        }
}
