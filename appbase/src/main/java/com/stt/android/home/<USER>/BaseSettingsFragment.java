package com.stt.android.home.settings;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.hardware.SensorManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.PreferenceScreen;
import androidx.recyclerview.widget.RecyclerView;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.analytics.AnalyticsUserProperty;
import com.stt.android.analytics.DatahubAnalyticsTracker;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.appupdates.AppUpdatesActivity;
import com.stt.android.appversion.AppVersionViewModel;
import com.stt.android.bluetooth.BleHelper;
import com.stt.android.cadence.CadenceHelper;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.Sex;
import com.stt.android.home.HomeTab;
import com.stt.android.maps.SuuntoMaps;
import com.stt.android.remote.appversion.CheckAppVersionRemoteApiKt;
import com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionListActivity;
import com.stt.android.ui.tasks.LogoutTask;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.usecases.startup.UserSettingsTracker;
import com.stt.android.utils.BluetoothUtils;
import com.stt.android.utils.BrandFlavourConstants;
import com.stt.android.utils.STTConstants;
import com.stt.android.utils.UpdatePressureTask;
import com.stt.android.utils.WindowsSubsystemForAndroidUtils;
import dagger.Lazy;
import io.reactivex.disposables.CompositeDisposable;
import java.util.Locale;
import javax.inject.Inject;
import timber.log.Timber;

public class BaseSettingsFragment extends ExtendedPreferenceFragmentCompat
    implements HomeTab, UserSettingsController.UpdateListener {
    public static final String SETTINGS_FRAGMENT_TAG = "SETTINGS_FRAGMENT_TAG";
    public static final String KEY_PREFERENCE_SCREEN = "com.stt.android.KEY_PREFERENCE_SCREEN";
    public static final String KEY_PREFERENCE_DIALOG = "com.stt.android.KEY_PREFERENCE_DIALOG";

    public static SettingsFragment newInstance() {
        return newInstance(null, null);
    }

    public static SettingsFragment newInstance(String navigateToScreen,
        String navigateToPreference) {
        SettingsFragment settingsFragment = new SettingsFragment();
        Bundle bundle = new Bundle();
        bundle.putString(KEY_PREFERENCE_SCREEN, navigateToScreen);
        bundle.putString(KEY_PREFERENCE_DIALOG, navigateToPreference);
        settingsFragment.setArguments(bundle);
        return settingsFragment;
    }

    @Inject
    UserSettingsController userSettingsController;

    @Inject
    UserSettingsTracker userSettingsTrackerForAnalytics;

    @Inject
    CurrentUserController currentUserController;

    @Inject
    LocalBroadcastManager localBM;

    @Inject
    SensorManager sensorManager;

    @Inject
    EmarsysAnalytics emarsysAnalytics;

    @Inject
    DatahubAnalyticsTracker datahubAnalyticsTracker;

    @Inject
    Lazy<LogoutTask> logoutTask;

    @Inject
    SharedPreferences sharedPreferences;

    @Inject
    SuuntoMaps suuntoMaps;

    private AppVersionViewModel appVersionViewModel;

    final CompositeDisposable disposable = new CompositeDisposable();

    protected PreferenceCategory servicePreferenceCategory;
    protected PreferenceCategory otherCategory;
    protected PreferenceCategory aboutPreferenceCategory;
    protected PreferenceCategory generalCategory;
    protected Preference logOutPreference;
    private UserSettingsController.UserSettingsUpdater userSettingsUpdater;

    private final String[] socialKeys = { "fb", "instagram" };

    private final BroadcastReceiver userStatusChangedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, final Intent intent) {
            updateLogOutPreference();
        }
    };

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        appVersionViewModel = new ViewModelProvider(this, getDefaultViewModelProviderFactory()).get(
            AppVersionViewModel.class);
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        //set fragment container for swapping fragmnets
        setFragmentContainerId(R.id.mainContent);
        //set pref resources
        setPreferencesFromResource(R.xml.preferences, rootKey);
        initializePreferences();
        disableCadenceIfNeeded();
        disableAltitudeSourceIfNeeded();
        disableSocialNetworksIfNeeded();
        disableHearRateSensorIfNeeded();
        disablePowerManagementIfNeeded();

        String navigateToScreen = getArguments().getString(KEY_PREFERENCE_SCREEN);
        String navigateToPreferenceDialog = getArguments().getString(KEY_PREFERENCE_DIALOG);
        if (!TextUtils.isEmpty(navigateToPreferenceDialog) || !TextUtils.isEmpty(
            navigateToScreen)) {
            navigateTo(navigateToScreen, navigateToPreferenceDialog);
        }
    }

    @Override
    public void onNavigateToScreen(PreferenceScreen preferenceScreen) {
        super.onNavigateToScreen(preferenceScreen);

        String key = preferenceScreen.getKey();
        setToolbarTitleByScreenKey(key);
    }

    private void setToolbarTitleByScreenKey(String key) {
        if (getString(R.string.user_settings_category).equals(key)) {
            setToolbarTitle(getString(R.string.settings_general_user_settings));
            datahubAnalyticsTracker.trackUserProperty(
                AnalyticsUserProperty.GENDER,
                userSettingsController.getSettings().getGender() == Sex.MALE ? "Male"
                    : "Female");
        } else if (getString(R.string.heart_rate_screen).equals(key)) {
            setToolbarTitle(getString(R.string.settings_general_heart_rate_belt));
        } else if (getString(R.string.hr_intensity_zones).equals(key)) {
            setToolbarTitle(getString(R.string.settings_general_hr_intensity_zones));
        }
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Take appbar into account
        RecyclerView listView = getListView();
        listView.setPadding(
            listView.getPaddingLeft(),
            listView.getPaddingTop(),
            listView.getPaddingRight(),
            (int) getResources().getDimension(R.dimen.height_toolbar));

        initAppVersion();
    }

    private void initAppVersion() {
        VersionPreference preference = findPreference("app_version");
        if (preference != null) {
            appVersionViewModel.getNewVersionLive().observe(getViewLifecycleOwner(),
                info -> preference.updateAppVersionView(
                    CheckAppVersionRemoteApiKt.getShouldShowRedDot(info)));
            appVersionViewModel.checkAppVersion();
        }
    }

    private final SharedPreferences.OnSharedPreferenceChangeListener
        onSharedPreferenceChangeListener = (prefs, key) -> {
        if (key.equals("measurement_unit")) {
            AltitudeOffsetDialogPreference altitudePreference =
                (AltitudeOffsetDialogPreference) findPreference("altitude_offset");
            if (altitudePreference != null) {
                // measurement unit changed, update altitude offset view
                altitudePreference.updateSummary();
            }
        } else if (key.equals("map_provider")) {
            String providerName = prefs.getString(key, "mapbox");
            if (suuntoMaps.getProvider(providerName) != null) {
                suuntoMaps.setDefaultProvider(providerName);
            }
        }
    };

    @Override
    public void onResume() {
        super.onResume();
        sharedPreferences.registerOnSharedPreferenceChangeListener(
            onSharedPreferenceChangeListener);
    }

    @Override
    public void onPause() {
        super.onPause();
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(
            onSharedPreferenceChangeListener);
    }

    //override onClicks on preferences that need activity context
    @Override
    public boolean onPreferenceTreeClick(Preference preference) {
        Timber.d("Preference clicked %s", preference.getKey());
        String prefKey = preference.getKey();
        if (prefKey != null) {
            switch (preference.getKey()) {
                case "log_out":
                    onLogoutClicked();
                    break;
                case "birth_date":
                    onBirthDateClicked();
                    break;
                case "app_version":
                    onAppAVersionClicked();
                    break;
                case "key_promo_suunto_sensor":
                    datahubAnalyticsTracker.trackEvent(AnalyticsEvent.HR_BELT_BUY_ONLINE_TAPPED);
                    break;
                case "privacy_policy":
                    datahubAnalyticsTracker.trackEvent(AnalyticsEvent.ABOUT_PRIVACY_POLICY);
                    break;
                case "service_terms":
                    datahubAnalyticsTracker.trackEvent(AnalyticsEvent.ABOUT_SERVICE_TERMS);
                    break;
                case "data_practices":
                    datahubAnalyticsTracker.trackEvent(AnalyticsEvent.ABOUT_DATA_PRACTICES);
                    break;
            }
        }

        return super.onPreferenceTreeClick(preference);
    }

    private void onAppAVersionClicked() {
        startActivity(AppUpdatesActivity.newStartIntent(requireContext(),
            AppUpdatesActivity.SOURCE_SETTINGS));
    }

    private void onBirthDateClicked() {
        userSettingsTrackerForAnalytics.trackBirthDate();
    }

    private void onLogoutClicked() {
        String dialogTitle = getString(R.string.dialog_title_settings_service_sign_out,
            currentUserController.getUsername());
        if (getContext() != null) {
            new AlertDialog.Builder(getContext())
                .setCancelable(true)
                .setTitle(dialogTitle)
                .setMessage(R.string.dialog_message_settings_service_sign_out)
                .setPositiveButton(R.string.positive_button_settings_service_sign_out,
                    (dialog, which) -> logout())
                .setNegativeButton(R.string.negative_button_settings_service_sign_out, null)
                .show();
        }
    }

    private void logout() {
        disposable.add(
            logoutTask.get().logoutWithProgressDialog(requireContext(), getChildFragmentManager())
                .subscribe(
                    () -> Timber.d("Logout success"),
                    (error) -> Timber.w(error, "Logout failed")
                )
        );
    }

    protected void initializePreferences() {
        servicePreferenceCategory = (PreferenceCategory) findPreference("service_category");
        otherCategory = (PreferenceCategory) findPreference(getString(R.string.other_category));
        aboutPreferenceCategory = findPreference(getString(R.string.about_category));
        generalCategory = (PreferenceCategory) findPreference(getString(R.string.general_category));
        logOutPreference = findPreference("log_out");
        Preference menstrualCyclePreference = findPreference(getString(R.string.menstrual_cycle));
        if (!BrandFlavourConstants.PROVIDE_MC_FEATURE && menstrualCyclePreference != null) {
            generalCategory.removePreference(menstrualCyclePreference);
        }
    }

    private void disableCadenceIfNeeded() {
        if (!BleHelper.supportsBle(getContext())) {
            Preference cadencePreference = findPreference("cadence");
            if (cadencePreference != null) {
                cadencePreference.setEnabled(false);
                cadencePreference.setSummary(R.string.settings_cadence_not_supported_summary);
            }
        }
    }

    private void disableAltitudeSourceIfNeeded() {
        if (!UpdatePressureTask.hasPressureSensor(sensorManager)) {
            Preference altitudeSourcePreference = findPreference("altitude_source");
            if (altitudeSourcePreference != null) {
                altitudeSourcePreference.setEnabled(false);
                altitudeSourcePreference.setSummary(
                    R.string.settings_altitude_not_supported_summary);
            }
        }
    }

    private void disableSocialNetworksIfNeeded() {
        if (!getResources().getBoolean(R.bool.showSocialWorkoutSharing)) {
            for (String key : socialKeys) {
                Preference socialPreference = findPreference(key);
                if (socialPreference != null) {
                    otherCategory.removePreference(socialPreference);
                }
            }
        }
    }

    private void disableHearRateSensorIfNeeded() {
        if (!BluetoothUtils.isBluetoothSupported(requireContext())) {
            Preference preference =
                findPreference(requireContext().getString(R.string.heart_rate_screen));
            if (preference != null) {
                generalCategory.removePreference(preference);
            }
        }
    }

    private void disablePowerManagementIfNeeded() {
        if (!BluetoothUtils.isBluetoothSupported(requireContext())
            && WindowsSubsystemForAndroidUtils.isWindowsSubsystemForAndroid) {
            Preference preference =
                findPreference(requireContext().getString(R.string.power_management_key));
            if (preference != null) {
                generalCategory.removePreference(preference);
            }
        }
    }

    protected void updateLogOutPreference() {
        if (aboutPreferenceCategory == null || logOutPreference == null) {
            return;
        }

        if (currentUserController.isLoggedIn()) {
            aboutPreferenceCategory.addPreference(logOutPreference);
        } else {
            aboutPreferenceCategory.removePreference(logOutPreference);
        }
    }

    private void updateCadenceDistanceIfPossible() {
        if (BleHelper.supportsBle(getContext())) {
            float totalDistance = CadenceHelper.getTotalDistance(getContext(),
                userSettingsController.getSettings().getWheelCircumference());
            MeasurementUnit unit = userSettingsController.getSettings().getMeasurementUnit();
            Preference cadenceTotalPreference = findPreference("cadence_total_distance");
            if (cadenceTotalPreference != null) {
                cadenceTotalPreference.setSummary(String.format(Locale.getDefault(), "%s %s",
                    TextFormatter.formatDistance(unit.toDistanceUnit(totalDistance)),
                    getString(unit.getDistanceUnit())));
            }
        }
    }

    @Override
    public void onStop() {
        localBM.unregisterReceiver(userStatusChangedReceiver);
        userSettingsController.unregisterUserSettingsUpdater(getContext(), userSettingsUpdater);
        userSettingsController.removeUpdateListener(this);
        super.onStop();
    }

    @Override
    public void onStart() {
        super.onStart();
        datahubAnalyticsTracker.trackEvent(AnalyticsEvent.SETTINGS_SCREEN);
        emarsysAnalytics.trackEvent(AnalyticsEvent.SETTINGS_SCREEN);

        updateLogOutPreference();
        updateCadenceDistanceIfPossible();

        localBM.registerReceiver(userStatusChangedReceiver,
            new IntentFilter(STTConstants.BroadcastActions.USER_STATUS_CHANGED));
        userSettingsUpdater = userSettingsController.registerUserSettingsUpdater(getContext());
        userSettingsController.addUpdateListener(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        disposable.clear();
    }

    @Override
    public void moveTo(int position) {
        RecyclerView view = getListView();
        //make sure recyclerview is ready
        if (view != null) {
            view.scrollToPosition(0);
        }
    }

    @Override
    public void onSettingsStoredToPreferences(boolean didLocalChanges) {
        if (!didLocalChanges) return;

        // Check if the country has been changed to US and then fire up the state selection if
        // applicable
        if (userSettingsController.getSettings().getCountry().equals(Locale.US.getCountry())
            && getContext() != null && userSettingsController.getSettings()
            .getCountrySubdivision()
            .isEmpty()
        ) {
            startActivity(CountrySubdivisionListActivity.newStartIntent(
                getContext(),
                false,
                true,
                AnalyticsPropertyValue.UsStateContext.US_AS_NEW_LOCATION_IN_SETTINGS
            ));
        }
    }

    private void navigateTo(String preferenceScreenKey, String preferenceDialogKey) {
        if (!TextUtils.isEmpty(preferenceScreenKey)) {
            Preference screen = findPreference(preferenceScreenKey);
            if (screen instanceof PreferenceScreen) {
                setToolbarTitleByScreenKey(preferenceScreenKey);
                setPreferenceScreen((PreferenceScreen) screen);
            }
        }
        if (!com.helpshift.util.TextUtils.isEmpty(preferenceDialogKey)) {
            Preference preference = findPreference(preferenceDialogKey);
            if (preference != null) {
                onDisplayPreferenceDialog(preference);
            }
        }
    }

    private void setToolbarTitle(String title) {
        ActionBar actionBar = null;
        Activity activity = getActivity();
        if (activity instanceof AppCompatActivity) {
            actionBar = ((AppCompatActivity) activity).getSupportActionBar();
        }
        if (actionBar != null) {
            actionBar.setTitle(title);
        }
    }
}
