package com.stt.android.home.dashboardv2.ui.activities

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.newfeed.ExploreCardData
import com.stt.android.ui.components.workout.WorkoutCard

@Composable
internal fun Explore(
    exploreCard: ExploreCardData,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    modifier: Modifier = Modifier,
    analyticsSource: String? = null,
) {
    Column(
        modifier = modifier,
    ) {
        HorizontalDivider()

        Text(
            text = stringResource(R.string.shared_nearby),
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLargeBold,
        )

        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.spacing.medium),
        ) {
            items(exploreCard.workoutCards) { workoutCardInfo ->
                WorkoutCard(
                    viewData = workoutCardInfo.workoutCardViewData,
                    onClick = { viewEvent(DashboardScreenViewEvent.OpenWorkout(workoutCardInfo.workoutHeader, analyticsSource)) },
                    modifier = Modifier
                        .padding(start = MaterialTheme.spacing.medium)
                        .width(312.dp),
                    onUserClick = { username -> viewEvent(DashboardScreenViewEvent.OpenUser(username)) },
                )
            }

            item {
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }
        }

        HorizontalDivider()
    }
}
