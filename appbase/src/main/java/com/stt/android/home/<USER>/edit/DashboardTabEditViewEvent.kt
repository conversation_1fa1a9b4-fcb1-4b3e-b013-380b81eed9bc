package com.stt.android.home.dashboardv2.edit

import com.stt.android.home.dashboardv2.widgets.WidgetType

internal sealed interface DashboardTabEditViewEvent {
    data object Close : DashboardTabEditViewEvent

    data class UpdateWidget(
        val toBe: List<WidgetType>,
    ) : DashboardTabEditViewEvent

    data object OpenAddWidget : DashboardTabEditViewEvent

    data class ToggleShowLatestWorkout(
        val toShow: <PERSON>olean,
    ) : DashboardTabEditViewEvent
}
