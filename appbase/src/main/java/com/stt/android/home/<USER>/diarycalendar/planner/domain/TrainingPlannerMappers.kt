package com.stt.android.home.diary.diarycalendar.planner.domain

import com.google.common.collect.Comparators.max
import com.soy.algorithms.impact.WorkoutImpact
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.DateInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.DistanceInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.DurationInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.GenerateTrainingPlanResponseRemote
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.GetTrainingPlannerCatalogueResponse
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.GetTrainingPlannerPlanByPlanIdResponse
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.GetTrainingPlannerProgramDetailsResponse
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.MultipleChoiceAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.NumberInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.PaceInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.RemotePlannedWorkouts
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.RemoteWeeklyProgram
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.RemoteWeeklyTargets
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.SingleChoiceAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.SpeedInputAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.SportSelectionAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlanAnswer
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramDetailsRemoteEventInfo
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramDetailsRemoteOption
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramDetailsRemoteQuestion
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramDetailsRemoteQuestionnaire
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramRemoteCategory
import com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api.TrainingPlannerProgramRemotePlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.GenerateTrainingPlanResponse
import com.stt.android.home.diary.diarycalendar.planner.domain.models.PlannedWorkout
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlanLevel
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlanStatus
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerCatalogue
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramCategory
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetails
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetailsEventInfo
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetailsQuestionnaire
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.WeeklyProgram
import com.stt.android.home.diary.diarycalendar.planner.domain.models.WeeklyTargets
import com.stt.android.home.diary.diarycalendar.planner.models.DistanceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Option
import com.stt.android.home.diary.diarycalendar.planner.models.PaceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.SpeedMeasurement
import com.stt.android.utils.takeIfNotEmpty
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.UUID

private const val DISTANCE_DEFAULT_MIN_VALUE_IN_METERS = 0
private const val DISTANCE_DEFAULT_MAX_VALUE_IN_METERS = 1_000_000

private const val DURATION_DEFAULT_MIN_VALUE_IN_SECONDS = 0

const val SPORTS_SELECTION_DEFAULT_MAX_VALUE = 100
private val SPORTS_SELECTION_DEFAULT_SELECTABLE_SPORTS =
    CoreActivityType.entries

private val DATE_DEFAULT_MIN_SELECTABLE_DATE = LocalDate.now()
private val DATE_DEFAULT_MAX_SELECTABLE_DATE = LocalDate.now().plusMonths(9)

fun GetTrainingPlannerProgramDetailsResponse.toDomain(
    unit: MeasurementUnit,
): TrainingPlannerProgramDetails {
    return TrainingPlannerProgramDetails(
        id = header.id,
        version = header.version.orEmpty(),
        questionnaire = questionnaire?.toDomain(unit) ?: TrainingPlannerProgramDetailsQuestionnaire.EMPTY,
        bannerUrl = bannerUrl.orEmpty(),
        eventInfo = eventInfo?.toDomain(),
        description = description.orEmpty(),
        durationWeeks = header.durationWeeks ?: 0,
        name = header.name,
        richInfo = richInfo.orEmpty(),
        sports = header.sports?.mapNotNull {
            try {
                CoreActivityType.valueOf(it)
            } catch (e: Exception) {
                null
            }
        }.orEmpty(),
        level = TrainingPlanLevel.entries.find { it.name == header.level },
        focus = header.focus,
    )
}

fun TrainingPlannerProgramDetailsRemoteQuestionnaire.toDomain(
    unit: MeasurementUnit,
): TrainingPlannerProgramDetailsQuestionnaire {
    return TrainingPlannerProgramDetailsQuestionnaire(
        version = version,
        questions = questions.mapNotNull {
            it.toDomain(unit)
        },
    )
}

fun TrainingPlannerProgramDetailsRemoteQuestion.toDomain(
    unit: MeasurementUnit,
): Question? {
    return when (type) {
        "singlechoice" -> Question.SingleChoice(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            options = options?.mapNotNull { option: TrainingPlannerProgramDetailsRemoteOption ->
                option.toDomain()
            }.orEmpty(),
        )

        "multiplechoice" -> Question.MultiChoice(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            options = options?.mapNotNull { option ->
                option.toDomain()
            }.orEmpty(),
        )

        "dateinput" -> {
            val now = LocalDate.now()
            val minDate = minDate?.toLocalDate() ?: DATE_DEFAULT_MIN_SELECTABLE_DATE
            val maxDate = maxDate?.toLocalDate() ?: DATE_DEFAULT_MAX_SELECTABLE_DATE
            val date = date?.toLocalDate()?.takeIf { it in minDate..maxDate && it >= now } ?: now
            Question.ValueInput.Date(
                id = id,
                title = title,
                subtitle = subTitle,
                coachNote = coachNote?.text,
                summaryTitle = summaryTitle ?: title,
                valueInMillis = date.toUtcMillis(),
                minDate = max(minDate, now),
                maxDate = maxDate,
            )
        }

        "numberinput" -> Question.ValueInput.Number(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            value = value
        )

        "durationinput" -> {
            val (hours, minutes) = durationInSeconds?.let { seconds ->
                val hours = seconds / 3600
                val minutes = (seconds % 3600) / 60

                hours to minutes
            } ?: (null to null)
            Question.ValueInput.Duration(
                id = id,
                title = title,
                subtitle = subTitle,
                coachNote = coachNote?.text,
                summaryTitle = summaryTitle ?: title,
                hours = hours,
                minutes = minutes,
                minDurationInSeconds = minValue
                    ?: DURATION_DEFAULT_MIN_VALUE_IN_SECONDS
            )
        }

        "distanceinput" -> Question.ValueInput.Distance(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            value = DistanceMeasurement(meters = distanceInMeters ?: 0),
            measurementUnit = unit,
            minValue = DistanceMeasurement(
                meters = minValue ?: DISTANCE_DEFAULT_MIN_VALUE_IN_METERS
            ),
            maxValue = DistanceMeasurement(
                meters = maxValue ?: DISTANCE_DEFAULT_MAX_VALUE_IN_METERS
            ),
        )

        "sportsselection" -> Question.Sports(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            maxSelectedCount = maxSelectedCount
                ?: SPORTS_SELECTION_DEFAULT_MAX_VALUE,
            selectableSports = options?.takeIfNotEmpty()?.map {
                CoreActivityType.valueOf(it.id.toInt())
            }
                ?: SPORTS_SELECTION_DEFAULT_SELECTABLE_SPORTS,
        )

        "paceinput" -> Question.ValueInput.Pace(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            value = PaceMeasurement(
                secondsPerKm = value?.toDouble() ?: 0.0
            ),
            measurementUnit = unit,
        )

        "speedinput" -> Question.ValueInput.Speed(
            id = id,
            title = title,
            subtitle = subTitle,
            coachNote = coachNote?.text,
            summaryTitle = summaryTitle ?: title,
            value = SpeedMeasurement(
                metersPerSec = value?.toDouble() ?: 0.0
            ),
            measurementUnit = unit,
        )

        else -> {
            println("Questions type [$type] is not supported")
            null // TODO: For Backward compatibility, we need to somehow have a fallback form
        }
    }
}

fun TrainingPlannerProgramDetailsRemoteOption.toDomain(): Option? {
    if (text.isNullOrBlank()) {
        return null
    }

    return Option(
        id = id,
        text = text
    )
}

fun GetTrainingPlannerPlanByPlanIdResponse.toDomain(unit: MeasurementUnit): TrainingPlan {
    // durationWeeks and startDate can be null if the plan is still being generated. Otherwise
    // they should always contain values
    val durationWeeks = durationWeeks ?: 0
    val startDate = startDate ?: LocalDate.now().toString()

    return TrainingPlan(
        id = id,
        metaPlanId = metaPlanId,
        name = name,
        durationWeeks = durationWeeks,
        startDate = startDate.toLocalDate(),
        targetDate = targetDate?.toLocalDate(),
        status = when (status.uppercase()) {
            "ACTIVE" -> TrainingPlanStatus.ACTIVE
            "BEING_GENERATED" -> TrainingPlanStatus.BEING_GENERATED
            "GENERATION_FAILED" -> TrainingPlanStatus.GENERATION_FAILED
            "CANCELLED" -> TrainingPlanStatus.CANCELLED
            "FINISHED" -> TrainingPlanStatus.FINISHED
            else -> TrainingPlanStatus.UNKNOWN
        },
        weeklyPrograms = weeklyPrograms.map { it.toDomain() },
        answers = answers ?: emptyList(),
        questions = questions?.mapNotNull { it.toDomain(unit) } ?: emptyList(),
        metaPlanHeader = metaPlanHeader?.toDomain(),
        description = description,
        richInfo = richInfo,
        eventInfo = eventInfo?.toDomain(),
    )
}

fun RemoteWeeklyProgram.toDomain(): WeeklyProgram {
    return WeeklyProgram(
        weekNumber = weekNumber,
        weeklyTargets = weeklyTargets.toDomain(),
        plannedWorkouts = plannedWorkouts.map { it.toDomain() },
        goal = goal,
        note = note,
    )
}

fun RemoteWeeklyTargets.toDomain(): WeeklyTargets {
    return WeeklyTargets(
        distanceInMeters = distanceInMeters,
        duration = duration ?: 0,
        trainingLoad = trainingLoad ?: 0
    )
}

fun RemotePlannedWorkouts.toDomain(): PlannedWorkout {
    return PlannedWorkout(
        id = UUID.randomUUID().toString(),
        activityType = CoreActivityType.valueOf(activityId),
        durationInSeconds = duration,
        estimatedDistanceInMeters = estimatedDistanceInMeters,
        intensityZone = intensityZone ?: 0,
        impacts = impacts?.mapNotNull { impactFromString(it) }.orEmpty(),
        name = name,
        notes = notes,
        trainingDate = trainingDate.toLocalDate(),
        trainingStressScore = trainingStressScore,
        targetPace = targetPace,
        targetHeartRate = targetHeartRate,
        targetPower = targetPower,
        avgSpeed = avgSpeed,
    )
}

private fun impactFromString(impact: String): WorkoutImpact? =
    WorkoutImpact.entries.find { it.name.equals(impact, ignoreCase = true) }

fun GetTrainingPlannerCatalogueResponse.toDomain(): TrainingPlannerCatalogue {
    val plansMap = plans.associateBy { it.id }
    return TrainingPlannerCatalogue(
        categories = categories.map {
            it.toDomain(plansMap)
        }
    )
}

fun TrainingPlannerProgramRemoteCategory.toDomain(plansMap: Map<String, TrainingPlannerProgramRemotePlan>): TrainingPlannerProgramCategory {
    return TrainingPlannerProgramCategory(
        id = this.id,
        iconUrl = this.iconUrl,
        name = this.name,
        plans = this.refs.mapNotNull {
            plansMap[it]?.toDomain()
        }
    )
}

fun TrainingPlannerProgramRemotePlan.toDomain(): TrainingPlannerProgramPlan {
    return TrainingPlannerProgramPlan(
        thumbnailUrl = thumbnailUrl,
        durationWeeks = durationWeeks ?: 0,
        id = id,
        sports = sports?.mapNotNull {
            try {
                CoreActivityType.valueOf(it)
            } catch (e: Exception) {
                null
            }
        }.orEmpty(),
        name = name,
        version = version.orEmpty(),
        focus = focus,
        level = TrainingPlanLevel.entries.find { it.name == level },
    )
}

fun TrainingPlannerProgramDetailsRemoteEventInfo.toDomain(): TrainingPlannerProgramDetailsEventInfo {
    return TrainingPlannerProgramDetailsEventInfo(
        name = name,
        date = date,
        richInfo = richInfo,
        distance = distance,
        ascent = ascent,
        terrain = terrain,
        weather = weather
    )
}

fun Question.toRequestQuestion(): TrainingPlanAnswer {
    return when (this) {
        is Question.MultiChoice -> MultipleChoiceAnswer(
            questionId = id,
            choiceIds = answers.map { it.id }
        )

        is Question.SingleChoice -> SingleChoiceAnswer(
            questionId = id,
            choiceId = answer?.id
                ?: throw IllegalStateException("Answer is null for SingleChoice with id=$id")
        )

        is Question.Sports -> SportSelectionAnswer(
            questionId = id,
            sportIds = this.selectedSports.map { it.id.toString() }
        )

        is Question.ValueInput.Date -> DateInputAnswer(
            questionId = id,
            date = this.valueInMillis?.toLocalDate()
                ?: throw IllegalStateException("valueInMillis is null for Date with id=$id")
        )

        is Question.ValueInput.Distance -> DistanceInputAnswer(
            questionId = id,
            distanceInMeters = this.value.meters
        )

        is Question.ValueInput.Duration -> DurationInputAnswer(
            questionId = id,
            durationInSeconds = this.durationInSeconds
                ?: throw IllegalStateException("durationInSeconds is null for Duration with id=$id")
        )

        is Question.ValueInput.Number -> NumberInputAnswer(
            questionId = id,
            value = this.value
                ?: throw IllegalStateException("value is null for Number with id=$id")
        )

        is Question.ValueInput.Pace -> PaceInputAnswer(
            questionId = id,
            paceInSecondsPerKm = this.value.secondsPerKm
        )

        is Question.ValueInput.Speed -> SpeedInputAnswer(
            questionId = id,
            speedInMetersPerSec = this.value.metersPerSec
        )
    }
}

fun GenerateTrainingPlanResponseRemote.toDomain(): GenerateTrainingPlanResponse {
    return GenerateTrainingPlanResponse(
        id = id,
        text = text
    )
}

fun String.toLocalDate(): LocalDate = LocalDate.parse(
    this,
    DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.getDefault())
)

private fun LocalDate.toUtcMillis(): Long {
    return this.atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli()
}
