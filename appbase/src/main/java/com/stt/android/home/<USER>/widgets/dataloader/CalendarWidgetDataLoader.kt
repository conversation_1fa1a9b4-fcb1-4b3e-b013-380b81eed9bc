package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.controllers.userSettings
import com.stt.android.domain.diarycalendar.DailyWorkoutStatisticsWithSummary
import com.stt.android.domain.diarycalendar.DiaryCalendarDailyData
import com.stt.android.domain.diarycalendar.DiaryCalendarTotalValues
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.home.dashboardv2.widgets.CalendarWidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.home.diary.diarycalendar.planner.usercases.PlannedWorkoutEnhancer
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.ui.map.selection.toGetWorkoutStatisticsWithSummaryUseCaseParams
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.CalendarUtils
import com.stt.android.utils.FixedFirstDayOfTheWeekCalendarProvider
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT
import com.stt.android.utils.iterator
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.Locale
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

internal class CalendarWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase,
    private val diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder,
    private val sharedPreferences: SharedPreferences,
    private val calendarProvider: CalendarProvider,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val plannedWorkoutEnhancer: PlannedWorkoutEnhancer,
) : WidgetDataLoader<CalendarWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): CalendarWidgetInfo? {
        return when (type) {
            WidgetType.CALENDAR_THIS_WEEK -> mockBubbleDataForWeek(MyTracksGranularity.Type.THIS_WEEK)
            WidgetType.CALENDAR_THIS_MONTH -> mockBubbleDataForMonth(MyTracksGranularity.Type.THIS_MONTH)
            WidgetType.CALENDAR_LAST_30_DAYS -> mockBubbleDataForMonth(MyTracksGranularity.Type.LAST_30_DAYS)
            else -> null
        }
    }

    override suspend fun realLoad(param: Param): WidgetData<CalendarWidgetInfo> =
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(
                workoutHeaderController.currentUserWorkoutUpdated
                    .onStart { emit(Unit) },
                userSettingsController.userSettings()
                    .distinctUntilChangedBy(UserSettings::getFirstDayOfTheWeek),
            ) { _, _ -> loadWidgetInfo(param) }
                .flowOn(coroutinesDispatchers.io),
        )

    private suspend fun loadWidgetInfo(param: Param): CalendarWidgetInfo {
        val granularity = when (param.type) {
            WidgetType.CALENDAR_THIS_WEEK -> MyTracksGranularity.Type.THIS_WEEK
            WidgetType.CALENDAR_THIS_MONTH -> MyTracksGranularity.Type.THIS_MONTH
            WidgetType.CALENDAR_LAST_30_DAYS -> MyTracksGranularity.Type.LAST_30_DAYS
            else -> throw IllegalStateException("Unsupported param: $param")
        }
        val username = currentUserController.username
        val showPredictions = sharedPreferences.getBoolean(
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS,
            KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS_DEFAULT,
        )
        val getWorkoutParams = granularity
            .toGetWorkoutStatisticsWithSummaryUseCaseParams(
                username,
                showPredictions,
                calendarProvider
            )
        val summary = getWorkoutStatisticsWithSummaryUseCase(getWorkoutParams)

        // Enhance data with planned workouts if endDate is today or later
        val enhancedSummary = if (getWorkoutParams.endDay >= LocalDate.now()) {
            plannedWorkoutEnhancer.enhanceWithPlannedWorkouts(
                summary,
                getWorkoutParams.firstDay,
                getWorkoutParams.endDay
            )
        } else {
            summary
        }

        val weekDayLabels = CalendarUtils.buildDayOfWeekLabels(
            LocalDate.now(),
            calendarProvider.getDayOfWeekField()
        )
        val dataForGranularity = createDiaryBubbleData(
            data = enhancedSummary,
            startDate = getWorkoutParams.firstDay,
            endDate = getWorkoutParams.endDay,
            weekDayLabels = weekDayLabels,
            diaryCalendarListContainerBuilder = diaryCalendarListContainerBuilder
        )
        return CalendarWidgetInfo(
            calendarProvider = calendarProvider,
            granularityType = granularity,
            bubbleData = dataForGranularity,
        )
    }

    private val Int.h get() = this.hours.inWholeMilliseconds

    private val Int.m get() = this.minutes.inWholeMilliseconds

    private fun mockDay(
        year: Int,
        month: Int,
        day: Int,
        group: ActivityGroup? = null,
        duration: Long = 0L
    ): Pair<LocalDate, DiaryCalendarDailyData> =
        LocalDate.of(year, month, day) to if (group == null) {
            DiaryCalendarDailyData.EMPTY
        } else {
            DiaryCalendarDailyData.EMPTY.copy(
                durationByActivityGroup = mapOf(group to duration),
                workoutIds = listOf(1),
            )
        }

    private fun buildMockWidgetInfo(
        granularity: MyTracksGranularity.Type,
        startDate: LocalDate,
        endDate: LocalDate,
        mockData: Map<LocalDate, DiaryCalendarDailyData>
    ): CalendarWidgetInfo {
        val calendarProvider = FixedFirstDayOfTheWeekCalendarProvider(
            Locale.getDefault(),
            DayOfWeek.MONDAY,
        )
        val weekDayLabels = CalendarUtils.buildDayOfWeekLabels(
            startDate,
            calendarProvider.getDayOfWeekField(),
        )
        return CalendarWidgetInfo(
            calendarProvider = calendarProvider,
            granularityType = granularity,
            bubbleData = createDiaryBubbleData(
                DailyWorkoutStatisticsWithSummary(
                    mockData,
                    emptyMap(),
                    DiaryCalendarTotalValues.EMPTY,
                    emptyList(),
                ),
                startDate,
                endDate,
                weekDayLabels,
                diaryCalendarListContainerBuilder,
            ),
            currentLocalDate = endDate,
        )
    }

    private fun mockBubbleDataForWeek(
        granularity: MyTracksGranularity.Type,
        startDate: LocalDate = LocalDate.of(2025, 6, 2)
    ): CalendarWidgetInfo {
        val mockData = mapOf(
            mockDay(2025, 6, 2, ActivityGroup.OutdoorAdventures, 1.h),
            mockDay(2025, 6, 3),
            mockDay(2025, 6, 4, ActivityGroup.Running, 1.h + 20.m),
            mockDay(2025, 6, 5, ActivityGroup.OutdoorAdventures, 1.h + 20.m),
            mockDay(2025, 6, 6),
            mockDay(2025, 6, 7, ActivityGroup.Running, 1.h + 25.m),
            mockDay(2025, 6, 8, ActivityGroup.Cycling, 3.h + 54.m),
        )
        return buildMockWidgetInfo(
            granularity,
            startDate,
            startDate.plusDays(6),
            mockData
        )
    }

    private fun mockBubbleDataForMonth(
        granularity: MyTracksGranularity.Type
    ): CalendarWidgetInfo {
        val startDate = LocalDate.of(2025, 6, 1)
        val mockData = mapOf(
            mockDay(2025, 6, 1, ActivityGroup.Running, 50.m),
            mockDay(2025, 6, 2, ActivityGroup.Cycling, 110.m),
            mockDay(2025, 6, 3, ActivityGroup.OutdoorAdventures, 2.h),
            mockDay(2025, 6, 4),
            mockDay(2025, 6, 5, ActivityGroup.Running, 50.m),
            mockDay(2025, 6, 6, ActivityGroup.Cycling, 110.m),
            mockDay(2025, 6, 7),
            mockDay(2025, 6, 8, ActivityGroup.Running, 70.m),
            mockDay(2025, 6, 9, ActivityGroup.Cycling, 150.m),
            mockDay(2025, 6, 10, ActivityGroup.OutdoorAdventures, 3.h),
            mockDay(2025, 6, 11, ActivityGroup.Unspecified, 20.m),
            mockDay(2025, 6, 12),
            mockDay(2025, 6, 13, ActivityGroup.Running, 50.m),
            mockDay(2025, 6, 14, ActivityGroup.Cycling, 130.m),
            mockDay(2025, 6, 15, ActivityGroup.OutdoorAdventures, 2.h),
            mockDay(2025, 6, 16, ActivityGroup.Unspecified, 30.m),
            mockDay(2025, 6, 17),
            mockDay(2025, 6, 18, ActivityGroup.Running, 50.m),
            mockDay(2025, 6, 19, ActivityGroup.Cycling, 150.m),
            mockDay(2025, 6, 20, ActivityGroup.OutdoorAdventures, 3.h),
            mockDay(2025, 6, 21, ActivityGroup.Unspecified, 20.m),
            mockDay(2025, 6, 22),
            mockDay(2025, 6, 23, ActivityGroup.Running, 70.m),
            mockDay(2025, 6, 24, ActivityGroup.Cycling, 110.m),
            mockDay(2025, 6, 25, ActivityGroup.OutdoorAdventures, 2.h),
            mockDay(2025, 6, 26, ActivityGroup.Unspecified, 30.m),
            mockDay(2025, 6, 27),
            mockDay(2025, 6, 28, ActivityGroup.Running, 50.m),
            mockDay(2025, 6, 29, ActivityGroup.Cycling, 130.m),
            mockDay(2025, 6, 30, ActivityGroup.OutdoorAdventures, 3.h),
        )
        return buildMockWidgetInfo(
            granularity,
            startDate,
            startDate.withDayOfMonth(30),
            mockData
        )
    }
}

internal fun createDiaryBubbleData(
    data: DailyWorkoutStatisticsWithSummary,
    startDate: LocalDate,
    endDate: LocalDate,
    weekDayLabels: List<String>,
    diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder
) = DiaryBubbleData(
    startDate = startDate,
    endDate = endDate,
    bubbles = createCalendarData(data, startDate, endDate, diaryCalendarListContainerBuilder),
    weekDayLabels = weekDayLabels,
    onMonthClicked = {}
)

private fun createCalendarData(
    data: DailyWorkoutStatisticsWithSummary,
    startDate: LocalDate,
    endDate: LocalDate,
    diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder
): List<DiaryBubbleContainer> {
    val bubbleList = mutableListOf<DiaryBubbleContainer>()
    for (day in startDate..endDate) {
        val diaryCalendarDailyData = data.dailyData[day] ?: DiaryCalendarDailyData.EMPTY
        bubbleList.add(
            DiaryBubbleContainer(
                id = day.toString(),
                startDate = day,
                bubbleType = diaryCalendarListContainerBuilder.getBubbleType(
                    day,
                    diaryCalendarDailyData
                ),
                dayData = diaryCalendarDailyData,
                onDateClicked = {}
            )
        )
    }
    return bubbleList
}
