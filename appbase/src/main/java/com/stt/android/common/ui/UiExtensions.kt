package com.stt.android.common.ui

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData

/**
 * Extension function for observing [LiveData]. Originally from <PERSON>' Tivi
 */
inline fun <T> LiveData<T>.observeK(owner: LifecycleOwner, crossinline observer: (T?) -> Unit) {
    this.observe(owner) { observer(it) }
}

/**
 * Extension function for filtering out null values emited by [LiveData]
 */
inline fun <T : Any> LiveData<T?>.observeWhenNotNull(
    owner: LifecycleOwner,
    crossinline observer: (T) -> Unit
) {
    this.observe(owner) { maybeIt ->
        maybeIt?.let {
            observer(it)
        }
    }
}

/**
 * Extension function for filtering out null values emited by [LiveData]
 */
inline fun <T> LiveData<T>.observeNotNull(
    owner: LifecycleOwner,
    crossinline observer: (T) -> Unit
) {
    this.observe(owner) { maybeIt ->
        maybeIt?.let {
            observer(it)
        }
    }
}

/**
 * Extension function for debouncing values emited by [LiveData], limiting the rate
 * how fast new values can be emited. Interactive example how it behaves can be sen at
 * https://rxmarbles.com/#debounce
 * Originally from https://gist.github.com/lupajz/43068881d949e207d5efe2c40bde1866
 */
fun <T> LiveData<T>.debounce(duration: Long = 1000L) = MediatorLiveData<T>().also { mediator ->
    val source = this
    val handler = Handler(Looper.getMainLooper())
    val runnable = Runnable {
        mediator.value = source.value
    }
    mediator.addSource(source) {
        handler.removeCallbacks(runnable)
        handler.postDelayed(runnable, duration)
    }
}
