package com.stt.android.social.userprofileV2

import android.content.Intent
import androidx.annotation.StringRes
import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.friends.Friend
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler

data class UserProfileState(
    val user: User,
    val isCurrentUser: Boolean,
    val blockStatus: BlockStatus,
    val friend: Friend? = null,
    val uploadingAvatar: Boolean = false,
)

sealed interface UserProfileEvent

data object FollowerRemoved : UserProfileEvent
data object UserReported : UserProfileEvent
data object UserDuplicateReported : UserProfileEvent
data class UserProfileError(val throwable: Throwable) : UserProfileEvent

data class ConfirmDialogEvent(
    @StringRes val title: Int,
    @StringRes val message: Int,
    @StringRes val confirmText: Int,
    val onConfirm: () -> Unit,
) : UserProfileEvent

// Navigation Events
sealed interface NavigationEvent : UserProfileEvent {
    data object Back : NavigationEvent
    data object Edit : NavigationEvent
    data class AllActivity(val user: User) : NavigationEvent
    data object PersonalRecords : NavigationEvent
    data object Badges : NavigationEvent
    data object Search : NavigationEvent
    data class Followers(val count: Int) : NavigationEvent
    data class Following(val count: Int) : NavigationEvent
    data class FriendBadgesList(val username: String) : NavigationEvent
    data class BadgeImage(val badgeId: String) : NavigationEvent
    data class Menu(val menu: SettingMenuInfo) : NavigationEvent
    data class PremiumSubscription(val isSubscribed: Boolean) : NavigationEvent
    data class Workout(val workoutHeader: WorkoutHeader, val source: String) : NavigationEvent
}

// Workout Card Action Event
data class WorkoutCardActionEvent(val action: WorkoutCardActionsHandler.Action) : UserProfileEvent

// MVI Intent definitions
sealed interface UserProfileIntent

// Navigation Intents
data object BackClicked : UserProfileIntent
data object EditClicked : UserProfileIntent
data class AllActivityClicked(val user: User) : UserProfileIntent
data object PersonalRecordsClicked : UserProfileIntent
data object BadgesClicked : UserProfileIntent
data object SearchClicked : UserProfileIntent

// Social Intents
data class FollowersClicked(val count: Int) : UserProfileIntent
data class FollowingClicked(val count: Int) : UserProfileIntent
data object FollowButtonClicked : UserProfileIntent
data class FriendBadgesListClicked(val username: String) : UserProfileIntent
data class BadgeImageClicked(val badgeId: String) : UserProfileIntent

// Menu Intents
data class MenuClicked(val menu: SettingMenuInfo) : UserProfileIntent
data class MoreMenuItemClicked(val menu: MoreMenu) : UserProfileIntent
data class PremiumSubscriptionClicked(val isSubscribed: Boolean) : UserProfileIntent

// Workout Intents
data class WorkoutOpened(val workoutHeader: WorkoutHeader, val source: String) : UserProfileIntent
data class WorkoutCardAction(val action: WorkoutCardActionsHandler.Action) : UserProfileIntent

// Avatar Intent
data class UpdateAvatarClicked(val intent: Intent) : UserProfileIntent
