package com.stt.android.social.userprofileV2.ui

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.collectAsLazyPagingItems
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.UserProfileViewModel
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.social.workoutlistv2.WorkoutPageViewModel
import com.stt.android.social.workoutlistv2.ui.OtherUserProfileContent
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import java.io.File

@Composable
internal fun UserProfileScreen(
    viewModel: UserProfileViewModel,
    allWorkoutViewModel: AllWorkoutViewModel,
    workoutPageViewModel: WorkoutPageViewModel,
    menuList: List<SettingMenuInfo>,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit,
    onAllActivityClick: (User) -> Unit,
    onPersonalRecordsClick: () -> Unit,
    onBadgesClick: () -> Unit,
    onFriendBadgesListClick: (String) -> Unit,
    onBadgeImageClick: (String) -> Unit,
    onFollowersClicked: (Int) -> Unit,
    onFollowingClicked: (Int) -> Unit,
    onFollowButtonClickedV2: () -> Unit,
    onMenuClick: (SettingMenuInfo) -> Unit,
    onPremiumSubscriptionClick: (Boolean) -> Unit,
    openWorkout: (WorkoutHeader, String) -> Unit,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    onSearchClick: () -> Unit,
    modifier: Modifier = Modifier,
    onUpdateAvatarClicked: ((Intent) -> Unit)? = null,
    tempProfilePictureFile: File? = null,
) {
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val workoutPagingEnabled = workoutPageViewModel.enabled
    val friendBadgesListItems by viewModel.friendBadgesUiState.collectAsStateWithLifecycle()
    val followCountSummary by viewModel.followCountSummaryState.collectAsStateWithLifecycle()
    val userWorkoutSummaryState by viewModel.userWorkoutSummaryState.collectAsStateWithLifecycle()
    val deviceTypeList by viewModel.deviceTypeListState.collectAsStateWithLifecycle()
    val moreMenuList by viewModel.moreMenuListState.collectAsStateWithLifecycle()
    val premiumSubscriptionStatus by viewModel.premiumSubscriptionStatus.collectAsStateWithLifecycle()
    val myBadgesListItems by viewModel.myBadgesUiState.collectAsStateWithLifecycle()
    val friend = viewState.friend
    val blocked = viewState.blockStatus.isUserBlocked
    if (!workoutPagingEnabled || viewState.isCurrentUser) {
        val allWorkoutViewState by allWorkoutViewModel.viewState.collectAsStateWithLifecycle()
        UserProfileContent(
            modifier = modifier,
            user = viewState.user,
            isCurrentUser = viewState.isCurrentUser,
            measurementUnit = viewModel.measurementUnit,
            allWorkoutViewState = allWorkoutViewState,
            followCountSummary = followCountSummary,
            userWorkoutSummaryState = userWorkoutSummaryState,
            deviceTypeList = deviceTypeList,
            moreMenuList = moreMenuList,
            myBadgesItems = myBadgesListItems,
            premiumSubscriptionStatus = premiumSubscriptionStatus,
            settingMenuList = if (viewState.isCurrentUser) menuList else null,
            onBackClick = onBackClick,
            openWorkout = openWorkout,
            onMoreMenuItemClick = onMoreMenuItemClick,
            onEditClick = onEditClick,
            onAllActivityClick = {
                onAllActivityClick(viewState.user)
            },
            onPersonalRecordsClick = onPersonalRecordsClick,
            onFollowersClicked = onFollowersClicked,
            onFollowingClicked = onFollowingClicked,
            onFollowButtonClicked = onFollowButtonClickedV2,
            onMenuClick = onMenuClick,
            onPremiumSubscriptionClick = {
                onPremiumSubscriptionClick(premiumSubscriptionStatus?.isSubscribed == true)
            },
            onRetryClicked = allWorkoutViewModel::load,
            onSearchClick = onSearchClick,
            friend = friend,
            blocked = blocked,
            onUpdateProfilePictureClicked = if (viewState.isCurrentUser) onUpdateAvatarClicked else null,
            tempProfilePictureFile = if (viewState.isCurrentUser) tempProfilePictureFile else null,
            uploadingAvatar = viewState.uploadingAvatar,
            onBadgesClick = onBadgesClick,
            workoutCardActionsHandler = workoutCardActionsHandler,
            friendBadgesItems = friendBadgesListItems,
            onFriendBadgesListClick = onFriendBadgesListClick,
            onBadgeImageClick = onBadgeImageClick,
        )
    } else {
        val workoutPagingItems =
            workoutPageViewModel.workoutPagingDataFlow.collectAsLazyPagingItems()
        val mediaPagingItems = workoutPageViewModel.mediaPagingDataFlow.collectAsLazyPagingItems()
        val updatedCardList by workoutPageViewModel.updatedCardFlow.collectAsStateWithLifecycle()
        OtherUserProfileContent(
            user = viewState.user,
            workoutPagingItems = workoutPagingItems,
            mediaPagingItems = mediaPagingItems,
            workoutCardActionsHandler = workoutCardActionsHandler,
            modifier = modifier,
            userWorkoutSummaryState = userWorkoutSummaryState,
            friendBadgesItems = friendBadgesListItems,
            measurementUnit = viewModel.measurementUnit,
            premiumSubscriptionStatus = premiumSubscriptionStatus,
            followCountSummary = followCountSummary,
            deviceTypeList = deviceTypeList,
            moreMenuList = moreMenuList,
            friend = friend,
            blocked = blocked,
            openWorkout = openWorkout,
            onBackClick = onBackClick,
            onFollowersClicked = onFollowersClicked,
            onFollowingClicked = onFollowingClicked,
            onFollowButtonClicked = onFollowButtonClickedV2,
            updatedCardList = updatedCardList,
            onSearchClick = onSearchClick,
            onFriendBadgesListClick = onFriendBadgesListClick,
            onBadgeImageClick = onBadgeImageClick,
        )
    }
}
