package com.stt.android.social.userprofileV2.ui

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.paging.compose.collectAsLazyPagingItems
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofileV2.*
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.UserProfileViewModel
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.social.workoutlistv2.WorkoutPageViewModel
import com.stt.android.social.workoutlistv2.ui.OtherUserProfileContent
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import java.io.File

@Composable
internal fun UserProfileScreen(
    viewModel: UserProfileViewModel,
    allWorkoutViewModel: AllWorkoutViewModel,
    workoutPageViewModel: WorkoutPageViewModel,
    menuList: List<SettingMenuInfo>,
    onIntent: (UserProfileIntent) -> Unit,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    tempProfilePictureFile: File? = null,
) {
    val viewState by viewModel.viewState.collectAsStateWithLifecycle()
    val workoutPagingEnabled = workoutPageViewModel.enabled
    val friendBadgesListItems by viewModel.friendBadgesUiState.collectAsStateWithLifecycle()
    val followCountSummary by viewModel.followCountSummaryState.collectAsStateWithLifecycle()
    val userWorkoutSummaryState by viewModel.userWorkoutSummaryState.collectAsStateWithLifecycle()
    val deviceTypeList by viewModel.deviceTypeListState.collectAsStateWithLifecycle()
    val moreMenuList by viewModel.moreMenuListState.collectAsStateWithLifecycle()
    val premiumSubscriptionStatus by viewModel.premiumSubscriptionStatus.collectAsStateWithLifecycle()
    val myBadgesListItems by viewModel.myBadgesUiState.collectAsStateWithLifecycle()
    val friend = viewState.friend
    val blocked = viewState.blockStatus.isUserBlocked
    if (!workoutPagingEnabled || viewState.isCurrentUser) {
        val allWorkoutViewState by allWorkoutViewModel.viewState.collectAsStateWithLifecycle()
        UserProfileContent(
            modifier = modifier,
            user = viewState.user,
            isCurrentUser = viewState.isCurrentUser,
            measurementUnit = viewModel.measurementUnit,
            allWorkoutViewState = allWorkoutViewState,
            followCountSummary = followCountSummary,
            userWorkoutSummaryState = userWorkoutSummaryState,
            deviceTypeList = deviceTypeList,
            moreMenuList = moreMenuList,
            myBadgesItems = myBadgesListItems,
            premiumSubscriptionStatus = premiumSubscriptionStatus,
            settingMenuList = if (viewState.isCurrentUser) menuList else null,
            onBackClick = { onIntent(BackClicked) },
            openWorkout = { workoutHeader, source -> onIntent(WorkoutOpened(workoutHeader, source)) },
            onMoreMenuItemClick = { menu -> onIntent(MoreMenuItemClicked(menu)) },
            onEditClick = { onIntent(EditClicked) },
            onAllActivityClick = { onIntent(AllActivityClicked(viewState.user)) },
            onPersonalRecordsClick = { onIntent(PersonalRecordsClicked) },
            onFollowersClicked = { count -> onIntent(FollowersClicked(count)) },
            onFollowingClicked = { count -> onIntent(FollowingClicked(count)) },
            onFollowButtonClicked = { onIntent(FollowButtonClicked) },
            onMenuClick = { menu -> onIntent(MenuClicked(menu)) },
            onPremiumSubscriptionClick = {
                onIntent(PremiumSubscriptionClicked(premiumSubscriptionStatus?.isSubscribed == true))
            },
            onRetryClicked = allWorkoutViewModel::load,
            onSearchClick = { onIntent(SearchClicked) },
            friend = friend,
            blocked = blocked,
            onUpdateProfilePictureClicked = if (viewState.isCurrentUser) { intent ->
                onIntent(UpdateAvatarClicked(intent))
            } else null,
            tempProfilePictureFile = if (viewState.isCurrentUser) tempProfilePictureFile else null,
            uploadingAvatar = viewState.uploadingAvatar,
            onBadgesClick = { onIntent(BadgesClicked) },
            workoutCardActionsHandler = workoutCardActionsHandler,
            friendBadgesItems = friendBadgesListItems,
            onFriendBadgesListClick = { username -> onIntent(FriendBadgesListClicked(username)) },
            onBadgeImageClick = { badgeId -> onIntent(BadgeImageClicked(badgeId)) },
        )
    } else {
        val workoutPagingItems =
            workoutPageViewModel.workoutPagingDataFlow.collectAsLazyPagingItems()
        val mediaPagingItems = workoutPageViewModel.mediaPagingDataFlow.collectAsLazyPagingItems()
        val updatedCardList by workoutPageViewModel.updatedCardFlow.collectAsStateWithLifecycle()
        OtherUserProfileContent(
            user = viewState.user,
            workoutPagingItems = workoutPagingItems,
            mediaPagingItems = mediaPagingItems,
            workoutCardActionsHandler = workoutCardActionsHandler,
            modifier = modifier,
            userWorkoutSummaryState = userWorkoutSummaryState,
            friendBadgesItems = friendBadgesListItems,
            measurementUnit = viewModel.measurementUnit,
            premiumSubscriptionStatus = premiumSubscriptionStatus,
            followCountSummary = followCountSummary,
            deviceTypeList = deviceTypeList,
            moreMenuList = moreMenuList,
            friend = friend,
            blocked = blocked,
            openWorkout = { workoutHeader, source -> onIntent(WorkoutOpened(workoutHeader, source)) },
            onBackClick = { onIntent(BackClicked) },
            onFollowersClicked = { count -> onIntent(FollowersClicked(count)) },
            onFollowingClicked = { count -> onIntent(FollowingClicked(count)) },
            onFollowButtonClicked = { onIntent(FollowButtonClicked) },
            updatedCardList = updatedCardList,
            onSearchClick = { onIntent(SearchClicked) },
            onFriendBadgesListClick = { username -> onIntent(FriendBadgesListClicked(username)) },
            onBadgeImageClick = { badgeId -> onIntent(BadgeImageClicked(badgeId)) },
        )
    }
}
