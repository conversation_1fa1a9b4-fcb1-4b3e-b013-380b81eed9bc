package com.stt.android.di;

import com.squareup.moshi.Moshi;
import com.stt.android.controllers.ExtensionDataAccess;
import com.stt.android.controllers.ExtensionDataAccessOrmliteDb;
import com.stt.android.controllers.ExtensionDataAccessRoomDb;
import com.stt.android.data.source.local.DaoFactory;
import com.stt.android.data.source.local.diveextension.DiveExtensionDao;
import com.stt.android.data.source.local.diveextension.LocalDiveExtension;
import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao;
import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension;
import com.stt.android.data.source.local.intensityextension.IntensityExtensionDao;
import com.stt.android.data.source.local.intensityextension.LocalIntensityExtension;
import com.stt.android.data.source.local.jumpropeextension.JumpRopeExtensionDao;
import com.stt.android.data.source.local.jumpropeextension.LocalJumpRopeExtension;
import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension;
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao;
import com.stt.android.data.source.local.swimmingextension.LocalSwimmingExtension;
import com.stt.android.data.source.local.swimmingextension.SwimmingExtensionDao;
import com.stt.android.data.source.local.weatherextension.LocalWeatherExtension;
import com.stt.android.data.source.local.weatherextension.WeatherExtensionDao;
import com.stt.android.data.workout.diveextension.DiveExtensionDataFetcher;
import com.stt.android.data.workout.extensions.DiveExtensionLocalMapper;
import com.stt.android.data.workout.extensions.FitnessExtensionLocalMapper;
import com.stt.android.data.workout.extensions.IntensityExtensionLocalMapper;
import com.stt.android.data.workout.extensions.JumpRopeExtensionLocalMapper;
import com.stt.android.data.workout.extensions.SummaryExtensionLocalMapper;
import com.stt.android.data.workout.extensions.SwimmingExtensionLocalMapper;
import com.stt.android.data.workout.extensions.WeatherExtensionLocalMapper;
import com.stt.android.data.workout.fitnessextension.FitnessExtensionDataFetcher;
import com.stt.android.data.workout.intensityextension.IntensityExtensionDataFetcher;
import com.stt.android.data.workout.jumpropeextension.JumpRopeExtensionDataFetcher;
import com.stt.android.data.workout.summaryextension.SummaryExtensionDataFetcher;
import com.stt.android.data.workout.swimmingextension.SwimmingExtensionDataFetcher;
import com.stt.android.data.workout.weatherextension.WeatherExtensionDataFetcher;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.user.workoutextension.FitnessExtension;
import com.stt.android.domain.user.workoutextension.IntensityExtension;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import com.stt.android.domain.workouts.extensions.DiveExtension;
import com.stt.android.domain.workouts.extensions.JumpRopeExtension;
import com.stt.android.domain.workouts.extensions.SummaryExtension;
import com.stt.android.domain.workouts.extensions.SwimmingExtension;
import com.stt.android.domain.workouts.extensions.WeatherExtension;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.extensions.ExtensionsRestApi;
import dagger.Module;
import dagger.Provides;
import java.util.LinkedHashMap;
import java.util.Map;
import okhttp3.OkHttpClient;

@SuppressWarnings("WeakerAccess")
@Module
public abstract class ExtensionDataAccessModule {

    @Provides
    public static Map<Class<? extends WorkoutExtension>,
        ? extends ExtensionDataAccess<? extends WorkoutExtension>> provideExtensionDataAccessMap(
        ExtensionDataAccessOrmliteDb<SlopeSkiSummary> slopeSkiSummaryDataAccess,
        ExtensionDataAccessRoomDb<LocalIntensityExtension, IntensityExtension> intensityExtensionDataAccess,
        ExtensionDataAccessRoomDb<LocalFitnessExtension, FitnessExtension> fitnessExtensionExtensionDataAccess,
        ExtensionDataAccessRoomDb<LocalSummaryExtension, SummaryExtension> summaryExtensionDataAccess,
        ExtensionDataAccessRoomDb<LocalDiveExtension, DiveExtension> diveExtensionDataAccess,
        ExtensionDataAccessRoomDb<LocalSwimmingExtension, SwimmingExtension> swimmingExtensionDataAccess,
        ExtensionDataAccessRoomDb<LocalJumpRopeExtension, JumpRopeExtension> jumpRopeExtensionDataAccess
    ) {
        Map<Class<? extends WorkoutExtension>,
            ExtensionDataAccess<? extends WorkoutExtension>> map = new LinkedHashMap<>();
        map.put(SlopeSkiSummary.class, slopeSkiSummaryDataAccess);
        map.put(IntensityExtension.class, intensityExtensionDataAccess);
        map.put(FitnessExtension.class, fitnessExtensionExtensionDataAccess);
        map.put(SummaryExtension.class, summaryExtensionDataAccess);
        map.put(DiveExtension.class, diveExtensionDataAccess);
        map.put(SwimmingExtension.class, swimmingExtensionDataAccess);
        map.put(JumpRopeExtension.class, jumpRopeExtensionDataAccess);
        return map;
    }

    @Provides
    public static ExtensionDataAccessOrmliteDb<SlopeSkiSummary> provideSlopeSkiSummaryExtensionDataAccess(
            DatabaseHelper helper) {
        return new ExtensionDataAccessOrmliteDb<>(SlopeSkiSummary.class,
                helper, SlopeSkiSummary.DbFields.WORKOUT_ID);
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalIntensityExtension, IntensityExtension> provideRoomIntensityExtensionDataAccess(
        IntensityExtensionDao intensityExtensionDao,
        IntensityExtensionDataFetcher intensityExtensionDataFetcher,
        IntensityExtensionLocalMapper intensityExtensionLocalMapper
    ) {
        return new ExtensionDataAccessRoomDb<>(
            intensityExtensionDao,
            intensityExtensionDataFetcher,
            intensityExtensionLocalMapper
        );
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalFitnessExtension, FitnessExtension> provideRoomFitnessExtensionDataAccess(
        FitnessExtensionDao fitnessExtensionDao,
        FitnessExtensionDataFetcher fitnessExtensionDataFetcher,
        FitnessExtensionLocalMapper fitnessExtensionLocalMapper
    ) {
        return new ExtensionDataAccessRoomDb<>(
            fitnessExtensionDao,
            fitnessExtensionDataFetcher,
            fitnessExtensionLocalMapper
        );
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalSummaryExtension, SummaryExtension> provideSummaryExtensionDataAccess(
        SummaryExtensionDao summaryExtensionDao,
        SummaryExtensionDataFetcher summaryExtensionDataFetcher,
        SummaryExtensionLocalMapper summaryExtensionLocalMapper
    ) {
        return new ExtensionDataAccessRoomDb<>(
            summaryExtensionDao,
            summaryExtensionDataFetcher,
            summaryExtensionLocalMapper);
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalDiveExtension, DiveExtension> provideDiveExtensionDataAccess(
            DiveExtensionDao diveExtensionDao,
            DiveExtensionDataFetcher diveExtensionDataFetcher,
            DiveExtensionLocalMapper diveExtensionLocalMapper) {
        return new ExtensionDataAccessRoomDb<>(
                diveExtensionDao, diveExtensionDataFetcher, diveExtensionLocalMapper);
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalSwimmingExtension, SwimmingExtension> provideSwimmingExtensionDataAccess(
            SwimmingExtensionDao swimmingExtensionDao,
            SwimmingExtensionDataFetcher swimmingExtensionDataFetcher,
            SwimmingExtensionLocalMapper swimmingExtensionLocalMapper) {
        return new ExtensionDataAccessRoomDb<>(
            swimmingExtensionDao, swimmingExtensionDataFetcher, swimmingExtensionLocalMapper);
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalJumpRopeExtension, JumpRopeExtension> provideJumpRopeExtensionDataAccess(
        JumpRopeExtensionDao jumpRopeExtensionDao,
        JumpRopeExtensionDataFetcher jumpRopeExtensionDataFetcher,
        JumpRopeExtensionLocalMapper jumpRopeExtensionLocalMapper) {
        return new ExtensionDataAccessRoomDb<>(
            jumpRopeExtensionDao, jumpRopeExtensionDataFetcher, jumpRopeExtensionLocalMapper);
    }

    @Provides
    public static ExtensionDataAccessRoomDb<LocalWeatherExtension, WeatherExtension> provideWeatherExtensionDataAccess(
        WeatherExtensionDao weatherExtensionDao,
        WeatherExtensionDataFetcher weatherExtensionDataFetcher,
        WeatherExtensionLocalMapper weatherExtensionLocalMapper) {
        return new ExtensionDataAccessRoomDb<>(
            weatherExtensionDao, weatherExtensionDataFetcher, weatherExtensionLocalMapper);
    }

    @Provides
    public static ExtensionsRestApi provideExtensionsRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        AuthProvider authProvider,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            ExtensionsRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }

    @Provides
    public static DiveExtensionDao provideDiveExtensionDao(DaoFactory daoFactory) {
        return daoFactory.getDiveExtensionDao();
    }

    @Provides
    public static SwimmingExtensionDao provideSwimmingJsonDao(DaoFactory daoFactory) {
        return daoFactory.getSwimmingReferenceDao();
    }

    @Provides
    public static WeatherExtensionDao provideWeatherExtensionDao(DaoFactory daoFactory) {
        return daoFactory.getWeatherExtensionDao();
    }

    @Provides
    public static SummaryExtensionDao provideSummaryExtensionDao(DaoFactory daoFactory) {
        return daoFactory.getSummaryExtensionDao();
    }

    @Provides
    public static JumpRopeExtensionDao provideJumpRopeExtensionDao(DaoFactory daoFactory) {
        return daoFactory.getJumpRopeExtensionDao();
    }

}
