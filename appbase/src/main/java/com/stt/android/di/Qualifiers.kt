package com.stt.android.di

import javax.inject.Qualifier

@Qualifier
@MustBeDocumented
@Retention
annotation class ApplicationId

@Qualifier
@MustBeDocumented
@Retention
annotation class VersionCode

@Qualifier
@MustBeDocumented
@Retention
annotation class VersionName

@Qualifier
@MustBeDocumented
@Retention
annotation class AppVersionNumberForSupport

@Qualifier
@MustBeDocumented
@Retention
annotation class MapPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class ExploreMapPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class DiaryPagePreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class AnalyticsSharedPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class SuuntoSharedPrefs

@Qualifier
@MustBeDocumented
@Retention
annotation class TooltipPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class DiaryPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class DashboardPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class TrackingPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class DayViewPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class EmarsysCustomAttributePreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class SystemWidgetPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class FeatureTogglePreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class Forced2dPlaybackMode

@Qualifier
@MustBeDocumented
@Retention
annotation class AllAvailableWidgetTypes

@Qualifier
@MustBeDocumented
@Retention
annotation class PremiumRequiredWidgetTypes

@Qualifier
@MustBeDocumented
@Retention
annotation class HeatmapsRequirePremium

@Qualifier
@MustBeDocumented
@Retention
annotation class RoadSurfaceTypesRequirePremium

@Qualifier
@MustBeDocumented
@Retention
annotation class PremiumRequiredMyTracksGranularityTypes

@Qualifier
@MustBeDocumented
@Retention
annotation class TrainingHubPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class FragmentContext

@Qualifier
@MustBeDocumented
@Retention
annotation class FragmentLifecycle

@Qualifier
@MustBeDocumented
@Retention
annotation class WeChatPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class CacheFileSharedPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class TrainingZoneSummaryPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class FirstPairedDevicePreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class TrainingPlannerPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class WatchMusicManagerController

@Qualifier
@MustBeDocumented
@Retention
annotation class WatchSettingsPreferences

@Qualifier
@MustBeDocumented
@Retention
annotation class UserProfilePreferences
