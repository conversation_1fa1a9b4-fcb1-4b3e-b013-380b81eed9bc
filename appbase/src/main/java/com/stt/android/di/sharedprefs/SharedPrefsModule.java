package com.stt.android.di.sharedprefs;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import com.stt.android.di.AnalyticsSharedPreferences;
import com.stt.android.di.CacheFileSharedPreferences;
import com.stt.android.di.DashboardPreferences;
import com.stt.android.di.DayViewPreferences;
import com.stt.android.di.DiaryPagePreferences;
import com.stt.android.di.EmarsysCustomAttributePreferences;
import com.stt.android.di.ExploreMapPreferences;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.di.FirstPairedDevicePreferences;
import com.stt.android.di.MapPreferences;
import com.stt.android.di.SuuntoSharedPrefs;
import com.stt.android.di.SystemWidgetPreferences;
import com.stt.android.di.TooltipPreferences;
import com.stt.android.di.TrackingPreferences;
import com.stt.android.di.TrainingHubPreferences;
import com.stt.android.di.TrainingPlannerPreferences;
import com.stt.android.di.TrainingZoneSummaryPreferences;
import com.stt.android.di.UserProfilePreferences;
import com.stt.android.di.WatchSettingsPreferences;
import com.stt.android.di.WeChatPreferences;
import com.stt.android.home.diary.DiaryPagePreferencesKeys;
import com.stt.android.utils.STTConstants;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class SharedPrefsModule {

    @Provides
    public static SharedPreferences provideSharedPreferences(Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context);
    }

    @Provides
    @FeatureTogglePreferences
    public static SharedPreferences provideFeatureToggleSharedPreferences(Context context) {
        return context.getSharedPreferences(
            STTConstants.FeatureTogglePreferences.FEATURE_TOGGLE_PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @MapPreferences
    public static SharedPreferences provideMapPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @ExploreMapPreferences
    public static SharedPreferences provideExploreMapPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.MapPreferences.EXPLORE_MAP_PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @DiaryPagePreferences
    public static SharedPreferences provideDiaryPagePreferences(Context context) {
        return context.getSharedPreferences(DiaryPagePreferencesKeys.DIARY_PAGE_PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @AnalyticsSharedPreferences
    public static SharedPreferences provideAnalyticsPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.AnalyticsPreferences.ANALYTICS_PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @TooltipPreferences
    public static SharedPreferences provideTooltipPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.TooltipPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @TrackingPreferences
    public static SharedPreferences provideTrackingPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.TrackingPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @DayViewPreferences
    public static SharedPreferences provideDayViewPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.DayViewPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @SuuntoSharedPrefs
    public static SharedPreferences provideSuuntoSharedPreferences(Application application) {
        return application.getSharedPreferences(STTConstants.SuuntoPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @EmarsysCustomAttributePreferences
    public static SharedPreferences provideEmarsysCustomAttributePreferences(
        Application application) {
        return application.getSharedPreferences(
            STTConstants.EmarsysCustomAttributePreferences.PREFS_NAME,
            Context.MODE_PRIVATE
        );
    }

    @Provides
    @DashboardPreferences
    public static SharedPreferences provideDashboardPrefs(Context context) {
        return context.getSharedPreferences(
            STTConstants.DashboardPreferences.PREFS_NAME,
            Context.MODE_PRIVATE
        );
    }

    @Provides
    @SystemWidgetPreferences
    public static SharedPreferences provideSystemWidgetPreferences(Context context) {
        return context.getSharedPreferences(
            STTConstants.SystemWidgetPreferences.PREFS_NAME,
            Context.MODE_PRIVATE
        );
    }

    @Provides
    @TrainingHubPreferences
    public static SharedPreferences provideTrainingHubPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.TrainingHubPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @TrainingZoneSummaryPreferences
    public static SharedPreferences provideTrainingZoneSummaryPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.TrainingZoneSummaryPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @WeChatPreferences
    public static SharedPreferences provideWeChatPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.WeChatPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @CacheFileSharedPreferences
    public static SharedPreferences provideCacheFileSharedPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.CacheFileSharedPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @FirstPairedDevicePreferences
    public static SharedPreferences provideDeviceFirstPairedSharedPreferences(
        Application application) {
        return application.getSharedPreferences(
            STTConstants.FirstPairedDevicePreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @TrainingPlannerPreferences
    public static SharedPreferences provideTrainingPlannerSharedPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.TrainingPlannerPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @WatchSettingsPreferences
    public static SharedPreferences provideUserSettingsPreferences(Context context) {
        return context.getSharedPreferences(STTConstants.WatchSettingsPreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }

    @Provides
    @UserProfilePreferences
    public static SharedPreferences provideUserProfilePreferences(Context context) {
        return context.getSharedPreferences(STTConstants.UserProfilePreferences.PREFS_NAME,
            Context.MODE_PRIVATE);
    }
}
