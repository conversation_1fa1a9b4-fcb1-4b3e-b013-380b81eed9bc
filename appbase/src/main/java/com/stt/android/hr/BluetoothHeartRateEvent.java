package com.stt.android.hr;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class BluetoothHeartRateEvent extends HeartRateEvent implements Parcelable {
    @IntDef({ACTION_CONNECTED, ACTION_DISCONNECTED, ACTION_ERROR, ACTION_UPDATE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Action {
    }

    public static final int ACTION_CONNECTED = 0;
    public static final int ACTION_DISCONNECTED = 1;
    public static final int ACTION_ERROR = 2;
    public static final int ACTION_UPDATE = 3;

    @Action
    private final int action;
    private final BatteryStatus batteryStatus;

    public static BluetoothHeartRateEvent newConnectedEvent() {
        return new BluetoothHeartRateEvent(ACTION_CONNECTED, null, -1, -1, null);
    }

    public static BluetoothHeartRateEvent newDisconnectedEvent() {
        return new BluetoothHeartRateEvent(ACTION_DISCONNECTED, null, -1, -1, null);
    }

    public static BluetoothHeartRateEvent newErrorEvent() {
        return new BluetoothHeartRateEvent(ACTION_ERROR, null, -1, -1, null);
    }

    public static BluetoothHeartRateEvent newUpdateEvent(BatteryStatus batteryStatus,
                                                         long timestamp, int heartBeatsPerMinute, int[] binaryData) {
        return new BluetoothHeartRateEvent(ACTION_UPDATE, batteryStatus, timestamp, heartBeatsPerMinute, binaryData);
    }

    private BluetoothHeartRateEvent(@Action int action, BatteryStatus batteryStatus,
                                    long timestamp, int heartBeatsPerMinute, int[] binaryData) {
        super(timestamp, heartBeatsPerMinute, binaryData);
        this.action = action;
        this.batteryStatus = batteryStatus;
    }

    @Action
    public int getAction() {
        return action;
    }

    public BatteryStatus getBatteryStatus() {
        return batteryStatus;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(action);
        dest.writeParcelable(batteryStatus, 0);
        dest.writeLong(getTimestamp());
        dest.writeInt(getHeartRate());
        int[] rawBinaryData = getRawData();
        dest.writeInt(rawBinaryData.length);
        dest.writeIntArray(rawBinaryData);
    }

    public static final Parcelable.Creator<HeartRateEvent> CREATOR = new Creator<HeartRateEvent>() {
        @Override
        public HeartRateEvent[] newArray(int size) {
            return new HeartRateEvent[size];
        }

        @Override
        public HeartRateEvent createFromParcel(Parcel source) {
            @Action
            int action = source.readInt();
            BatteryStatus batteryStatus = source.readParcelable(BatteryStatus.class.getClassLoader());
            long timestamp = source.readLong();
            int heartBeatsPerMinute = source.readInt();
            int[] frame = new int[source.readInt()];
            source.readIntArray(frame);
            return new BluetoothHeartRateEvent(action, batteryStatus, timestamp, heartBeatsPerMinute, frame);
        }
    };
}
