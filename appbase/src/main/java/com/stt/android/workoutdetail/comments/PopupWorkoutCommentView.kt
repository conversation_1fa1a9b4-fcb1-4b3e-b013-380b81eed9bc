package com.stt.android.workoutdetail.comments

import android.content.Context
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.target
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.core.R as CoreR

@AndroidEntryPoint
class PopupWorkoutCommentView : LinearLayout, View.OnClickListener {
    @Inject
    lateinit var getUserByUsernameUseCase: GetUserByUsernameUseCase

    @Inject
    lateinit var coroutinesDispatchers: CoroutinesDispatchers

    private val coroutineScope = CoroutineScope(SupervisorJob() + coroutinesDispatchers.main)

    private lateinit var userImage: ImageView
    private lateinit var userNameAndComment: TextView
    private lateinit var timestamp: TextView

    private var commenterUserName: String? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    init {
        orientation = HORIZONTAL
    }

    override fun onFinishInflate() {
        super.onFinishInflate()

        userImage = findViewById(R.id.userImage)
        userImage.scaleType = ImageView.ScaleType.CENTER_CROP
        userImage.setOnClickListener(this)
        userNameAndComment = findViewById(R.id.userNameAndComment)
        timestamp = findViewById(R.id.timestamp)
    }

    override fun onDetachedFromWindow() {
        coroutineScope.cancel()
        super.onDetachedFromWindow()
    }

    override fun onClick(v: View) {
        openCommenterProfile()
    }

    private fun openCommenterProfile() {
        commenterUserName?.let {
            context.startActivity(BaseUserProfileActivity.newStartIntent(context, it, false))
        }
    }

    fun setWorkoutComment(workoutComment: WorkoutComment) {
        commenterUserName = workoutComment.username
        timestamp.text =
            TextFormatter.formatRelativeDateSpan(context.resources, workoutComment.timestamp)

        setValues(
            userName = workoutComment.realName,
            comment = workoutComment.message,
            profileImage = workoutComment.profilePictureUrl,
        )

        // if the user has changed real name or profile picture, that won't be reflected in
        // workout comment, so we try to see from local DB if there's any change
        coroutineScope.coroutineContext.cancelChildren()
        coroutineScope.launch {
            runSuspendCatching {
                val user = getUserByUsernameUseCase.getUserByUsername(workoutComment.username, true)
                if (workoutComment.realName != user.realNameOrUsername ||
                    workoutComment.profilePictureUrl != user.profileImageUrl
                ) {
                    setValues(
                        userName = user.realNameOrUsername,
                        comment = workoutComment.message,
                        profileImage = user.profileImageUrl,
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to get user")
            }
        }
    }

    private fun setValues(userName: String, comment: String, profileImage: String?) {
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(textView: View) {
                openCommenterProfile()
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
            }
        }
        CommentUtils.styleCommentsTextViewFont(userNameAndComment, userName, comment, clickableSpan)

        val request = ImageRequest.Builder(context)
            .data(profileImage)
            .placeholderWithFallback(context, CoreR.drawable.ic_default_profile_image_light)
            .transformations(CircleCropTransformation())
            .target(userImage)
            .build()
        context.imageLoader.enqueue(request)
    }
}
