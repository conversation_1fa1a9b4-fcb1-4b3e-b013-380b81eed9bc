package com.stt.android.utils;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;

public class PreferencesUtils {

    /**
     * Helper method to retrieve a boolean value from
     * {@link STTConstants.SuuntoPreferences#PREFS_NAME}.
     *
     * @param context a {@link Context} object.
     * @param defaultValue A default to return if the value could not be read.
     * @return The value from shared preferences, or the provided default.
     */
    public static boolean getSuuntoSharedPreferences(@NonNull Context context, @NonNull String key,
        boolean defaultValue) {
        return getSharedPreferences(
            context,
            STTConstants.SuuntoPreferences.PREFS_NAME,
            key,
            defaultValue);
    }

    /**
     * Helper method to write a boolean value to {@link STTConstants.SuuntoPreferences#PREFS_NAME}.
     *
     * @param context a {@link Context} object.
     * @param key key
     * @param value boolean value to write
     */
    public static void putSuuntoSharedPreferences(@NonNull Context context, @NonNull String key,
        boolean value) {
        putSharedPreferences(context, STTConstants.SuuntoPreferences.PREFS_NAME, key, value);
    }

    /**
     * Helper method to retrieve a boolean value from {@link SharedPreferences}.
     *
     * @param context a {@link Context} object.
     * @param prefsName Desired preferences file name.
     * @param defaultValue A default to return if the value could not be read.
     * @return The value from shared preferences, or the provided default.
     */
    public static boolean getSharedPreferences(@NonNull Context context, @NonNull String prefsName,
        String key, boolean defaultValue) {
        return getSharedPreferencesByName(context, prefsName)
            .getBoolean(key, defaultValue);
    }

    /**
     * Helper method to write a boolean value to {@link SharedPreferences}.
     *
     * @param context a {@link Context} object.
     * @param prefsName Desired preferences file name.
     * @param key key
     * @param value boolean value to write
     */
    public static void putSharedPreferences(@NonNull Context context, @NonNull String prefsName,
        @NonNull String key, boolean value) {
        getSharedPreferencesByName(context, prefsName)
            .edit()
            .putBoolean(key, value)
            .apply();
    }

    @NonNull
    private static SharedPreferences getSharedPreferencesByName(@NonNull Context context,
        @NonNull String prefsName) {
        return context.getSharedPreferences(prefsName, Context.MODE_PRIVATE);
    }
}
