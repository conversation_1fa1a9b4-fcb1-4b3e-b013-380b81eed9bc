package com.stt.android.utils

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.featuretoggle.api.FeatureFlag
import com.stt.android.featuretoggle.api.FeatureToggle
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ZoneSenseUtils @Inject constructor(
    private val featureToggle: FeatureToggle,
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
) {
    fun isZoneSenseEnabled(workoutId: Int? = null): Boolean {
        // Make it easier to debug
        if (isZoneSenseDebugInfoEnabled()) {
            return true
        }

        // We only enable it for workouts belonging to current user.
        workoutId?.let {
            if (workoutHeaderController.findByIdsForCurrentUser(listOf(it)).isEmpty()) {
                return false
            }
        }

        return true
    }

    fun isZoneSenseDebugInfoEnabled(): Boolean = featureToggle[FeatureFlag.ShowZoneSenseDebugInfo] ||
        currentUserController.isFieldTester
}
