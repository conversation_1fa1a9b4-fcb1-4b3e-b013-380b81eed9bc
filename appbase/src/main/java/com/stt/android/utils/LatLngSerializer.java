package com.stt.android.utils;

import com.google.android.gms.maps.model.LatLng;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;

public class LatLngSerializer implements JsonSerializer<LatLng> {
    @Override
    public JsonElement serialize(LatLng src, Type typeOfSrc, JsonSerializationContext context) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.add("latitude", new JsonPrimitive(src.latitude));
        jsonObject.add("longitude", new JsonPrimitive(src.longitude));
        return jsonObject;
    }
}
