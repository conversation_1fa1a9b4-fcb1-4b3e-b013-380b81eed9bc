package com.stt.android.ui.components;

import android.content.Context;
import android.content.DialogInterface;
import androidx.core.util.Pair;
import android.util.AttributeSet;
import android.view.View;
import com.stt.android.R;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.ui.utils.DialogHelper;
import dagger.hilt.android.AndroidEntryPoint;

@AndroidEntryPoint
public class WorkoutTypeEditor extends Editor<ActivityType> {

    public WorkoutTypeEditor(final Context context, AttributeSet attrs) {
        super(context, attrs);
        setValue(ActivityType.DEFAULT);

        final ActivityType[] allActivities =
            ActivityType.getAllActivitiesSortedByLocalNames(getResources());

        setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityType activityType = getValue();
                // if the activity is unknown, we don't select anything
                int selected = -1;
                if (!activityType.isUnknown()) {
                    for (int i=0; i < allActivities.length; i++) {
                        if (activityType.getId() == allActivities[i].getId()) {
                            selected = i;
                            break;
                        }
                    }
                }
                Pair<String[], int[]> pair =
                    ActivityType.getListOfActivityNamesAndIcons(getResources(), allActivities);
                DialogHelper.showDialog(context, R.string.select_activity, pair.first, pair.second,
                    selected, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            ActivityType selected = allActivities[which];
                            setValue(selected);
                            notifyListener(selected);
                            dialog.dismiss();
                        }
                    });
            }
        });
    }

    @Override
    protected String getSubtitleForValue(ActivityType value) {
        return value.getLocalizedName(getResources());
    }
}
