package com.stt.android.ui.workout.widgets;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.workouts.RecordWorkoutService;

import javax.inject.Inject;

public class AvgSpeedPaceWidget extends SpeedRelatedWidget {
    public static class SmallAvgSpeedPaceWidget extends AvgSpeedPaceWidget {
        @Inject
        public SmallAvgSpeedPaceWidget(LocalBroadcastManager localBM, UserSettingsController userSettingsController) {
            super(localBM, userSettingsController);
        }

        @Override
        protected int getLayoutId() {
            return R.layout.small_tracking_widget_with_unit;
        }
    }

    @Inject
    public AvgSpeedPaceWidget(LocalBroadcastManager localBM, UserSettingsController userSettingsController) {
        super(localBM, userSettingsController);
    }

    @Override
    protected void onViewInflated() {
        label.setText(R.string.avg_speed_avg_pace_capital);
        super.onViewInflated();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.tracking_widget_with_unit;
    }

    @Override
    protected double getValue() {
        RecordWorkoutService rws = serviceConnection.getRecordWorkoutService();
        return rws != null ? rws.getAverageSpeed() : 0.0;
    }

    @Override
    protected int getWidgetLabelId() {
        return R.id.label;
    }
}
