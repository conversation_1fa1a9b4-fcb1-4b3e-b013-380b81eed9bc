package com.stt.android.ui.utils

import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Throttles live data updates using the given interval.
 *
 * First value is posted immediately.
 * Not suitable where an exact interval is needed. The actual delay interval will not be exactly
 * the given intervalMs, but something close to it.
 */
class ThrottledLiveData<T>(
    private var intervalMs: Long = 0L,
    private val coroutineScope: CoroutineScope,
) : MutableLiveData<T>() {
    private var latestValue: T? = null
    private var throttleJob: Job? = null

    override fun postValue(value: T) {
        if (throttleJob?.isCompleted != false) {
            if (latestValue == null || intervalMs == 0L) {
                super.postValue(value)
            } else {
                throttleJob = coroutineScope.launch {
                    delay(intervalMs)
                    latestValue?.run { super.postValue(this) }
                }
            }
        }
        latestValue = value
    }

    fun setInterval(intervalMs: Long) {
        this.intervalMs = intervalMs
    }
}
