package com.stt.android.ui.components.editors;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import com.airbnb.epoxy.CallbackProp;
import com.airbnb.epoxy.ModelProp;
import com.airbnb.epoxy.ModelView;
import com.airbnb.epoxy.TextProp;
import com.stt.android.R;
import com.stt.android.ThemeColors;

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
public class CheckboxEditor extends FrameLayout {
    public interface OnCheckedChangeListener {
        void onCheckedChanged(boolean isChecked);
    }

    TextView title;
    CheckBox checkBox;

    @Nullable
    public OnCheckedChangeListener listener;

    public CheckboxEditor(Context context) {
        super(context);
        init(context, null);
    }

    public CheckboxEditor(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public CheckboxEditor(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    protected @LayoutRes int getLayoutId() {
        return R.layout.checkbox_editor;
    }

    private void init(Context context, @Nullable AttributeSet attrs) {
        inflate(context, getLayoutId(), this);
        title = findViewById(R.id.title);
        checkBox = findViewById(R.id.checkbox);

        Drawable itemBackground =
            ThemeColors.attrDrawable(getContext(), android.R.attr.selectableItemBackground);
        if (itemBackground != null) {
            setBackground(itemBackground);
        }

        setOnClickListener(v -> setChecked(!isChecked()));

        checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (listener != null) {
                listener.onCheckedChanged(isChecked);
            }
        });

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CheckboxEditor);
            int titleResourceId = a.getResourceId(R.styleable.CheckboxEditor_titleText, -1);
            if (titleResourceId != -1) {
                title.setText(titleResourceId);
            }

            int titleColor = a.getResourceId(R.styleable.CheckboxEditor_titleColor, -1);
            if (titleColor != -1) {
                title.setTextColor(a.getColor(R.styleable.CheckboxEditor_titleColor, title
                    .getTextColors().getDefaultColor()));
            }
            a.recycle();
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        title.setEnabled(enabled);
        checkBox.setEnabled(enabled);
    }

    @TextProp
    public void setTitle(CharSequence title) {
        this.title.setText(title);
    }

    @CallbackProp
    public void setOnCheckedChangeListener(@Nullable OnCheckedChangeListener listener) {
        this.listener = listener;
    }

    public void setChecked(boolean checked) {
        checkBox.setChecked(checked);
    }

    @ModelProp
    public void setCheckedInitialValue(boolean checked) {
        // Do not notify listener when initializing the checkbox
        OnCheckedChangeListener currentListener = listener;
        listener = null;
        checkBox.setChecked(checked);
        listener = currentListener;
    }

    public boolean isChecked() {
        return checkBox.isChecked();
    }
}
