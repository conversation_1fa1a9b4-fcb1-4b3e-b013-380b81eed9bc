package com.stt.android.ui.utils;

import android.content.Context;
import android.content.res.Resources;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.format.DateUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.workouts.extensions.AltitudeSetting;
import com.stt.android.utils.LocaleUtils;
import com.stt.android.workouts.details.values.WorkoutValue;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.FieldPosition;
import java.util.Formatter;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public final class TextFormatter {

    private TextFormatter() {
    }

    static {
        depthFormatter = new HashMap<>();
        shortDistanceFormatter = new HashMap<>();
    }

    private static DecimalFormatSymbols decimalFormatSymbols = null;

    private static DecimalFormatSymbols getDecimalFormatSymbols() {
        if (decimalFormatSymbols == null) {
            // We want to always use English formatting for numeric data regardless of the
            // selected locale.
            decimalFormatSymbols = new DecimalFormatSymbols(Locale.ENGLISH);
        }
        return decimalFormatSymbols;
    }

    private static final StringBuffer STRING_BUFFER = new StringBuffer();
    private static final FieldPosition ZERO_FIELD_POSITION = new FieldPosition(0);

    private static DecimalFormat distanceFormatter = null;

    private static final String VALUE_CHARS = "0123456789:'" +
        getDecimalFormatSymbols().getDecimalSeparator() +
        getDecimalFormatSymbols().getMinusSign() +
        getDecimalFormatSymbols().getInfinity();

    private static DecimalFormat getDistanceDecimalFormat() {
        if (distanceFormatter == null) {
            distanceFormatter = new DecimalFormat("0.00", getDecimalFormatSymbols());
        }
        return distanceFormatter;
    }

    public static String formatDistance(double distance) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getDistanceDecimalFormat().format(distance, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static final Map<MeasurementUnit, DecimalFormat> shortDistanceFormatter;

    private static DecimalFormat getShortDistanceDecimalFormat(MeasurementUnit unit) {
        if (shortDistanceFormatter.get(unit) == null) {
            shortDistanceFormatter.put(
                unit,
                (unit == MeasurementUnit.IMPERIAL)
                    ? new DecimalFormat("0", getDecimalFormatSymbols())
                    : new DecimalFormat("0.0", getDecimalFormatSymbols()));
        }
        return shortDistanceFormatter.get(unit);
    }

    public static String formatShortDistance(double distance, MeasurementUnit unit) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getShortDistanceDecimalFormat(unit).format(distance, STRING_BUFFER,
                ZERO_FIELD_POSITION).toString();
        }
    }

    private static DecimalFormat distanceFormatterWithoutZero = null;

    private static DecimalFormat getDistanceWithoutZeroDecimalFormat() {
        if (distanceFormatterWithoutZero == null) {
            distanceFormatterWithoutZero = new DecimalFormat("0.#", getDecimalFormatSymbols());
        }
        return distanceFormatterWithoutZero;
    }

    public static String formatDistanceWithoutZeros(double distance) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getDistanceWithoutZeroDecimalFormat().format(distance, STRING_BUFFER,
                ZERO_FIELD_POSITION).toString();
        }
    }

    private static DecimalFormat distanceFormatterRounded = null;

    private static DecimalFormat getDistanceRoundedFormat() {
        if (distanceFormatterRounded == null) {
            distanceFormatterRounded = new DecimalFormat("0", getDecimalFormatSymbols());
        }
        return distanceFormatterRounded;
    }

    public static String formatDistanceRounded(double distance) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getDistanceRoundedFormat().format(distance, STRING_BUFFER,
                ZERO_FIELD_POSITION).toString();
        }
    }

    private static DecimalFormat speedFormatter = null;

    private static DecimalFormat getSpeedDecimalFormat() {
        if (speedFormatter == null) {
            speedFormatter = new DecimalFormat("0.0", getDecimalFormatSymbols());
        }
        return speedFormatter;
    }

    public static String formatSpeed(double speed) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getSpeedDecimalFormat().format(speed, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static DecimalFormat angleFormatter = null;

    private static DecimalFormat getAngleDecimalFormat() {
        if (angleFormatter == null) {
            angleFormatter = new DecimalFormat("0.0", getDecimalFormatSymbols());
        }
        return angleFormatter;
    }

    public static String formatAngle(double angle) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getAngleDecimalFormat().format(angle, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static DecimalFormat altitudeFormatter = null;

    private static DecimalFormat getAltitudeDecimalFormat() {
        if (altitudeFormatter == null) {
            altitudeFormatter = new DecimalFormat("0", getDecimalFormatSymbols());
        }
        return altitudeFormatter;
    }

    public static String formatAltitude(double altitude) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getAltitudeDecimalFormat().format(altitude, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static final Map<MeasurementUnit, DecimalFormat> depthFormatter;

    private static DecimalFormat getDepthDecimalFormat(
        MeasurementUnit unit) {
        if (depthFormatter.get(unit) == null) {
            depthFormatter.put(
                unit,
                (unit == MeasurementUnit.IMPERIAL)
                    ? new DecimalFormat("0", getDecimalFormatSymbols())
                    : new DecimalFormat("0.0", getDecimalFormatSymbols()));
        }
        return depthFormatter.get(unit);
    }

    public static String formatDepth(double depth, MeasurementUnit unit) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getDepthDecimalFormat(unit).format(depth, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static DecimalFormat gasConsumptionFormatter = null;

    private static DecimalFormat getGasConsumptionDecimalFormat() {
        if (gasConsumptionFormatter == null) {
            gasConsumptionFormatter = new DecimalFormat("0.#", getDecimalFormatSymbols());
        }
        return gasConsumptionFormatter;
    }

    public static String formatGasConsumption(double gasConsumption) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getGasConsumptionDecimalFormat().format(gasConsumption, STRING_BUFFER,
                ZERO_FIELD_POSITION)
                .toString();
        }
    }

    private static DecimalFormat personalSettingFormatter = null;

    private static DecimalFormat getPersonalSettingDecimalFormat() {
        if (personalSettingFormatter == null) {
            personalSettingFormatter = new DecimalFormat("0", decimalFormatSymbols);
        }
        return personalSettingFormatter;
    }

    public static String formatPersonalSetting(int personalSetting) {
        synchronized (STRING_BUFFER) {
            final DecimalFormat format = getPersonalSettingDecimalFormat();
            final String prefix = personalSetting > 0 ? "+" : "";
            format.setPositivePrefix(prefix);
            STRING_BUFFER.setLength(0);
            return format.format(personalSetting, STRING_BUFFER,
                ZERO_FIELD_POSITION)
                .toString();
        }
    }

    public static String formatAltitudeSetting(
        AltitudeSetting setting,
        MeasurementUnit unit,
        Resources resources) {

        String[] stringArray = resources.getStringArray(unit == MeasurementUnit.IMPERIAL
            ? R.array.altitude_setting_imperial
            : R.array.altitude_setting_metric);

        // Altitude setting is either 0, 1500 or 3000. This should always be of size 3.
        if (stringArray.length != 3) return "";

        String formatted;
        switch (setting) {
            case LOW:
                formatted = stringArray[0];
                break;
            case MIDDLE:
                formatted = stringArray[1];
                break;
            case HIGH:
                formatted = stringArray[2];
                break;
            default:
                formatted = "";
                break;
        }

        return formatted;
    }

    private static DecimalFormat pressureFormatter = null;

    private static DecimalFormat getPressureDecimalFormat() {
        if ( pressureFormatter == null) {
            pressureFormatter = new DecimalFormat("0", decimalFormatSymbols);
        }
        return pressureFormatter;
    }

    private static DecimalFormat percentageFormatter = null;

    private static DecimalFormat getPercentageDecimalFormat() {
        if ( percentageFormatter == null) {
            percentageFormatter = new DecimalFormat("0", decimalFormatSymbols);
        }
        return percentageFormatter;
    }

    public static String formatPercentage(double percentage) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getPercentageDecimalFormat().format(percentage, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString();
        }
    }

    public static String formatDateTime(Context context, long timestamp) {
        return formatDateTime(context, timestamp, DateUtils.FORMAT_SHOW_TIME
            | DateUtils.FORMAT_SHOW_DATE
            | DateUtils.FORMAT_NUMERIC_DATE);
    }

    public static String formatTime(Context context, long timestamp) {
        return formatDateTime(context, timestamp, DateUtils.FORMAT_SHOW_TIME);
    }

    private static final StringBuilder STRING_BUILDER = new StringBuilder(50);
    private static final Formatter FORMATTER = new Formatter(STRING_BUILDER);

    private static String formatDateTime(Context context, long timestamp, int flag) {
        synchronized (STRING_BUILDER) {
            STRING_BUILDER.setLength(0);
            return DateUtils.formatDateRange(context, FORMATTER, timestamp, timestamp, flag, null)
                .toString();
        }
    }

    public static String formatDate(Context context, long timestamp) {
        return formatDateTime(context, timestamp,
            DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_NUMERIC_DATE);
    }

    public static String formatDate(Context context, long timestamp, boolean showYear) {
        return formatDateTime(context, timestamp,
            showYear
                ? DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_NUMERIC_DATE | DateUtils.FORMAT_SHOW_YEAR
                : DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_NUMERIC_DATE | DateUtils.FORMAT_NO_YEAR);
    }

    /**
     * Show year & month, eg.
     * en: March 2025
     * zh: 2025年3月
     * @param context
     * @param timestamp
     * @return
     */
    public static String formatYearMonth(Context context, long timestamp) {
        return formatDateTime(context, timestamp, DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_SHOW_YEAR | DateUtils.FORMAT_NO_MONTH_DAY);
    }

    private static DecimalFormat jumpHeightFormatter = null;

    private static DecimalFormat getJumpHeightFormatter() {
        if (jumpHeightFormatter == null) {
            jumpHeightFormatter = new DecimalFormat("0.0", decimalFormatSymbols);
        }
        return jumpHeightFormatter;
    }

    private static DecimalFormat maxOneDecimalFormatter = null;

    private static DecimalFormat getMaxOneDecimalFormatter() {
        if (maxOneDecimalFormatter == null) {
            maxOneDecimalFormatter = new DecimalFormat("#.#", decimalFormatSymbols);
        }
        return maxOneDecimalFormatter;
    }


    /**
     * @param height value in cm
     * @param unit   value is set from user
     * @return formatted string use different unit
     */
    public static String formatJumpHeight(float height, MeasurementUnit unit) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getJumpHeightFormatter().format(unit.toJumpHeightUnit(height), STRING_BUFFER,
                    ZERO_FIELD_POSITION)
                .toString();
        }
    }

    /**
     *
     * @param height value in cm
     * @param unit value is set from user
     * @return round float height
     */
    public static String formatRoundJumpHeight(float height, MeasurementUnit unit) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return STRING_BUFFER.append(Math.round(unit.toJumpHeightUnit(height))).toString();
        }
    }

    /**
     *
     * @param velocity m/s
     * @param unit  value is set from user
     * @return formatted string use different unit
     */
    public static String formatTakeoffVelocity(float velocity, MeasurementUnit unit) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getJumpHeightFormatter().format(unit.toJumpHeightTakeoffVelocityUnit(velocity), STRING_BUFFER,
                    ZERO_FIELD_POSITION)
                .toString();
        }
    }

    public static String formatVo2Max(float vo2Max) {
        synchronized (STRING_BUFFER) {
            STRING_BUFFER.setLength(0);
            return getMaxOneDecimalFormatter().format(vo2Max, STRING_BUFFER, ZERO_FIELD_POSITION).toString();
        }
    }

    @Deprecated
    public static String formatElapsedTime(long seconds) {
        //noinspection deprecation
        return formatElapsedTime(seconds, true, true);
    }

    @Deprecated
    public static String formatElapsedTime(long seconds, boolean showHour) {
        //noinspection deprecation
        return formatElapsedTime(seconds, showHour, true);
    }

    @Deprecated
    private static String formatElapsedTime(long seconds, boolean showHour, boolean showSecond) {
        long h = 0L;
        long m = 0L;
        long s;
        if (seconds >= 60L) {
            if (seconds >= 3600L) {
                h = seconds / 3600L;
                m = (seconds % 3600L) / 60L;
            } else {
                m = seconds / 60L;
            }
            s = seconds % 60L;
        } else {
            s = seconds;
        }

        synchronized (STRING_BUILDER) {
            STRING_BUILDER.setLength(0);
            if (showHour) {
                if (h < 10L) {
                    STRING_BUILDER.append('0');
                }
                STRING_BUILDER.append(h).append(':');
            }

            if (m < 10L) {
                STRING_BUILDER.append('0');
            }
            STRING_BUILDER.append(m);

            if (showSecond) {
                STRING_BUILDER.append(':');
                if (s < 10L) {
                    STRING_BUILDER.append('0');
                }
                STRING_BUILDER.append(s);
            }
            return STRING_BUILDER.toString();
        }
    }

    public static CharSequence formatRelativeDateSpan(Resources resources, long timestamp) {
        return com.stt.android.utils.DateUtils.getRelativeDateString(resources,
            System.currentTimeMillis(), timestamp, false);
    }

    public static String formatElapsedTimeWithUnit(Resources resources, long seconds) {
        long hours = 0L;
        long minutes = 0L;
        if (seconds >= 3600L) {
            hours = seconds / 3600L;
            seconds -= hours * 3600L;
        }
        if (seconds >= 60L) {
            minutes = seconds / 60L;
            seconds -= minutes * 60L;
        }
        synchronized (STRING_BUILDER) {
            STRING_BUILDER.setLength(0);
            if (hours > 0L) {
                STRING_BUILDER.append(hours).append(' ').append(resources.getString(com.stt.android.core.R.string.hour));
            }
            if (minutes > 0L) {
                if (STRING_BUILDER.length() > 0) {
                    STRING_BUILDER.append(' ');
                }
                STRING_BUILDER.append(minutes).append(' ')
                    .append(resources.getString(com.stt.android.core.R.string.minute));
            }
            if (hours == 0L && seconds > 0L) {
                if (STRING_BUILDER.length() > 0) {
                    STRING_BUILDER.append(' ');
                }
                STRING_BUILDER.append(seconds).append(' ')
                    .append(resources.getString(com.stt.android.core.R.string.seconds));
            }
            return STRING_BUILDER.toString();
        }
    }

    public static String formatLocation(Context context, String country, String city) {
        String countryName = null;
        if (!TextUtils.isEmpty(country)) {
            Locale countryCode = LocaleUtils.fromIso3CountryCode(country);
            if (countryCode != null) {
                Locale locale = new Locale(context.getString(R.string.language_code));
                countryName = countryCode.getDisplayCountry(locale);
            }
        }

        synchronized (STRING_BUILDER) {
            STRING_BUILDER.setLength(0);

            if (!TextUtils.isEmpty(city)) {
                STRING_BUILDER.append(city);
                if (!TextUtils.isEmpty(countryName)) {
                    STRING_BUILDER.append(", ");
                }
            }
            if (!TextUtils.isEmpty(countryName)) {
                STRING_BUILDER.append(countryName);
            }

            return STRING_BUILDER.toString();
        }
    }

    public static CharSequence formatWorkoutValue(
        Resources resources,
        WorkoutValue workoutValue,
        SpanFactory valueSpanFactory,
        SpanFactory textSpanFactory
    ) {
        return formatWorkoutValue(resources, workoutValue, valueSpanFactory, textSpanFactory, false);
    }

    public static CharSequence formatWorkoutValue(
        Resources resources,
        WorkoutValue workoutValue,
        SpanFactory valueSpanFactory,
        SpanFactory textSpanFactory,
        boolean showLabel
    ) {
        if (workoutValue == null) return "";

        String value = workoutValue.getValue() != null
            ? workoutValue.getValue()
            : "";

        String unit;
        if (workoutValue.getUnitString() != null) {
            unit = workoutValue.getUnitString();
        } else if (workoutValue.getUnit() != null) {
            unit = resources.getString(workoutValue.getUnit());
        } else {
            unit = "";
        }
        return formatValueUnitLabel(
            value,
            unit,
            showLabel ? workoutValue.getLabel() : null,
            valueSpanFactory,
            textSpanFactory
        );
    }

    public static CharSequence formatValueUnitLabel(
        @NonNull String value,
        @NonNull String unit,
        @Nullable String label,
        SpanFactory valueSpanFactory,
        SpanFactory textSpanFactory
    ) {
        String text = label != null
            ? String.format(Locale.getDefault(), "%s\n%s", unit, label)
            : unit;

        SpannableString valueSpannable = TextFormatter
            .styleValueString(value, valueSpanFactory, textSpanFactory);

        SpannableString textSpannable = new SpannableString(text);
        for (Object span : textSpanFactory.createSpans()) {
            textSpannable.setSpan(
                span,
                0,
                textSpannable.length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return TextUtils.concat(valueSpannable, " ", textSpannable);
    }

    public static CharSequence formatValueAndUnit(
        @NonNull String value,
        @Nullable String unit,
        @NonNull SpanFactory valueSpanFactory,
        @Nullable SpanFactory unitSpanFactory
    ) {
        SpannableString valueSpannable = TextFormatter
            .styleValueString(value, valueSpanFactory, unitSpanFactory);

        if (unit != null && unitSpanFactory != null) {
            SpannableString unitSpannable = new SpannableString(unit);
            for (Object span : unitSpanFactory.createSpans()) {
                unitSpannable.setSpan(
                    span,
                    0,
                    unitSpannable.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            return TextUtils.concat(valueSpannable, " ", unitSpannable);
        } else {
            return valueSpannable;
        }
    }

    /**
     * Factory creating span instances suitable for SpannableString.
     */
    public interface SpanFactory {
        /**
         * Creates span instances
         * @return Span list
         */
        List<Object> createSpans();
    }

    /**
     * Sets different text appearances for values and other text in a string.
     *
     * Value characters are 0-9, decimal separator, minus sign, colon and infinity sign.
     *
     * @param input Input string
     * @param valueSpanFactory Factory for creating spans for values
     * @param textSpanFactory Factory for creating spans for other text
     * @return Spannable string
     */
    private static SpannableString styleValueString(
        String input,
        SpanFactory valueSpanFactory,
        SpanFactory textSpanFactory
    ) {

        SpannableString output = new SpannableString(input);

        int spanStart = -1;
        SpanFactory currentSpanFactory = null;
        for (int i = 0; i <= input.length(); i++) {
            SpanFactory spanFactory = null;
            if (i < input.length()) {
                spanFactory = VALUE_CHARS.indexOf(input.charAt(i)) >= 0 ? valueSpanFactory :
                    textSpanFactory;
            }
            if (spanFactory != currentSpanFactory) {
                if (spanStart >= 0 && currentSpanFactory != null) {
                    for (Object span : currentSpanFactory.createSpans()) {
                        output.setSpan(span, spanStart, i, Spanned
                            .SPAN_INCLUSIVE_EXCLUSIVE);
                    }
                }
                spanStart = i;
                currentSpanFactory = spanFactory;
            }
        }

        return output;
    }
}
