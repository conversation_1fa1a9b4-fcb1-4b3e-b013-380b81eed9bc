package com.stt.android.ui.utils

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.stt.android.R
import com.stt.android.ui.extensions.withClipOut
import com.stt.android.core.R as CR

/**
 * A custom FrameLayout that limits the maximum width of the child views to
 * [R.dimen.content_max_width] while still allowing to interact with the child Views inside this
 * parent View. Typically used as a parent for Views that would otherwise to end up too wide on
 * landscape orientation while still allowing to touch those Views normally from the edges of the
 * display.
 *
 * The size of this view is always the size of the parent.
 *
 * If this view is larger than the max child width, the children are positioned horizontally in
 * the middle of this view with the size set to the max child width. The height of the children is
 * always the same as the height of this View.
 */
class WidthLimiterLayout : FrameLayout {

    private val maxChildWidth = resources.getDimensionPixelSize(CR.dimen.content_max_width)
    private val willNotDraw: Boolean
    private val paint = Paint().apply {
        color = resources.getColor(R.color.light_grey, context.theme)
    }
    private var contentRect = Rect()

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    @SuppressLint("UseKtx")
    constructor(context: Context, attrs: AttributeSet?, attributeSetId: Int) : super(
        context,
        attrs,
        attributeSetId
    ) {
        context.obtainStyledAttributes(attrs, R.styleable.WidthLimiterLayout).let { a ->
            willNotDraw = a.getBoolean(R.styleable.WidthLimiterLayout_wllWillNotDraw, false)
            a.recycle()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // Limit the maximum width of the children to maxChildWidth
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val allowedWidth = width.coerceAtMost(maxChildWidth)
        val constrainedWidthMeasureSpec =
            MeasureSpec.makeMeasureSpec(allowedWidth, MeasureSpec.EXACTLY)

        super.onMeasure(constrainedWidthMeasureSpec, heightMeasureSpec)

        // Reset the width of this view the the size of the parent
        setMeasuredDimension(widthMeasureSpec, measuredHeight)

        // Update the clip-out rect that is used to draw background during dispatchDraw()
        if (allowedWidth < measuredWidth) {
            val paddingWidth = (measuredWidth - allowedWidth) / 2
            contentRect.set(paddingWidth, 0, measuredWidth - paddingWidth, measuredHeight)
        } else {
            contentRect.setEmpty()
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        val childTop = this.paddingTop
        val childBottom = this.measuredHeight - this.paddingBottom
        for (i in 0 until childCount) {
            getChildAt(i).takeUnless { it.isGone }?.let { child ->
                // Position the child horizontally in the middle of the parent
                val horizontalPadding = (measuredWidth - child.measuredWidth) / 2
                child.layout(
                    horizontalPadding,
                    childTop,
                    measuredWidth - horizontalPadding,
                    childBottom
                )
            }
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        // Draw a background color if the child content area is narrower than the width of this view
        if (!willNotDraw && !contentRect.isEmpty) {
            canvas.withClipOut(contentRect) {
                canvas.drawPaint(paint)
            }
        }
        super.dispatchDraw(canvas)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // Forward the event to the first child view that handles it
        for (i in 0 until childCount) {
            getChildAt(i).takeIf { it.isVisible }?.let { child ->
                if (child.onTouchEvent(event)) {
                    return true
                }
            }
        }
        return false
    }
}
