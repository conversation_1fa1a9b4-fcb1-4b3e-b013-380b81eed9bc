package com.stt.android.ui.compose

import androidx.compose.runtime.Composable
import com.airbnb.epoxy.ModelCollector
import com.airbnb.epoxy.composableInterop
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme

/**
 * Add [composeFunction] surrounded with [AppTheme] as a model in [com.airbnb.epoxy
 * .EpoxyController].
 *
 * See [com.airbnb.epoxy.ComposeEpoxyModel].
 */
fun ModelCollector.composableInteropInAppTheme(
    id: String,
    vararg keys: Any,
    composeFunction: @Composable () -> Unit
) {
    composableInterop(id, keys) {
        AppTheme {
            composeFunction()
        }
    }
}

fun ModelCollector.composableInteropInM3AppTheme(
    id: String,
    vararg keys: Any,
    composeFunction: @Composable () -> Unit
) {
    composableInterop(id, keys) {
        M3AppTheme {
            composeFunction()
        }
    }
}
