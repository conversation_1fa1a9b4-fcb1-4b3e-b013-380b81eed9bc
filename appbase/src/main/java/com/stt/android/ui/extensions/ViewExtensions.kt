package com.stt.android.ui.extensions

import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.core.view.isGone
import androidx.core.view.isVisible

fun View.closeKeyboard() {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
    imm?.hideSoftInputFromWindow(windowToken, 0)
}

fun View.setVisible() {
    if (!isVisible) visibility = View.VISIBLE
}

fun View.setGone() {
    if (!isGone) visibility = View.GONE
}
