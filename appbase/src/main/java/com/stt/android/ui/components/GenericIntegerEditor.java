package com.stt.android.ui.components;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.Editable;
import android.text.InputType;
import android.util.AttributeSet;
import android.widget.EditText;
import com.stt.android.R;

public class GenericIntegerEditor extends InlineEditor<Integer> {
    private int maxValue;
    private int minValue;

    public GenericIntegerEditor(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public GenericIntegerEditor(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void setInlineEditorActionListener(InlineEditorActionListener<Integer> inlineEditorActionListener){
        this.inlineEditorActionListener = inlineEditorActionListener;
    }

    @Override
    protected void init(Context context, AttributeSet attrs) {
        super.init(context, attrs);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.GenericIntegerEditor);
        maxValue = a.getInt(R.styleable.GenericIntegerEditor_editorMax, Integer.MAX_VALUE);
        minValue = a.getInt(R.styleable.GenericIntegerEditor_editorMin, Integer.MIN_VALUE);
        a.recycle();
    }

    @Override
    protected Integer convertToValue(Editable text) {
        String string = text.toString();
        if ("".equals(string)) {
            return 0;
        }
        try {
            return Integer.parseInt(string);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    @Override
    protected Integer getValidValue(Integer valueToCheck) {
        if (valueToCheck < minValue) {
            return minValue;
        } else if (valueToCheck > maxValue) {
            return maxValue;
        }
        return valueToCheck;
    }

    public void setMinMax(int min, int max){
        minValue = min;
        maxValue = max;

        if (minValue < 0) {
            EditText editText = getEditorValue();
            editText.setInputType(editText.getInputType() | InputType.TYPE_NUMBER_FLAG_SIGNED);
        }
    }

    /**
     * @return true if the value is in-between the min and max values
     */
    @Override
    protected boolean isValid(Integer valueToCheck) {
        return valueToCheck <= maxValue && valueToCheck >= minValue;
    }
}
