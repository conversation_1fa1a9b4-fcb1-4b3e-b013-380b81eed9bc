package com.stt.android.ui.components.charts.model

import com.github.mikephil.charting.data.BarEntry

data class SleepStageEntry(
    val xStart: Long,
    val xEnd: Long,
    val stage: ExtendedSleepStage,
) : BarEntry(xStart.toFloat(), 0f)

enum class ExtendedSleepStage {
    AWAKE,
    REM,
    LIGHT,
    DEEP,
    NAP,
}

data class SleepRegion(
    val xStart: Long,
    val xEnd: Long,
    val nap: <PERSON>olean,
)
