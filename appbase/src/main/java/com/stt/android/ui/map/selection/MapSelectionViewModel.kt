package com.stt.android.ui.map.selection

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.HeatmapType
import com.stt.android.domain.user.RoadSurfaceType
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.maps.MapType
import com.stt.android.maps.TopRouteType
import com.stt.android.maps.TopRouteTypes
import com.stt.android.maps.isAvailable
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.HideCyclingForbiddenRoadsLiveData
import com.stt.android.ui.map.SelectedHeatmapTypeLiveData
import com.stt.android.ui.map.SelectedMapTypeLiveData
import com.stt.android.ui.map.SelectedMyTracksGranularityLiveData
import com.stt.android.ui.map.SelectedRoadSurfaceTypesLiveData
import com.stt.android.ui.map.ShowPOIsLiveData
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.home.explore.ExploreAnalytics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.DayOfWeek
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class MapSelectionViewModel @Inject constructor(
    val selectedMapType: SelectedMapTypeLiveData,
    val selectedHeatmapType: SelectedHeatmapTypeLiveData,
    val selectedRoadSurfaceTypes: SelectedRoadSurfaceTypesLiveData,
    val hideCyclingForbiddenRoadsLiveData: HideCyclingForbiddenRoadsLiveData,
    val showPOIsLiveData: ShowPOIsLiveData,
    val selectedMyTracksGranularity: SelectedMyTracksGranularityLiveData,
    private val mapSelectionModel: MapSelectionModel,
    private val userSettingsController: UserSettingsController,
    private val currentUserController: CurrentUserController,
    @TopRouteTypes private val topRouteTypes: List<TopRouteType>,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    coroutinesDispatchers: CoroutinesDispatchers,
    private val exploreAnalytics: ExploreAnalytics,
) : ViewModel() {
    val firstDayOfWeek: DayOfWeek get() = userSettingsController.settings.firstDayOfTheWeek

    var mapCenter: LatLng? = null
    var analyticsSource: String = AnalyticsEvent.USER_PROFILE_SCREEN

    private val _mapTypes = MutableStateFlow<List<MapType>>(emptyList())
    val mapTypes: Flow<List<MapType>> = _mapTypes.asStateFlow().filterNot(List<MapType>::isEmpty)

    val heatmapTypes: List<HeatmapType> = mapSelectionModel.loadHeatmapTypes()
    val roadSurfaceTypes: List<RoadSurfaceType> = mapSelectionModel.loadRoadSurfaceTypes()

    var showHeatmaps = false
    var mapsProviderName: String? = null

    val hasPremium: LiveData<Boolean> = isSubscribedToPremiumUseCase.hasPremium()

    private val _popularRoutesSelectionContainer =
        MutableStateFlow<PopularRoutesSelectionContainer?>(null)
    val popularRoutesSelectionContainer: StateFlow<PopularRoutesSelectionContainer?> =
        _popularRoutesSelectionContainer.asStateFlow()

    private val coroutineScope = CoroutineScope(coroutinesDispatchers.io + SupervisorJob())

    init {
        loadMapTypes()
        loadPopularRoutesSelection()
    }

    private fun loadMapTypes() {
        coroutineScope.launch {
            // Built-in maps are emitted immediately. Dynamic maps are appended to the list when they
            // have been loaded, typically from the cache also immediately.
            val builtInMapTypes = mapSelectionModel.loadBuiltInMapTypes()
            _mapTypes.value = filterMapTypeList(builtInMapTypes)

            val dynamicMapTypes = runSuspendCatching {
                val userCountry = userSettingsController.settings
                    .country
                    .lowercase(Locale.ROOT)
                mapSelectionModel.loadDynamicMapTypes()
                    .sortedByDescending { it.getPriority(userCountry) }
            }.getOrElse { e ->
                Timber.w(e, "Failed to load dynamic maps")
                emptyList()
            }

            _mapTypes.value = filterMapTypeList(builtInMapTypes + dynamicMapTypes)
        }
    }

    private fun filterMapTypeList(types: List<MapType>): List<MapType> = types.filter { mapType ->
        mapType.isAvailable(
            currentUserController = currentUserController,
            userSettingsController = userSettingsController,
        )
    }

    private fun loadPopularRoutesSelection() {
        val allActivityTypes = topRouteTypes.map { it.activityType }
        val container = PopularRoutesSelectionContainer(
            selectedTypes = mapSelectionModel.selectedPopularRoutesActivityTypes,
            allTypes = allActivityTypes,
            onSelectChanged = ::handlePopularRoutesActivityTypeChanged
        )
        _popularRoutesSelectionContainer.value = container
    }

    private fun handlePopularRoutesActivityTypeChanged(activityTypes: List<ActivityType>) {
        mapSelectionModel.selectedPopularRoutesActivityTypes = activityTypes
        exploreAnalytics.trackMapChangeMode(analyticsSource, AnalyticsPropertyValue.MapFeatureChanged.POPULAR_ROUTES)
    }

    fun setSelectedMapType(mapType: MapType) {
        mapSelectionModel.selectedMapType = mapType
    }

    fun getSelectedMapTypeIndex() = _mapTypes.value.indexOf(selectedMapType.value)

    fun setSelectedHeatmap(heatmapType: HeatmapType?) {
        if (heatmapType != mapSelectionModel.selectedHeatmap) {
            mapSelectionModel.selectedHeatmap = heatmapType
        }
    }

    fun toggleRoadSurfaceSelected(roadSurfaceType: RoadSurfaceType) {
        val oldSelection = mapSelectionModel.selectedRoadSurfaces
        val wasRoadSurfaceSelected = oldSelection.contains(roadSurfaceType)
        mapSelectionModel.selectedRoadSurfaces = if (wasRoadSurfaceSelected) {
            oldSelection.minus(roadSurfaceType)
        } else {
            oldSelection.plus(roadSurfaceType)
        }
    }

    fun clearRoadSurfaceSelection() {
        mapSelectionModel.selectedRoadSurfaces = emptyList()
    }

    fun setHideCyclingForbiddenRoads(hide: Boolean) {
        mapSelectionModel.hideCyclingForbiddenRoads = hide
        exploreAnalytics.trackMapChangeMode(analyticsSource, AnalyticsPropertyValue.MapFeatureChanged.ROAD_SURFACE)
    }

    fun setShowPOIs(show: Boolean) {
        if (mapSelectionModel.showPOIs != show) {
            mapSelectionModel.showPOIs = show
            exploreAnalytics.trackMapChangeMode(analyticsSource, AnalyticsPropertyValue.MapFeatureChanged.POIS)
        }
    }

    fun setSelectedMyTracksGranularity(granularity: MyTracksGranularity?) {
        val currentGranularity = mapSelectionModel.selectedMyTracksGranularity
        if (granularity != currentGranularity ||
            granularity?.type == MyTracksGranularity.Type.CUSTOM_DATES
        ) {
            mapSelectionModel.selectedMyTracksGranularity = granularity
        }
    }

    fun getSelectedHeatmapIndex() = heatmapTypes.indexOf(selectedHeatmapType.value)

    private companion object {
        /**
         * Higher value means higher priority.
         */
        fun MapType.getPriority(userCountry: String): Int = when {
            // Country specific map that is available for the user has highest priority.
            availableCountries?.any { it.lowercase(Locale.ROOT) == userCountry } == true -> 2

            // Maps available globally has second highest priority.
            availableCountries.isNullOrEmpty() -> 1

            // Everything else.
            else -> 0
        }
    }
}
