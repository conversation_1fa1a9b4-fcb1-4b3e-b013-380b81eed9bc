package com.stt.android.ui.components;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.data.TimeUtilsKt;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.ui.utils.ThrottlingOnClickListener;
import com.stt.android.utils.CalendarProviderKt;
import dagger.hilt.android.AndroidEntryPoint;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.Calendar;
import javax.inject.Inject;

@AndroidEntryPoint
public class DateTimeEditor extends Editor<Calendar> {
    @Inject
    UserSettingsController userSettingsController;

    private final boolean showTime;
    private final boolean allowFuture;

    public DateTimeEditor(final Context context, AttributeSet attrs) {
        super(context, attrs);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.DateTimeEditor);
        showTime = a.getBoolean(R.styleable.DateTimeEditor_showTime, true);
        allowFuture = a.getBoolean(R.styleable.DateTimeEditor_allowFuture, true);
        a.recycle();

        setOnClickListener(new ThrottlingOnClickListener(v -> {
            final Calendar dateTime = getValueSafety();
            DatePickerDialog dateDialog = new DatePickerDialog(context,
                (view, year, monthOfYear, dayOfMonth) -> {
                    dateTime.set(year, monthOfYear, dayOfMonth);
                    setValue(dateTime);

                    if (showTime) {
                        TimePickerDialog timeDialog = new TimePickerDialog(context,
                            (view1, hourOfDay, minute) -> {
                                dateTime.set(Calendar.HOUR_OF_DAY, hourOfDay);
                                dateTime.set(Calendar.MINUTE, minute);
                                setValue(dateTime);
                            }, dateTime.get(Calendar.HOUR_OF_DAY),
                            dateTime.get(Calendar.MINUTE), true);
                        // Regardless of the time set or cancelled we want to
                        // save the new date
                        timeDialog.setOnDismissListener(
                            dialog -> {
                                Calendar newDateTime = getValue();
                                notifyListener(newDateTime);
                            });
                        timeDialog.show();
                    } else {
                        notifyListener(dateTime);
                    }
                }, dateTime.get(Calendar.YEAR), dateTime.get(Calendar.MONTH),
                dateTime.get(Calendar.DAY_OF_MONTH));

            dateDialog.getDatePicker().setMinDate(TimeUtilsKt.getReleasedUTCMilli());

            DayOfWeek firstDayOfWeek = userSettingsController.getSettings().getFirstDayOfTheWeek();
            dateDialog.getDatePicker()
                .setFirstDayOfWeek(CalendarProviderKt.toCalendarInt(firstDayOfWeek));

            if (!allowFuture) {
                long todayEpochMillis = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000L;
                dateDialog.getDatePicker().setMaxDate(todayEpochMillis);
            }
            dateDialog.show();
        }));
    }

    /**
     * <a href="https://console.firebase.google.com/project/suunto-app/crashlytics/app/android:com.stt.android.suunto/issues/1fc980bad59c77b037f42d6fb1b73d53?time=last-seven-days&types=crash&versions=4.100.8%20(4100008)&sessionEventKey=66FFF88700ED00012C3D10B48E40439D_2000889180778992881">...</a>
     * If there is no explicit value set, it will be null when we get it.
     *
     * @return value or current calendar
     */
    private Calendar getValueSafety() {
        Calendar value = getValue();
        if (value == null) {
            value = Calendar.getInstance();
        }
        return value;
    }

    @Override
    protected String getSubtitleForValue(Calendar value) {
        return showTime ? TextFormatter.formatDateTime(getContext(), value.getTimeInMillis())
            : TextFormatter.formatDate(getContext(), value.getTimeInMillis());
    }
}
