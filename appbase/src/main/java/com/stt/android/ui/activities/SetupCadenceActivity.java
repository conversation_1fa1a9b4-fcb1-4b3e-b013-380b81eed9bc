package com.stt.android.ui.activities;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.R;
import com.stt.android.bluetooth.BleCadenceScanner;
import com.stt.android.bluetooth.BluetoothScanListener;
import com.stt.android.cadence.CadenceHelper;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;
import timber.log.Timber;

// makes sure this is only launched when Bluetooth Low Energy is supported
@AndroidEntryPoint
public class SetupCadenceActivity extends SetupSensorActivity implements BluetoothScanListener {
    @Nullable
    @Inject
    BleCadenceScanner bleCadenceScanner;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initializeUi(R.string.cadence_setup_instruction, R.drawable.setup_cadence,
            R.string.cadence_setup_not_found_title, R.string.cadence_setup_not_found_text,
            R.string.cadence_setup_connecting, R.string.cadence_setup_connect);

        findViewById(R.id.pinCode).setVisibility(View.GONE);
    }

    @Override
    protected void onPause() {
        if (bleCadenceScanner != null) {
            bleCadenceScanner.stopScanning();
        }

        super.onPause();
    }

    @Override
    protected void startBluetoothDiscovery() {
        if (bleCadenceScanner != null) {
            bleCadenceScanner.startScanning(this);
        }
    }

    @SuppressLint("MissingPermission")
    @Override
    public void onDeviceFound(@NonNull BluetoothDevice device) {
        Timber.d("Cadence found: address=%s, name=%s", device.getAddress(), device.getName());

        CadenceHelper.saveCadence(this, device);
        startActivity(DisplayCadenceActivity.newStartIntent(this, device));
        finish();
    }

    @Override
    public void onNoDeviceFound() {
        Timber.d("No cadence found");
        showNoDeviceUi();
    }

    @Override
    public void onAdapterDisabled() {
        requestBluetooth();
    }

    @Override
    public void onLocationPermissionRequired() {
        requestLocationPermission();
    }

    @Override
    public void onLocationDisabled() {
        requestLocationService();
    }

    @Override
    public void onNearbyDevicesPermissionRequired() {
        requestNearbyDevicesPermission();
    }
}
