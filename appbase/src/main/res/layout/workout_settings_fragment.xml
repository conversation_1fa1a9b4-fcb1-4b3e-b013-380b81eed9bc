<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:stt="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_grey"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/workoutSettingsMainView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/continueBtPanel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.stt.android.ui.utils.WidthLimiterLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?android:colorBackground"
                android:orientation="vertical">

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/activityTypeSelectionButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    stt:workoutSettingItemTitle="@string/activity_type" />

                <View style="@style/HorizontalDivider" />

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/voiceFeedbackButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    stt:workoutSettingImageItemImage="@drawable/ic_unmute_fill"
                    stt:workoutSettingItemTitle="@string/voice_feedback" />

                <View style="@style/HorizontalDivider" />

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/autoPauseButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    stt:workoutSettingImageItemImage="@drawable/ic_pause_fill"
                    stt:workoutSettingItemTitle="@string/auto_pause" />

                <View
                    android:id="@+id/autoPauseButtonDivider"
                    style="@style/HorizontalDivider" />

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/selectGhostTargetButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    stt:workoutSettingImageItemImage="@drawable/ic_stopwatch_fill"
                    stt:workoutSettingItemTitle="@string/ghost_target" />

                <View
                    android:id="@+id/selectGhostTargetButtonDivider"
                    style="@style/HorizontalDivider" />

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/selectFollowRouteButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    stt:workoutSettingImageItemImage="@drawable/ic_route_fill"
                    stt:workoutSettingItemTitle="@string/follow_route" />

                <View
                    android:id="@+id/selectFollowRouteButtonDivider"
                    style="@style/HorizontalDivider" />

                <com.stt.android.workoutsettings.WorkoutSettingImageItem
                    android:id="@+id/setGpsFrequencyButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/start_workout_setting_with_subtitle_row_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:visibility="gone"
                    stt:workoutSettingItemTitle="@string/gps_frequency" />

                <ImageView
                    android:id="@+id/banner_ad"
                    style="@style/ImageAd"
                    android:layout_gravity="center_horizontal"
                    android:adjustViewBounds="true"
                    android:paddingBottom="@dimen/size_spacing_xsmall"
                    android:src="@drawable/banner_ad"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_spacing_medium" />

            </LinearLayout>

            <FrameLayout
                android:id="@+id/toolTip"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </com.stt.android.ui.utils.WidthLimiterLayout>

    </ScrollView>

    <FrameLayout
        android:id="@+id/continueBtPanel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="?android:colorBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_max="@dimen/content_max_width">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_spacing_xsmall"
            android:background="@drawable/shadow_background" />

        <Button
            android:id="@+id/continueBt"
            style="@style/Button.Wide.SecondaryAccent"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:text="@string/continue_str" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
