<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

        <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".ui.activities.WorkoutEditDetailsActivity">

        <!-- Otherwise the edit text below wil have focus and open the keyboard when created -->
        <View
            android:id="@+id/focused"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone" />

        <com.stt.android.ui.components.DescriptionEditor
            android:id="@+id/descriptionEditor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:imeOptions="actionDone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:maxLength="256"
            app:layout_constraintTop_toBottomOf="@id/focused" />

        <com.stt.android.ui.components.DescriptionEditor
            android:id="@+id/tags_editor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_xsmall"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:enabled="false"
            android:focusable="false"
            android:inputType="none"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descriptionEditor" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/tags_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tags_editor" />

        <FrameLayout
            android:id="@+id/photoContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tags_container" />

        <FrameLayout
            android:id="@+id/workoutLocationContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/photoContainer"
            tools:layout_height="156dp"
            tools:visibility="visible" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workoutLocationContainer" />

        <com.stt.android.ui.components.WorkoutTypeEditor
            android:id="@+id/workoutTypeEditor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:padding="@dimen/size_spacing_medium"
            app:editorTitle="@string/activity_type"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workoutLocationContainer" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workoutTypeEditor" />

        <com.stt.android.ui.components.DateTimeEditor
            android:id="@+id/startTimeEditor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:padding="@dimen/size_spacing_medium"
            app:editorTitle="@string/start_time"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workoutTypeEditor"
            app:showTime="true"
            app:allowFuture="false" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/startTimeEditor" />

        <com.stt.android.ui.components.DurationEditor
            android:id="@+id/durationEditor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:padding="@dimen/size_spacing_medium"
            app:editorTitle="@string/duration"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/startTimeEditor" />

        <View
            android:id="@+id/distanceBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/distanceUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/distanceUnit" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/durationEditor" />

        <TextView
            android:id="@+id/distanceUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_medium"
            android:text="@string/distance"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/durationEditor"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/distanceValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/durationEditor"
            style="@style/Body.Larger" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/distanceUnit" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/maxSpeedGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="maxSpeedBackground,maxSpeedUnit,maxSpeedValue,maxSpeedDivider" />

        <View
            android:id="@+id/maxSpeedBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/maxSpeedUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/maxSpeedUnit" />

        <TextView
            android:id="@+id/maxSpeedUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/workout_values_headline_max_speed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/distanceUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/maxSpeedValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/distanceUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/maxSpeedDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxSpeedUnit" />

        <View
            android:id="@+id/avgHrBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/avgHrUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/avgHrUnit" />

        <View
            android:id="@+id/maxHrBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/maxHrUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/maxHrUnit" />

        <TextView
            android:id="@+id/avgHrUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/avg_heart_rate"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxSpeedUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/avgHrValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxSpeedUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/avgHrDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avgHrUnit" />

        <View
            android:id="@+id/energyBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/energyUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/energyUnit" />

        <TextView
            android:id="@+id/maxHrUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/max_heart_rate"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avgHrUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/maxHrValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avgHrUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/maxHrDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxHrUnit" />

        <View
            android:id="@+id/stepsBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/stepsUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/stepsUnit" />

        <TextView
            android:id="@+id/energyUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/energy"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxHrUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/energyValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/maxHrUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/energyDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/energyUnit" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/distanceGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="distanceUnit,distanceValue,distanceBackground" />

        <TextView
            android:id="@+id/stepsUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/step_count"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descentUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/stepsValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descentUnit"
            style="@style/Body.Larger" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/hideFromDivesGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="avgHrUnit,avgHrValue,maxHrUnit,maxHrValue,energyUnit,energyValue,stepsUnit,stepsValue,avgHrDivider,maxHrDivider,
                energyDivider,stepsDivider,avgHrBackground,maxHrBackground,energyBackground,stepsBackground" />

        <View
            android:id="@+id/stepsDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/stepsUnit" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/tssGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tssBackground,tssUnit,tssValue,tssDivider" />

        <View
            android:id="@+id/tssBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/tssUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tssUnit" />

        <TextView
            android:id="@+id/tssUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/workout_values_headline_tss"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/energyUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/tssValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/energyUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/tssDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tssUnit" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/ascentGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="ascentBackground,ascentUnit,ascentValue,ascentDivider" />

        <View
            android:id="@+id/ascentBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/ascentUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/ascentUnit" />

        <TextView
            android:id="@+id/ascentUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/summary_item_title_ascent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tssUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/ascentValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tssUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/ascentDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ascentUnit" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/descentGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="descentBackground,descentUnit,descentValue,descentDivider" />

        <View
            android:id="@+id/descentBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="@id/descentUnit"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/descentUnit" />

        <TextView
            android:id="@+id/descentUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/summary_item_title_descent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ascentUnit"
            style="@style/Body.Larger" />

        <TextView
            android:id="@+id/descentValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_spacing_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ascentUnit"
            style="@style/Body.Larger" />

        <View
            android:id="@+id/descentDivider"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_divider"
            android:background="?suuntoDividerColor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/descentUnit" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
