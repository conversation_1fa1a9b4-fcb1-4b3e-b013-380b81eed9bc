<?xml version="1.0" encoding="utf-8"?>
<com.stt.android.workoutdetail.comments.DragToDismissFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/elasticDismissFrameLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground"
    android:focusableInTouchMode="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusableInTouchMode="false">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/workoutHeaderContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true">
            <com.stt.android.workoutdetail.comments.WorkoutHeaderView
                android:id="@+id/workoutHeader"
                android:layout_width="match_parent"
                android:layout_height="@dimen/height_toolbar"/>
        </androidx.core.widget.NestedScrollView>

        <View
            android:id="@+id/divider"
            android:layout_below="@id/workoutHeaderContainer"
            style="@style/HorizontalDivider"/>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/commentFormContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">
            <com.stt.android.workoutdetail.comments.CommentTextForm
                android:id="@+id/commentForm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/size_spacing_xsmall"
                android:layout_marginEnd="@dimen/padding"
                android:layout_marginStart="@dimen/padding"
                android:layout_marginTop="@dimen/smaller_padding"/>
        </androidx.core.widget.NestedScrollView>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/commentsList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/commentFormContainer"
            android:layout_below="@id/divider"
            android:fillViewport="true"
            android:orientation="vertical"
            android:overScrollMode="never"
            android:scrollbarStyle="insideOverlay"
            android:scrollbars="vertical"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>


        <TextView
            android:id="@+id/noComments"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/commentFormContainer"
            android:layout_below="@id/divider"
            android:padding="@dimen/padding"
            android:text="@string/be_first_to_comment"
            android:textAppearance="@style/Body.Medium"/>

    </RelativeLayout>

    <ProgressBar
        android:id="@+id/loadingSpinner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        style="@style/Widget.AppCompat.ProgressBar"/>

</com.stt.android.workoutdetail.comments.DragToDismissFrameLayout>
