<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/diary_calendar_nav_graph"
    app:startDestination="@id/diaryCalendarMonthPagerFragment">

    <fragment
        android:id="@+id/diaryCalendarWeekPagerFragment"
        android:name="com.stt.android.home.diary.diarycalendar.week.DiaryCalendarWeekPagerFragment"
        android:label="fragment_diary_calendar_week">
        <deepLink app:uri="com.sports-tracker.suunto://campaign/weekview" />
        <deepLink app:uri="com.sports-tracker.suunto://campaign/weekview/{startOfWeek}" />
        <deepLink app:uri="com.sports-tracker://campaign/weekview" />
        <deepLink app:uri="com.sports-tracker://campaign/weekview/{startOfWeek}" />
        <argument
            android:name="startOfWeek"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showActivitiesList"
            android:defaultValue="false"
            app:nullable="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_diaryCalendarWeekFragment_to_diaryCalendarMonthFragment"
            app:destination="@id/diaryCalendarMonthPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarWeekFragment_to_diaryCalendarYearFragment"
            app:destination="@id/diaryCalendarYearPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarWeekFragment_to_diaryCalendarLast30DaysFragment"
            app:destination="@id/diaryCalendarLast30DaysFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/diaryCalendarMonthPagerFragment"
        android:name="com.stt.android.home.diary.diarycalendar.month.DiaryCalendarMonthPagerFragment"
        android:label="DiaryCalendarMonthFragment">
        <deepLink app:uri="com.sports-tracker.suunto://campaign/monthview" />
        <deepLink app:uri="com.sports-tracker.suunto://campaign/monthview/{month}" />
        <deepLink app:uri="com.sports-tracker://campaign/monthview" />
        <deepLink app:uri="com.sports-tracker://campaign/monthview/{month}" />
        <argument
            android:name="month"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showActivitiesList"
            android:defaultValue="false"
            app:nullable="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_diaryCalendarMonthFragment_to_diaryCalendarWeekFragment"
            app:destination="@id/diaryCalendarWeekPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarMonthFragment_to_diaryCalendarYearFragment"
            app:destination="@id/diaryCalendarYearPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarMonthFragment_to_diaryCalendarLast30DaysFragment"
            app:destination="@id/diaryCalendarLast30DaysFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/diaryCalendarYearPagerFragment"
        android:name="com.stt.android.home.diary.diarycalendar.year.DiaryCalendarYearPagerFragment"
        android:label="DiaryCalendarYearFragment">
        <deepLink app:uri="com.sports-tracker.suunto://campaign/yearview" />
        <deepLink app:uri="com.sports-tracker.suunto://campaign/yearview/{year}" />
        <deepLink app:uri="com.sports-tracker://campaign/yearview" />
        <deepLink app:uri="com.sports-tracker://campaign/yearview/{year}" />
        <argument
            android:name="year"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showActivitiesList"
            android:defaultValue="false"
            app:nullable="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_diaryCalendarYearFragment_to_diaryCalendarWeekFragment"
            app:destination="@id/diaryCalendarWeekPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarYearFragment_to_diaryCalendarMonthFragment"
            app:destination="@id/diaryCalendarMonthPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarYearFragment_to_diaryCalendarLast30DaysFragment"
            app:destination="@id/diaryCalendarLast30DaysFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/diaryCalendarLast30DaysFragment"
        android:name="com.stt.android.home.diary.diarycalendar.last30days.DiaryCalendarLast30DaysPagerFragment"
        android:label="DiaryCalendarLast30DaysFragment">
        <deepLink app:uri="com.sports-tracker.suunto://campaign/last30daysview" />
        <deepLink app:uri="com.sports-tracker://campaign/last30daysview" />
        <argument
            android:name="showActivitiesList"
            android:defaultValue="false"
            app:nullable="false"
            app:argType="boolean" />
        <action
            android:id="@+id/action_diaryCalendarLast30DaysFragment_to_diaryCalendarWeekFragment"
            app:destination="@id/diaryCalendarWeekPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarLast30DaysFragment_to_diaryCalendarMonthFragment"
            app:destination="@id/diaryCalendarMonthPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_diaryCalendarLast30DaysFragment_to_diaryCalendarYearFragment"
            app:destination="@id/diaryCalendarYearPagerFragment"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/diary_calendar_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>
</navigation>
