<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/recent_workout_summary_appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/recent_workout_summary_toolbar"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:text="@string/previous_capital"
            android:theme="@style/Toolbar.Native"
            style="@style/Toolbar.Native"/>
    </com.google.android.material.appbar.AppBarLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.stt.android.ui.components.RecentWorkoutSummaryView
        android:id="@+id/recentWorkoutSummaryView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@id/recent_workout_summary_appbar_layout"/>

    <View
        android:id="@+id/workoutSummaryViewBackground"
        android:layout_width="match_parent"
        android:layout_height="@dimen/feed_card_gradient_height"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:background="@drawable/section_divider_gradient"
        app:layout_constraintTop_toBottomOf="@id/recentWorkoutSummaryView"/>

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/workoutSummaryView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/size_spacing_medium"
        app:layout_constraintTop_toTopOf="@id/workoutSummaryViewBackground"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/slidingTabs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabMode="scrollable"
        app:layout_constraintTop_toBottomOf="@id/workoutSummaryView"
        style="@style/TabBar"/>

    <com.stt.android.ui.components.NonSwipeableViewPager
        android:id="@+id/summaryViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="@dimen/size_spacing_small"
        android:paddingEnd="@dimen/size_spacing_small"
        app:layout_constraintTop_toBottomOf="@id/slidingTabs"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <RelativeLayout
        android:id="@+id/progress_bar_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:elevation="20dp"
        tools:background="@color/transparent">

        <ProgressBar
            android:id="@+id/loadingSpinner"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
