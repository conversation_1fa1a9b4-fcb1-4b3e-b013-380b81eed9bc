<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="tts_language">de</string>
    <string name="tts_start">Start</string>
    <string name="tts_stop">Stop</string>
    <string name="tts_autopause">Auto Pause aktiviert</string>
    <string name="tts_autoresume">Weiter</string>
    <string name="tts_resume"><PERSON><PERSON></string>
    <string name="tts_lap_pace_metric">Tempo: %s pro Kilometer.</string>
    <string name="tts_lap_pace_imperial">Tempo: %s pro Meile.</string>
    <string name="tts_lap_swim_pace_metric">Runden-Tempo: %s pro 100 Meter.</string>
    <string name="tts_lap_swim_pace_imperial">Runden-Tempo: %s pro 100 Yards.</string>
    <string name="tts_lap_time">Rundenzeit %1$s</string>
    <string name="tts_total_time">%1$s.</string>

    <!-- We need two tts_kilometers -->
    <!-- we use string when it's not a full unit (1.23 kilometers, 2.1 kilometers) -->
    <string name="tts_kilometers">%1$s Kilometer</string>
    <!-- we can use the plurals when it's a full unit (1 kilometer, 2 kilometers) -->
    <plurals name="tts_kilometers">
        <item quantity="one">Ein Kilometer</item>
        <item quantity="other">%1$s Kilometer</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_miles">%1$s Meilen</string>

    <plurals name="tts_miles">
        <item quantity="one">Eine Meile</item>
        <item quantity="other">%1$s Meilen</item>
    </plurals>

    <!-- Same as per kilometers -->
    <string name="tts_nautical_miles">%1$s Seemeilen</string>

    <plurals name="tts_nautical_miles">
        <item quantity="one">%1$s Seemeile</item>
        <item quantity="few">%1$s Seemeilen</item>
        <item quantity="many">%1$s Seemeilen</item>
        <item quantity="other">%1$s Seemeilen</item>
    </plurals>

    <plurals name="tts_seconds">
        <item quantity="one">Eine Sekunde</item>
        <item quantity="other">%1$d Sekunden</item>
    </plurals>

    <plurals name="tts_minutes">
        <item quantity="one">Eine Minute</item>
        <item quantity="other">%1$d Minuten</item>
    </plurals>

    <plurals name="tts_hours">
        <item quantity="one">Eine Stunde</item>
        <item quantity="other">%1$d Stunden</item>
    </plurals>

    <string name="tts_ghost_ahead">Du bist schneller %1$s.</string>
    <string name="tts_ghost_behind">Du liegst zurück %1$s.</string>
    <string name="tts_ghost_neutral">Du bist gleich.</string>

    <string name="tts_decimal_separator">Komma</string>

    <!-- Added in the Custom VoiceFeedback release -->
    <string name="tts_lap_speed">Rundengeschwindigkeit: %s .</string>
    <string name="tts_current_speed">Geschwindigkeit: %s .</string>
    <string name="tts_current_pace_metric">Tempo: %s pro Kilometer.</string>
    <string name="tts_current_pace_imperial">Tempo: %s pro Meile.</string>
    <string name="tts_average_speed">Durchschnittsgeschwindigkeit: %s .</string>
    <string name="tts_average_pace_metric">Durchschnittstempo: %s pro Kilometer.</string>
    <string name="tts_average_pace_imperial">Durchschnittstempo: %s pro Meile.</string>
    <string name="tts_total_distance">%1$s.</string>

    <string name="tts_current_heart_rate">Herzfrequenz: %d .</string>
    <string name="tts_average_heart_rate">Durchschnittliche Herzfrequenz: %d .</string>

    <string name="tts_energy">%1$d Kalorien.</string>
    <plurals name="tts_energy">
        <item quantity="other">%1$d kalorie.</item>
    </plurals>

    <string name="tts_current_cadence">Trittfrequenz: %d .</string>
    <string name="tts_average_cadence">Rundentrittfrequenz: %d .</string>

    <string name="tts_lap_number">Runde %d.</string>
    <string name="tts_lap_distance">Rundendistanz: %s.</string>
    <string name="tts_max_heart_rate">Max. Herzfrequenz: %d.</string>
    <string name="tts_total_time_with_title">Gesamtzeit: %1$s.</string>
    <string name="tts_heart_zone">Herzfrequenzzone: %d.</string>
    <string name="tts_lap_power">Rundenleistung: %s.</string>
    <string name="tts_lap_ascent">Runden aufsteigend: %s.</string>
    <string name="tts_lap_descent">Runden absteigend: %s.</string>
</resources>
