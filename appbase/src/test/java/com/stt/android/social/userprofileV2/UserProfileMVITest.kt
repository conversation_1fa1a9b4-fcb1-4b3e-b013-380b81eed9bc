package com.stt.android.social.userprofileV2

import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试UserProfile MVI模式重构
 */
class UserProfileMVITest {

    @Test
    fun `test UserProfileIntent creation`() {
        // 测试导航Intent
        val backIntent = BackClicked
        assertTrue(backIntent is UserProfileIntent)

        val editIntent = EditClicked
        assertTrue(editIntent is UserProfileIntent)

        // 测试带参数的Intent
        val user = User.INVALID
        val allActivityIntent = AllActivityClicked(user)
        assertEquals(user, allActivityIntent.user)

        val followersIntent = FollowersClicked(100)
        assertEquals(100, followersIntent.count)

        val followingIntent = FollowingClicked(50)
        assertEquals(50, followingIntent.count)

        // 测试社交Intent
        val friendBadgesIntent = FriendBadgesListClicked("testuser")
        assertEquals("testuser", friendBadgesIntent.username)

        val badgeImageIntent = BadgeImageClicked("badge123")
        assertEquals("badge123", badgeImageIntent.badgeId)

        // 测试菜单Intent
        val menuInfo = SettingMenuInfo(SettingMenuType.SETTING, 0, "Settings")
        val menuIntent = MenuClicked(menuInfo)
        assertEquals(menuInfo, menuIntent.menu)

        val moreMenu = MoreMenu.BLOCK
        val moreMenuIntent = MoreMenuItemClicked(moreMenu)
        assertEquals(moreMenu, moreMenuIntent.menu)

        // 测试运动Intent
        val workoutHeader = WorkoutHeader.INVALID
        val workoutIntent = WorkoutOpened(workoutHeader, "test_source")
        assertEquals(workoutHeader, workoutIntent.workoutHeader)
        assertEquals("test_source", workoutIntent.source)
    }

    @Test
    fun `test NavigationEvent creation`() {
        // 测试导航事件
        val backEvent = NavigationEvent.Back
        assertTrue(backEvent is NavigationEvent)

        val editEvent = NavigationEvent.Edit
        assertTrue(editEvent is NavigationEvent)

        // 测试带参数的导航事件
        val user = User.INVALID
        val allActivityEvent = NavigationEvent.AllActivity(user)
        assertEquals(user, allActivityEvent.user)

        val followersEvent = NavigationEvent.Followers(100)
        assertEquals(100, followersEvent.count)

        val followingEvent = NavigationEvent.Following(50)
        assertEquals(50, followingEvent.count)

        // 测试其他导航事件
        val friendBadgesEvent = NavigationEvent.FriendBadgesList("testuser")
        assertEquals("testuser", friendBadgesEvent.username)

        val badgeImageEvent = NavigationEvent.BadgeImage("badge123")
        assertEquals("badge123", badgeImageEvent.badgeId)

        val menuInfo = SettingMenuInfo(SettingMenuType.SETTING, 0, "Settings")
        val menuEvent = NavigationEvent.Menu(menuInfo)
        assertEquals(menuInfo, menuEvent.menu)

        val premiumEvent = NavigationEvent.PremiumSubscription(true)
        assertTrue(premiumEvent.isSubscribed)

        val workoutHeader = WorkoutHeader.INVALID
        val workoutEvent = NavigationEvent.Workout(workoutHeader, "test_source")
        assertEquals(workoutHeader, workoutEvent.workoutHeader)
        assertEquals("test_source", workoutEvent.source)
    }

    @Test
    fun `test Intent and Event inheritance`() {
        // 验证所有Intent都继承自UserProfileIntent
        val intents: List<UserProfileIntent> = listOf(
            BackClicked,
            EditClicked,
            AllActivityClicked(User.INVALID),
            PersonalRecordsClicked,
            BadgesClicked,
            SearchClicked,
            FollowersClicked(0),
            FollowingClicked(0),
            FollowButtonClicked,
            FriendBadgesListClicked(""),
            BadgeImageClicked(""),
            MenuClicked(SettingMenuInfo(SettingMenuType.SETTING, 0, "")),
            MoreMenuItemClicked(MoreMenu.BLOCK),
            PremiumSubscriptionClicked(false),
            WorkoutOpened(WorkoutHeader.INVALID, ""),
        )

        intents.forEach { intent ->
            assertTrue("$intent should be UserProfileIntent", intent is UserProfileIntent)
        }

        // 验证所有NavigationEvent都继承自UserProfileEvent
        val events: List<UserProfileEvent> = listOf(
            NavigationEvent.Back,
            NavigationEvent.Edit,
            NavigationEvent.AllActivity(User.INVALID),
            NavigationEvent.PersonalRecords,
            NavigationEvent.Badges,
            NavigationEvent.Search,
            NavigationEvent.Followers(0),
            NavigationEvent.Following(0),
            NavigationEvent.FriendBadgesList(""),
            NavigationEvent.BadgeImage(""),
            NavigationEvent.Menu(SettingMenuInfo(SettingMenuType.SETTING, 0, "")),
            NavigationEvent.PremiumSubscription(false),
            NavigationEvent.Workout(WorkoutHeader.INVALID, ""),
        )

        events.forEach { event ->
            assertTrue("$event should be UserProfileEvent", event is UserProfileEvent)
            assertTrue("$event should be NavigationEvent", event is NavigationEvent)
        }
    }
}
