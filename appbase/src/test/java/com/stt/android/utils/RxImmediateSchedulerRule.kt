package com.stt.android.utils

import io.reactivex.android.plugins.RxAndroidPlugins
import io.reactivex.internal.schedulers.TrampolineScheduler
import io.reactivex.plugins.RxJavaPlugins
import org.junit.rules.TestRule
import org.junit.runner.Description
import org.junit.runners.model.Statement

class RxImmediateSchedulerRule : TestRule {
    private val trampoline = TrampolineScheduler.instance()

    override fun apply(base: Statement, description: Description?): Statement? {
        return object : Statement() {
            @Throws(Throwable::class)
            override fun evaluate() {
                // Set RxJava AndroidSchedulers to trampoline
                RxAndroidPlugins.reset()
                RxAndroidPlugins.setInitMainThreadSchedulerHandler { trampoline }

                // Set RxJava Schedulers to trampoline
                RxJavaPlugins.reset()
                RxJavaPlugins.setInitComputationSchedulerHandler { trampoline }
                RxJavaPlugins.setInitIoSchedulerHandler { trampoline }
                RxJavaPlugins.setInitNewThreadSchedulerHandler { trampoline }
                RxJavaPlugins.setInitSingleSchedulerHandler { trampoline }

                try {
                    base.evaluate()
                } finally {
                    RxAndroidPlugins.reset()
                    RxJavaPlugins.reset()
                }
            }
        }
    }
}
