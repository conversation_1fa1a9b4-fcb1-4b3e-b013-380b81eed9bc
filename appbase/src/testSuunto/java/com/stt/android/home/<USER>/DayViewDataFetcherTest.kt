package com.stt.android.home.dayview

import android.content.SharedPreferences
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.google.common.truth.Truth.assertThat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyEnergyUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyStepsUseCase
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.activitydata.goals.FetchEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchStepsGoalUseCase
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.LongSleep
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.user.User
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.featuretoggle.api.FeatureToggle
import com.stt.android.home.diary.RecoveryBarGraphItem.Companion.DATA_FREQUENCY_SECONDS
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.menstrualcycle.ShowCycleDayHelper
import com.stt.android.menstrualcycle.domain.MenstrualCycle
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleListUseCase
import com.suunto.algorithms.data.Energy.Companion.joules
import com.suunto.algorithms.data.Energy.Companion.kcal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flowOf
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.ArgumentMatchers.anyString
import org.mockito.ArgumentMatchers.eq
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import kotlin.time.Duration.Companion.seconds

@RunWith(MockitoJUnitRunner::class)
class DayViewDataFetcherTest {
    @Mock
    private lateinit var fetchTrendDataUseCase: FetchTrendDataUseCase

    @Mock
    private lateinit var fetchSleepUseCase: FetchSleepUseCase

    @Mock
    private lateinit var fetchEnergyGoalUseCase: FetchEnergyGoalUseCase

    @Mock
    private lateinit var fetchStepsGoalUseCase: FetchStepsGoalUseCase

    @Mock
    private lateinit var fetchDailyStepsUseCase: FetchDailyStepsUseCase

    @Mock
    private lateinit var fetchSleepGoalUseCase: FetchSleepGoalUseCase

    @Mock
    private lateinit var fetchWorkoutsController: WorkoutHeaderController

    @Mock
    private lateinit var fetchDailyEnergyUseCase: FetchDailyEnergyUseCase

    @Mock
    private lateinit var fetchRecoveryDataUseCase: FetchRecoveryDataUseCase

    @Mock
    private lateinit var fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase

    @Mock
    private lateinit var currentUserController: CurrentUserController

    @Mock
    @SuuntoSharedPrefs
    private lateinit var suuntoPreferences: SharedPreferences

    @Mock
    private lateinit var featureToggle: FeatureToggle

    @Mock
    private lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Mock
    private lateinit var infoModelFormatter: InfoModelFormatter

    @Mock
    private lateinit var observableMenstrualCycleListUseCase: ObservableMenstrualCycleListUseCase

    @Mock
    private lateinit var showCycleDayHelper: ShowCycleDayHelper

    @Mock
    private lateinit var unitConverter: JScienceUnitConverter

    private val username = User.ANONYMOUS_USERNAME

    private lateinit var fixedClock: Clock

    private val testDispatchers = object : CoroutinesDispatchers {
        override val main = Dispatchers.Unconfined
        override val computation = Dispatchers.Unconfined
        override val io = Dispatchers.Unconfined
    }

    @Before
    fun setup() {
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli(1522684426000L),
            ZoneId.of("UTC")
        )
        `when`(currentUserController.username).thenReturn(username)
        `when`(fetchSleepGoalUseCase.fetchSleepGoal()).thenReturn(flowOf(1.seconds))
        `when`(fetchEnergyGoalUseCase.fetchEnergyGoal()).thenReturn(flowOf(1.kcal))
        `when`(fetchStepsGoalUseCase.fetchStepsGoal()).thenReturn(flowOf(1))
        `when`(fetchDailyEnergyUseCase.fetchMetabolicEnergy()).thenReturn(flowOf(2100.kcal))
        `when`(observableMenstrualCycleListUseCase(MenstrualCycleType.HISTORICAL))
            .thenReturn(flowOf(mockMenstrualCycles))
        whenever(featureToggle.get<Boolean>(anyOrNull())).thenReturn(false)
    }

    @Test
    fun `data with single item per each`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(listOf(mockHeader(startOfDay)))

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date)
            .blockingFirst()
        assertThat(days).hasSize(1)
        assertThat(days[0].workouts.size).isEqualTo(1)
        assertThat(days[0].trendDataAggregated.size).isEqualTo(1)
        assertThat(days[0].sleep.size).isEqualTo(1)
        assertThat(days[0].recoveryData.size).isEqualTo(1)
    }

    @Test
    fun `data with no results`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(emptyList()))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyList()))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyMap()))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].workouts).isEmpty()
        assertThat(days[0].trendDataSamples).isEmpty()
        assertThat(days[0].sleep).isEmpty()
    }

    @Test
    fun `data with multiple workouts`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(
                listOf(
                    mockHeader(startOfDay),
                    mockHeader(startOfDay),
                    mockHeader(startOfDay)
                )
            )

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyList()))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyMap()))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].workouts).hasSize(3)
        assertThat(days[0].trendDataSamples).hasSize(3)
        assertThat(days[0].sleep).isEmpty()
    }

    @Test
    fun `data for multiple days`() {
        val firstDay = LocalDate.of(2018, 11, 23)
        val startOfFirstDay = firstDay.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val lastDay = firstDay.plusDays(7L)

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(
                listOf(
                    mockHeader(startOfFirstDay),
                    mockHeader(startOfFirstDay),
                    mockHeader(startOfFirstDay)
                )
            )

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfFirstDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfFirstDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfFirstDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyList()))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(emptyMap()))

        val days = dayViewDataFetcher().fetchDataForDateRange(firstDay, lastDay)
            .blockingFirst()

        assertThat(days).hasSize(8)
        assertThat(days[0].workouts).hasSize(3)
        assertThat(days[0].trendDataSamples).hasSize(3)
        assertThat(days[0].sleep).isEmpty()
    }

    @Test
    fun `daily use cases should be used for fetching steps, energy, balance and stress state for today`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.RELAXING))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].trendDataAggregated).hasSize(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].sleep).hasSize(1)
        assertThat(days[0].recoveryData).hasSize(1)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    @Test
    fun `daily use cases for stress and balance should not add a current balance if watch is not connected`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(-1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.INVALID))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].trendDataAggregated).hasSize(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].sleep).hasSize(1)
        assertThat(days[0].recoveryData).hasSize(1)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(.5f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days[0].currentRecoveryData).isNull()
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    @Test
    fun `daily use cases for stress and balance with no previously stored recovery data`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.RELAXING))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(listOf()))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].trendDataAggregated).hasSize(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].sleep).hasSize(1)
        assertThat(days[0].recoveryData).hasSize(1)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    @Test
    fun `daily use cases for stress and balance should fill linearly missing data if the watch is connected`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.RELAXING))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        // Set the clock to 01:30
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli((startOfDay + (3 * DATA_FREQUENCY_SECONDS.toInt() * 1000))),
            ZoneId.systemDefault()
        )

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        // We are expecting 4 items for recovery data due to linear filling:
        // From 00:00 bumped up 3 times of 30 minutes for the current value so
        // Value step is 16.66666...
        // Time step 00:30
        // 1. 00:00 50
        // 2. 00:30 .. 50 + 16.6666 -> rounded = 67
        // 3. 01:00 .. 50 + 16.6666 * 2  -> rounded  = 83
        // 4. 01:30 100 as of current
        assertThat(days).hasSize(1)
        assertThat(days[0].trendDataAggregated).hasSize(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].trendDataAggregated[0].hrv).isEqualTo(22)
        assertThat(days[0].sleep).hasSize(1)
        assertThat(days[0].recoveryData).hasSize(4)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].recoveryData[0].timestamp)
            .isEqualTo(startOfDay + (3 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days[0].recoveryData[1].balance).isWithin(0.01f).of(.83f)
        assertThat(days[0].recoveryData[1].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].recoveryData[1].timestamp)
            .isEqualTo(startOfDay + (2 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days[0].recoveryData[2].balance).isWithin(0.01f).of(.67f)
        assertThat(days[0].recoveryData[2].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].recoveryData[2].timestamp)
            .isEqualTo(startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days[0].recoveryData[3].balance).isWithin(0.01f).of(.5f)
        assertThat(days[0].recoveryData[3].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days[0].recoveryData[3].timestamp).isEqualTo(startOfDay)
        assertThat(days[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)

        // Set the clock to 00:30 should get only 2 items
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli((startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))),
            ZoneId.systemDefault()
        )

        val days2 = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days2).hasSize(1)
        assertThat(days2[0].trendDataAggregated).hasSize(1)
        assertThat(days2[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days2[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days2[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days2[0].trendDataAggregated[0].hrv).isEqualTo(22)
        assertThat(days2[0].sleep).hasSize(1)
        assertThat(days2[0].recoveryData).hasSize(2)
        assertThat(days2[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days2[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days2[0].recoveryData[0].timestamp)
            .isEqualTo(startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days2[0].recoveryData[1].balance).isEqualTo(0.5f)
        assertThat(days2[0].recoveryData[1].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days2[0].recoveryData[1].timestamp).isEqualTo(startOfDay)
        assertThat(days2[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days2[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days2[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days2[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days2[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days2[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days2[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days2[0].trendDataSamples[2].hrv).isEqualTo(23)

        // Set the clock to 00:32 should get only 2 items and squashed down to 00:30
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli((startOfDay + (1 * (DATA_FREQUENCY_SECONDS.toInt() + 120) * 1000))),
            ZoneId.systemDefault()
        )

        val days3 = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days3).hasSize(1)
        assertThat(days3[0].trendDataAggregated).hasSize(1)
        assertThat(days3[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days3[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days3[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days2[0].trendDataAggregated[0].hrv).isEqualTo(22)
        assertThat(days3[0].sleep).hasSize(1)
        assertThat(days3[0].recoveryData).hasSize(2)
        assertThat(days3[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days3[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days3[0].recoveryData[0].timestamp)
            .isEqualTo(startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days3[0].recoveryData[1].balance).isEqualTo(.5f)
        assertThat(days3[0].recoveryData[1].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days3[0].recoveryData[1].timestamp).isEqualTo(startOfDay)
        assertThat(days3[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days3[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days3[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days3[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days3[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days3[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days3[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days3[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    @Test
    fun `daily use cases for stress and balance should not fill linearly on 30 minutes over the range if the watch is connected`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.RELAXING))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        // Set the clock to 00:30 should get only 2 items
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli((startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))),
            ZoneId.systemDefault()
        )

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days).hasSize(1)
        assertThat(days[0].trendDataAggregated).hasSize(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].trendDataAggregated[0].hrv).isEqualTo(22)
        assertThat(days[0].sleep).hasSize(1)
        assertThat(days[0].recoveryData).hasSize(2)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].recoveryData[0].timestamp)
            .isEqualTo(startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000))
        assertThat(days[0].recoveryData[1].balance).isEqualTo(.5f)
        assertThat(days[0].recoveryData[1].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days[0].recoveryData[1].timestamp).isEqualTo(startOfDay)
        assertThat(days[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    @Test
    fun `daily use cases for stress and balance should not fill linearly on 32 minutes over the range if the watch is connected`() {
        val date = LocalDate.of(2018, 11, 23)
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()

        `when`(fetchDailyStepsUseCase.fetchSteps()).thenReturn(flowOf(6000))
        `when`(fetchDailyEnergyUseCase.fetchEnergy()).thenReturn(flowOf(2020.kcal))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentBalance(-1f)).thenReturn(flowOf(1f))
        `when`(fetchDailyRecoveryDataUseCase.fetchCurrentStressState(StressState.INVALID))
            .thenReturn(flowOf(StressState.RELAXING))

        `when`(
            fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                anyString(),
                any(),
                anyLong(),
                anyLong()
            )
        )
            .thenReturn(emptyList())

        `when`(fetchWorkoutsController.currentUserWorkoutUpdatesAsObservable)
            .thenReturn(
                rx.Observable.just(
                    WorkoutHeaderController.WorkoutUpdate(
                        WorkoutHeaderController.WorkoutUpdate.UPDATED,
                        mockHeader(startOfDay)
                    )
                )
            )

        `when`(fetchTrendDataUseCase.fetchTrendData(anyOrNull(), anyOrNull(), eq(false)))
            .thenReturn(flowOf(mockTrendData(startOfDay)))

        `when`(fetchRecoveryDataUseCase.fetchRecoveryData(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockRecoveryData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleeps(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepData(startOfDay)))

        `when`(fetchSleepUseCase.fetchSleepStages(anyOrNull(), anyOrNull()))
            .thenReturn(flowOf(mockSleepStageData(startOfDay)))

        // Set the clock to 00:32 should get only 2 items and squashed down to 00:30
        fixedClock = Clock.fixed(
            Instant.ofEpochMilli((startOfDay + (1 * (DATA_FREQUENCY_SECONDS.toInt() + 120) * 1000))),
            ZoneId.systemDefault()
        )

        val days = dayViewDataFetcher().fetchDataForDateRange(date, date, date)
            .blockingFirst()

        assertThat(days.size).isEqualTo(1)
        assertThat(days[0].trendDataAggregated.size).isEqualTo(1)
        assertThat(days[0].trendDataAggregated[0].steps).isEqualTo(6000)
        assertThat(days[0].trendDataAggregated[0].energy.inJoules).isEqualTo(2020 * 4184.0)
        assertThat(days[0].trendDataAggregated[0].hrMin).isEqualTo(1.11f)
        assertThat(days[0].trendDataAggregated[0].hrv).isEqualTo(22)
        assertThat(days[0].sleep.size).isEqualTo(1)
        assertThat(days[0].recoveryData.size).isEqualTo(2)
        assertThat(days[0].recoveryData[0].balance).isEqualTo(1f)
        assertThat(days[0].recoveryData[0].stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].recoveryData[0].timeISO8601.toInstant())
            .isEqualTo(Instant.ofEpochMilli(startOfDay + (1 * DATA_FREQUENCY_SECONDS.toInt() * 1000)))
        assertThat(days[0].recoveryData[1].balance).isEqualTo(.5f)
        assertThat(days[0].recoveryData[1].stressState).isEqualTo(StressState.STRESSFUL)
        assertThat(days[0].recoveryData[1].timeISO8601.toInstant())
            .isEqualTo(Instant.ofEpochMilli(startOfDay))
        assertThat(days[0].currentRecoveryData?.balance).isEqualTo(1f)
        assertThat(days[0].currentRecoveryData?.stressState).isEqualTo(StressState.RELAXING)
        assertThat(days[0].trendDataSamples[0].hr).isEqualTo(1.11f)
        assertThat(days[0].trendDataSamples[1].hr).isEqualTo(1.13f)
        assertThat(days[0].trendDataSamples[2].hr).isEqualTo(1.15f)
        assertThat(days[0].trendDataSamples[0].hrv).isEqualTo(21)
        assertThat(days[0].trendDataSamples[1].hrv).isEqualTo(22)
        assertThat(days[0].trendDataSamples[2].hrv).isEqualTo(23)
    }

    private fun dayViewDataFetcher(): DayViewDataFetcher {
        return DayViewDataFetcher(
            fetchTrendDataUseCase = fetchTrendDataUseCase,
            fetchRecoveryDataUseCase = fetchRecoveryDataUseCase,
            fetchDailyEnergyUseCase = fetchDailyEnergyUseCase,
            fetchEnergyGoalUseCase = fetchEnergyGoalUseCase,
            fetchStepsGoalUseCase = fetchStepsGoalUseCase,
            fetchDailyStepsUseCase = fetchDailyStepsUseCase,
            fetchSleepUseCase = fetchSleepUseCase,
            fetchDailyRecoveryDataUseCase = fetchDailyRecoveryDataUseCase,
            fetchSleepGoalUseCase = fetchSleepGoalUseCase,
            observableMenstrualCycleListUseCase = observableMenstrualCycleListUseCase,
            fetchWorkoutsController = fetchWorkoutsController,
            currentUserController = currentUserController,
            rewriteNavigator = rewriteNavigator,
            infoModelFormatter = infoModelFormatter,
            clock = fixedClock,
            suuntoPreferences = suuntoPreferences,
            featureToggle = featureToggle,
            showCycleDayHelper = showCycleDayHelper,
            unitConverter = unitConverter,
            coroutinesDispatchers = testDispatchers,
        )
    }

    private fun mockSleepData(startOfDay: Long): List<Sleep> =
        listOf(
            Sleep(
                serialNumber = "12345",
                timestamp = startOfDay,
                longSleep = LongSleep(
                    fellAsleep = 0,
                    sleepDuration = 1.seconds,
                    wokeUp = 0,
                )
            )
        )

    private fun mockSleepStageData(startOfDay: Long): HashMap<Long, List<SleepStageInterval>> =
        HashMap<Long, List<SleepStageInterval>>().apply {
            put(
                startOfDay,
                listOf(
                    SleepStageInterval(
                        duration = 120.seconds,
                        stage = SleepStage.DEEP,
                        timeISO8601 = ZonedDateTime.ofInstant(
                            Instant.ofEpochMilli(startOfDay),
                            ZoneId.systemDefault()
                        )
                    )
                )
            )
        }

    private fun mockTrendData(startOfDay: Long): List<TrendData> =
        listOf(
            TrendData(
                serial = "serial",
                timestamp = startOfDay,
                energy = 400f.joules,
                steps = 2500,
                hr = 1.11f,
                hrMin = 1.11f,
                hrMax = 2.0f,
                spo2 = 0.98f,
                altitude = 112f,
                hrv = 21
            ),
            TrendData(
                serial = "serial",
                timestamp = startOfDay + Duration.ofMinutes(10).toMillis(),
                energy = 400f.joules,
                steps = 2500,
                hr = 1.13f,
                hrMin = 1.12f,
                hrMax = 1.9f,
                spo2 = 0.97f,
                altitude = 113f,
                hrv = 22
            ),
            TrendData(
                serial = "serial",
                timestamp = startOfDay + Duration.ofMinutes(20).toMillis(),
                energy = 400f.joules,
                steps = 2500,
                hr = 1.15f,
                hrMin = 1.15f,
                hrMax = 2.0f,
                spo2 = 0.98f,
                altitude = 114f,
                hrv = 23
            )
        )

    private fun mockRecoveryData(startOfDay: Long): List<RecoveryData> =
        listOf(
            RecoveryData(
                serial = "serial",
                timestamp = startOfDay,
                balance = .5f,
                stressState = StressState.STRESSFUL,
                timeISO8601 = TimeUtils.epochToLocalZonedDateTime(startOfDay)
            )
        )

    private fun mockHeader(startOfDay: Long): WorkoutHeader {
        return WorkoutHeader.local(
            0.0,
            0.0,
            ActivityType.DEFAULT,
            0.0,
            null,
            null,
            null,
            null,
            startOfDay,
            1234567891L,
            0.5,
            0.0,
            "random",
            0.0,
            0.0,
            0.0,
            0.0,
            0.0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            false,
            null,
            0,
            0.0,
            0.0,
            0,
            0.0,
            0.0,
            null,
            null,
            0
        )
    }

    private val mockMenstrualCycles = listOf(
        MenstrualCycle.create(
            LocalDate.of(2024, 5, 1),
            LocalDate.of(2024, 5, 5),
            MenstrualCycleType.HISTORICAL
        )
    )
}
