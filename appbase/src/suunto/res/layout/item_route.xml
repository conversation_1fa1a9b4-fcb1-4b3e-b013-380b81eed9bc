<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/size_spacing_small"
    android:layout_marginTop="@dimen/size_spacing_small"
    android:theme="@style/WhiteTheme"
    style="@style/FeedCard">

    <include layout="@layout/include_item_route" />

    <View
        android:id="@+id/dividerWatchViewTop"
        style="@style/HorizontalDivider"
        android:layout_width="match_parent"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/routeSummaryDataView" />

    <com.stt.android.home.explore.routes.addtowatch.AddToWatchView
        android:id="@+id/addToWatchView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dividerWatchViewTop"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/addToWatchViewGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="addToWatchView, dividerWatchViewTop"/>

</androidx.constraintlayout.widget.ConstraintLayout>
