<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="item"
            type="com.stt.android.home.diary.RecoveryBarGraphItem" />
        <import type="android.view.View" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="0dp"
        android:paddingEnd="@dimen/size_spacing_small"
        android:paddingBottom="@dimen/size_spacing_medium"
        style="@style/FeedCard">

        <com.github.mikephil.charting.charts.BarChart
            android:id="@+id/activityChart"
            android:layout_width="match_parent"
            android:layout_height="@dimen/day_view_activity_graph_height"/>

    </FrameLayout>
</layout>
