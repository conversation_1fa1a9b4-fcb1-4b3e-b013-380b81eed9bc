<?xml version="1.0" encoding="utf-8"?>
<resources>


    <!--Here are all SUUNTO VARIANT SPECIFIC text styles.
        For usage instructions, see whitening_doc_text.xml -->


    <!-- DATA LABELS -->

    <style name="Datalabel" parent="BaseDatalabel">
        <item name="android:fontFamily">@font/suunto_font</item>
    </style>

    <style name="Datalabel.XXlarge">
        <item name="android:textSize">@dimen/text_size_xmega</item>
    </style>

    <style name="Datalabel.Xlarge">
        <item name="android:textSize">@dimen/text_size_mega</item>
    </style>

    <style name="Datalabel.Large">
        <item name="android:textSize">@dimen/text_size_big</item>
    </style>

    <style name="Datalabel.Large.Inverse">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:layout_marginStart">@dimen/size_spacing_medium</item>
    </style>

    <style name="Datalabel.Medium">
        <item name="android:textSize">@dimen/text_size_xxlarge</item>
    </style>

    <style name="Datalabel.DefaultSmall">
        <item name="android:fontFamily">@font/proximanova_bold</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="Datalabel.Small" parent="Datalabel.DefaultSmall"/>

    <style name="Datalabel.DefaultMini">
        <item name="android:fontFamily">@font/proximanova_bold</item>
        <item name="android:textSize">@dimen/text_size_large</item>
    </style>

    <style name="Datalabel.Mini" parent="Datalabel.DefaultMini"/>

    <!-- HEADERS -->

    <style name="DefaultHeaderLabel" parent="BaseHeaderLabel">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="HeaderLabel" parent="DefaultHeaderLabel"/>

    <style name="HeaderLabel.XLarge">
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="HeaderLabel.Large">
        <item name="android:textSize">@dimen/text_size_large</item>
    </style>

    <style name="HeaderLabel.Large.Gray">
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>

    <style name="HeaderLabel.Larger">
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

    <style name="HeaderLabel.Medium">
        <item name="android:textSize">@dimen/text_size_medium</item>
    </style>

    <style name="HeaderLabel.Medium.Gray">
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>

    <style name="HeaderLabel.Small">
        <item name="android:textSize">@dimen/text_size_small</item>
    </style>

    <style name="HeaderLabel.Small.Widget">
        <item name="android:textColor">?newAccentColor</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_marginTop">@dimen/size_spacing_medium</item>
        <item name="android:layout_marginStart">@dimen/size_spacing_medium</item>
        <item name="android:layout_width">0dp</item>
    </style>

    <!-- REGULAR BODY TEXT  -->

    <style name="Body" parent="BaseBody">
        <item name="android:fontFamily">@font/proximanova_regular</item>
    </style>

    <style name="Body.Large">
        <item name="android:textSize">@dimen/text_size_large</item>
    </style>

    <style name="Body.Large.DefaultBold">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Large.Bold" parent="Body.Large.DefaultBold"/>

    <style name="Body.XLarge">
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="Body.XLarge.DefaultBold">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.XLarge.Bold" parent="Body.XLarge.DefaultBold"/>

    <!-- Extra bold version of large body text for some ST-only specific cases. (Does not differ from bold in Suunto variant) -->
    <style name="Body.Large.DefaultBlack">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Large.Black" parent="Body.Large.DefaultBlack"/>

    <!-- Extra bold version of extra large body text for some ST-only specific cases. (Does not differ from bold in Suunto variant) -->
    <style name="Body.XLarge.Bolder">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Larger">
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

    <style name="Body.Larger.Gray">
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>

    <style name="Body.Larger.DefaultBold">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Larger.Bold" parent="Body.Larger.DefaultBold"/>

    <!-- Extra bold version of larger body text for some ST-only specific cases -->
    <style name="Body.Larger.Black" parent="Body.Larger.Bold"/>

    <style name="Body.Medium">
        <item name="android:textSize">@dimen/text_size_medium</item>
    </style>

    <style name="Body.Medium.DefaultSecondary">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Medium.Secondary" parent="Body.Medium.DefaultSecondary"/>

    <style name="Body.Medium.Gray">
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>

    <style name="AlertText">
        <item name="android:fontFamily">@font/proximanova_bold</item>
        <item name="android:textColor">@color/bright_red</item>
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="Body.Medium.DarkGray">
        <item name="android:textColor">@color/dark_grey_st</item>
    </style>

    <style name="Body.Medium.DefaultBold">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Medium.Bold" parent="Body.Medium.DefaultBold"/>

    <style name="Body.Medium.DefaultUsername">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Medium.Username" parent="Body.Medium.DefaultUsername"/>

    <!-- Extra bold version of medium body text for some ST-only specific cases. (Does not differ from bold in Suunto variant) -->
    <style name="Body.Medium.DefaultBlack">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Medium.Black" parent="Body.Medium.DefaultBlack"/>

    <style name="Body.Small.DefaultTitle">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Small.Title" parent="Body.Small.DefaultTitle"/>

    <style name="Body.Small">
        <item name="android:textSize">@dimen/text_size_small</item>
    </style>

    <style name="Body.Small.Gray">
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>

    <style name="Body.Small.DarkGray">
        <item name="android:textColor">@color/dark_grey_st</item>
    </style>

    <style name="Body.Small.DefaultBold">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="Body.Small.Bold" parent="Body.Small.DefaultBold"/>

    <style name="Body.Small.Bold.AllCaps" parent="Body.Small.Bold">
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="Body.Action">
        <item name="android:background">?attr/selectableItemBackground</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:textColor">@color/color_body_action_text_disabled</item>
    </style>

    <style name="Body.Action.DefaultLarger">
        <item name="android:fontFamily">@font/proximanova_regular</item>
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

    <style name="Body.Action.Larger" parent="Body.Action.DefaultLarger">
        <item name="android:textColor">@color/color_title_text_disabled</item>
    </style>

    <!-- Day view specific variant of body text -->
    <style name="Body.Medium.LineSpacing">
        <item name="android:lineSpacingExtra">6sp</item>
    </style>

    <!-- Sportie overlay -->
    <style name="WorkoutShareGraphAxisValue">
        <item name="android:fontFamily">@font/proximanova_regular</item>
        <item name="android:textColor">@color/sharing_chart_line_color</item>
        <item name="android:textSize">@dimen/text_size_xsmall</item>
    </style>

    <!-- User profile -->
    <style name="Profile.Following" parent="Body.Medium.Bold">
        <item name="android:textColor">@color/accent</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="Profile.Data" parent="Body.Large.Bold" />

    <style name="ActivityIcon">
        <item name="android:tint">?android:textColorPrimary</item>
    </style>

    <style name="ActivityName" parent="Body.Medium.Bold" />

    <!-- Comments -->
    <style name="CommentWorkoutAuthor" parent="Body.Medium.Bold" />

    <!-- Similar workouts comparison -->
    <style name="SimilarWorkoutsValue" parent="Body.Larger.Bold"/>
    <style name="SimilarWorkoutsRank" parent="Body.Medium.Bold"/>

    <style name="SimilarWorkoutsRankSummary" parent="Body.Medium.Bold">
        <item name="android:background">@drawable/shape_circle_filled</item>
        <item name="android:backgroundTint">@color/accent</item>
        <item name="android:gravity">center</item>
        <item name="android:minWidth">@dimen/size_icon_small</item>
        <item name="android:minHeight">@dimen/size_icon_small</item>
        <item name="android:layout_marginStart">@dimen/size_spacing_medium</item>
        <item name="android:layout_marginTop">@dimen/size_spacing_large</item>
        <item name="android:paddingStart">5dp</item>
        <item name="android:paddingEnd">5dp</item>
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <!-- Resource gained during sleep text -->
    <style name="ResourcesGainedValue" parent="Datalabel.DefaultSmall">
        <item name="android:textColor">@color/recovery_state_recovering</item>
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

    <style name="ResourcesLostValue" parent="Datalabel.DefaultSmall">
        <item name="android:textColor">@color/activity_progression_darker</item>
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

    <!-- Sleep card: over/under goal text -->
    <style name="SleepDurationComparedToGoalValue" parent="Datalabel.DefaultSmall">
        <item name="android:textColor">@color/activity_data_sleep</item>
        <item name="android:textSize">@dimen/text_size_larger</item>
    </style>

</resources>
