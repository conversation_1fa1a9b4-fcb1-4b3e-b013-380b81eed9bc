package com.stt.android.multimedia.sportie

import android.content.Context
import android.text.style.RelativeSizeSpan
import android.util.AttributeSet
import androidx.core.content.res.ResourcesCompat
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CustomFontStyleSpan
import com.stt.android.core.R as CR

class SportieOverlayView
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SportieOverlayViewBase(context, attrs, defStyleAttr) {

    override val valueSpanFactory: TextFormatter.SpanFactory
        get() {
            val font = ResourcesCompat.getFont(this.context, CR.font.proximanova_bold)
            return TextFormatter.SpanFactory {
                if (font != null) {
                    listOf(CustomFontStyleSpan(font))
                } else {
                    emptyList()
                }
            }
        }

    override val textSpanFactory: TextFormatter.SpanFactory
        get() {
            val font = ResourcesCompat.getFont(this.context, CR.font.proximanova_regular)
            return TextFormatter.SpanFactory {
                if (font != null) {
                    listOf(CustomFontStyleSpan(font), RelativeSizeSpan(0.8f))
                } else {
                    listOf(RelativeSizeSpan(0.8f))
                }
            }
        }
}
