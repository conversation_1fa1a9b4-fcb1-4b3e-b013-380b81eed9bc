package com.stt.android.ui.map.selection

import com.stt.android.di.PremiumRequiredMyTracksGranularityTypes
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class BrandMyTracksGranularityModule {
    @PremiumRequiredMyTracksGranularityTypes
    @Provides
    fun providePremiumRequiredMyTracksGranularityTypes(): Set<@JvmWildcard MyTracksGranularity.Type> = emptySet()
}
