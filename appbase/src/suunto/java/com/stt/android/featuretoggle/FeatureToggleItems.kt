package com.stt.android.featuretoggle

import com.stt.android.BuildConfig
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants.FeatureTogglePreferences

// TODO Use FeatureFlag module
private const val KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG = "KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH"
private const val KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG_DEFAULT = false

private const val KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG = "KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY"
private const val KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT = false

private const val KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG = "KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION"
private const val KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG_DEFAULT = false

private const val KEY_ENABLE_ZONE_SENSE_DEBUG_INFO = "KEY_ENABLE_ZONE_SENSE_DEBUG_INFO"
private const val KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT = false

private const val KEY_ENABLE_FIELD_TESTER_FOR_DEBUG = "KEY_ENABLE_FIELD_TESTER_FOR_DEBUG"
private const val KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT = false

private const val KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES = "KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES"
private const val KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT = false

private const val KEY_OFFLINE_MAP_FOR_MOBILE = "KEY_OFFLINE_MAP_FOR_MOBILE"
private const val KEY_OFFLINE_MAP_FOR_MOBILE_DEFAULT = false

private const val KEY_ENABLE_HEADSET_RUNNING = "KEY_ENABLE_HEADSET_RUNNING"
private const val KEY_ENABLE_HEADSET_RUNNING_DEFAULT = false

fun featureKeys(isFieldTester: Boolean) = listOfNotNull(
    KEY_ENABLE_FIELD_TESTER_FOR_DEBUG.takeIf { BuildConfig.DEBUG }, // We want to make sure this is only available for Debug build for obvious reasons.
    KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG.takeIf { BuildConfig.DEBUG },
    KEY_ENABLE_ZONE_SENSE_DEBUG_INFO.takeIf { isFieldTester || BuildConfig.DEBUG },
    KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES,
    KEY_ENABLE_HEADSET_RUNNING,
    FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
    FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS,
    FeatureTogglePreferences.KEY_GOOGLE_LOGIN,
    FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK,
    FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2,
    KEY_OFFLINE_MAP_FOR_MOBILE,
    FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING,
    FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH,
    FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD.takeIf { FlavorUtils.isSuuntoAppChina },
    FeatureTogglePreferences.KEY_ENABLE_SU10,
    FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO,
    FeatureTogglePreferences.KEY_ENABLE_DILU_WATCH_FACE,
)

// Suunto app specific FeatureItems
fun getStaticFeatures(key: String): FeatureItem {
    return when (key) {
        KEY_ENABLE_FIELD_TESTER_FOR_DEBUG ->
            FeatureItem(
                name = "Enable field tester role (for debugging only)",
                key = key,
                enabled = KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT,
                requireProcessKill = true,
            )

        KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG ->
            FeatureItem(
                name = "Show HRV graph (for debugging only)",
                key = key,
                enabled = KEY_PURPOSES_HRV_GRAPH_FOR_DEBUG_DEFAULT,
                requireProcessKill = false
            )

        KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG ->
            FeatureItem(
                "Show headset MAC addresses (for debugging only)",
                key,
                KEY_SHOW_HEADSET_MAC_ADDRESS_FOR_DEBUG_DEFAULT
            )

        KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG ->
            FeatureItem(
                "Show 'set debug location' menu option (for debugging only)",
                key,
                KEY_SHOW_SET_WATCH_LOCATION_FOR_DEBUG_DEFAULT
            )

        KEY_ENABLE_ZONE_SENSE_DEBUG_INFO ->
            FeatureItem(
                "Enable ZoneSense debug info",
                key,
                KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT
            )

        KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES ->
            FeatureItem(
                "Enable DiLu onboarding watch faces",
                key,
                KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
            )

        KEY_ENABLE_HEADSET_RUNNING ->
            FeatureItem("Enable Headset Running", key, KEY_ENABLE_HEADSET_RUNNING_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS ->
            FeatureItem(
                "Enable new widgets",
                key,
                FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES ->
            FeatureItem(
                "Enable TopRoutes features",
                key,
                FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS ->
            FeatureItem(
                "Enable workout value groups",
                key,
                FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT
            )

        FeatureTogglePreferences.KEY_GOOGLE_LOGIN ->
            FeatureItem(
                "Enable Google login",
                key,
                FeatureTogglePreferences.KEY_GOOGLE_LOGIN_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK ->
            FeatureItem(
                "Enable SuuntoPlus feedback",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2 ->
            FeatureItem(
                "Enable Day View V2",
                key,
                FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2_DEFAULT
            )

        KEY_OFFLINE_MAP_FOR_MOBILE ->
            FeatureItem(
                "Enable offline map for mobile",
                key,
                KEY_OFFLINE_MAP_FOR_MOBILE_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING ->
            FeatureItem(
                "Enable workout paging",
                key,
                FeatureTogglePreferences.KEY_ENABLE_WORKOUT_PAGING_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH ->
            FeatureItem(
                "Enable home search",
                key,
                FeatureTogglePreferences.KEY_ENABLE_HOME_SEARCH_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS ->
            FeatureItem(
                "Enable syncing widgets",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SYNC_WIDGETS_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD ->
            FeatureItem(
                "Enable transit card",
                key,
                FeatureTogglePreferences.KEY_ENABLE_TRANSIT_CARD_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_SU10 ->
            FeatureItem(
                "Enable SU10",
                key,
                FeatureTogglePreferences.KEY_ENABLE_SU10_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO ->
            FeatureItem(
                "Enable mocking widget info",
                key,
                FeatureTogglePreferences.KEY_ENABLE_MOCK_WIDGET_INFO_DEFAULT
            )

        FeatureTogglePreferences.KEY_ENABLE_DILU_WATCH_FACE ->
            FeatureItem(
                "Enable Dilu watch face",
                key,
                FeatureTogglePreferences.KEY_ENABLE_DILU_WATCH_FACE_DEFAULT,
                requireProcessKill = true
            )

        else -> throw IllegalArgumentException("Unknown key")
    }
}
