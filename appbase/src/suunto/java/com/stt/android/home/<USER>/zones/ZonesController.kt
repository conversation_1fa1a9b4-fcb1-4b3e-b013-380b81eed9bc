package com.stt.android.home.settings.zones

import com.airbnb.epoxy.EpoxyAsyncUtil
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.hrZone
import com.stt.android.hrZone2
import com.stt.android.hrZone3

class ZonesController(
    private val zoneClick: OnZoneClicked
) : TypedEpoxyController<List<BaseZoneItem>>(
    EpoxyAsyncUtil.getAsyncBackgroundHandler(),
    EpoxyAsyncUtil.getAsyncBackgroundHandler()
) {

    private fun createZoneTitleItem(item: ZoneTitleItem) {
        hrZone2 {
            id(item.zoneNumber)
            item(item)
        }
    }

    private fun createHrZoneItem(item: ZoneInfoItem) {
        hrZone {
            id(item.title)
            item(item)
            onZoneClicked { model, _, _, _ ->
                zoneClick.invoke(model.item())
            }
        }
    }

    override fun buildModels(items: List<BaseZoneItem>) {
        items.forEach { item ->
            when (item) {
                is ZoneTitleItem -> {
                    createZoneTitleItem(item)
                }

                is ZoneInfoItem -> {
                    createHrZoneItem(item)
                }

                is ZoneBarItem -> {
                    hrZone3 {
                        id(item.zoneId.name)
                        item(item)
                    }
                }
            }
        }
    }
}
