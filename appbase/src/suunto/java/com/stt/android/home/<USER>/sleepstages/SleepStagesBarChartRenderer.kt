package com.stt.android.home.dayview.sleepstages

import android.graphics.Canvas
import android.graphics.RectF
import com.github.mikephil.charting.animation.ChartAnimator
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.interfaces.dataprovider.BarDataProvider
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.renderer.BarChartRenderer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler

/**
 * render the bar of different sleep stage  time
 */
class SleepStagesBarChartRenderer(
    chart: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    animator: ChartA<PERSON>mator,
    viewPortHandler: ViewPortHandler,
    private val stageCount: Int,
    private val barCorner: Float
) : BarChartRenderer(chart, animator, viewPortHandler) {

    override fun drawDataSet(c: Canvas?, dataSet: IBarDataSet?, index: Int) {
        val transformer = mChart.getTransformer(YAxis.AxisDependency.LEFT)
        if (c != null && dataSet != null) {
            val isSingleColor = dataSet.colors.size == 1

            if (isSingleColor) {
                mRenderPaint.color = dataSet.color
            }
            val size = dataSet.entryCount
            for (dataIndex in 0 until size) {
                val barEntry = dataSet.getEntryForIndex(dataIndex)
                // only render sleepStageBar data
                if (barEntry is SleepStageBarEntry) {
                    if (barEntry.stageType.type >= 0 && barEntry.stageType.type < dataSet.colors.size) {
                        mRenderPaint.color = dataSet.colors[barEntry.stageType.type]
                    }
                    val barHeight =
                        (mViewPortHandler.contentBottom() - mViewPortHandler.contentTop()).div(
                            stageCount
                        )
                    val top = mViewPortHandler.contentTop() + barEntry.stageType.type * barHeight
                    val xStart = if (barEntry.xStart < mChart.xChartMin) {
                        mChart.xChartMin
                    } else {
                        barEntry.xStart
                    }
                    val xEnd = if (barEntry.xEnd > mChart.xChartMax) {
                        mChart.xChartMax
                    } else {
                        barEntry.xEnd
                    }
                    // top and bottom don't need transform
                    val rect = RectF(
                        xStart,
                        0f,
                        xEnd,
                        0f
                    )
                    transformer.rectValueToPixel(rect)
                    rect.top = top
                    rect.bottom = top + barHeight
                    val cornerPx = Utils.convertDpToPixel(barCorner)
                    c.drawRoundRect(rect, cornerPx, cornerPx, mRenderPaint)
                }
            }
        }
    }
}
