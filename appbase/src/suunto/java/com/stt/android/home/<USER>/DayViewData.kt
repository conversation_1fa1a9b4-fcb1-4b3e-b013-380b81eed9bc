package com.stt.android.home.dayview

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.EnergyUtil
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dayview.daypageitems.DayViewEditGoalsButtonItem
import com.stt.android.home.dayview.daypageitems.DayViewSleepItem
import com.stt.android.home.dayview.daypageitems.DayViewWorkoutItem
import com.stt.android.home.dayview.daypageitems.ExpandableRecoveryItem
import com.stt.android.home.dayview.daypageitems.RecoveryStatisticsItem
import com.stt.android.home.diary.ActivityBarGraphItem
import com.stt.android.home.diary.ActivityLineGraphItem
import com.stt.android.home.diary.RecoveryBarGraphItem
import com.stt.android.home.diary.RecoveryBarGraphItem.Companion.DATA_FREQUENCY_SECONDS
import com.stt.android.home.diary.calories.ExpandableCaloriesItem
import com.stt.android.home.diary.dailyhr.ExpandableDailyHRItem
import com.stt.android.home.diary.diarycalendar.workoutstats.WorkoutStat
import com.stt.android.home.diary.hrv.ExpandableDailyHRVItem
import com.stt.android.home.diary.spo2.ActivitySpO2GraphItem
import com.stt.android.home.diary.spo2.ExpandableSpO2Item
import com.stt.android.home.diary.steps.ExpandableStepsItem
import com.stt.android.mapping.InfoModelFormatter
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.HeartRate.Companion.hz
import com.xwray.groupie.ExpandableGroup
import com.xwray.groupie.Group
import com.xwray.groupie.Section
import kotlinx.collections.immutable.ImmutableList
import java.time.Clock
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.Locale
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import kotlin.time.Duration
import com.stt.android.core.R as CR

data class DayViewSleep(
    val sleep: Sleep,
    val balanceGained: Float? = null, // 0..1
    val balanceOnWakeUp: Float? = null, // 0..1
    val sleepStageIntervals: List<SleepStageInterval> = arrayListOf()
)

/**
 * Class representing all necessary data for displaying a single day in day view
 */
data class DayViewData(
    val startOfDayISO8601: ZonedDateTime,
    val workouts: List<WorkoutHeader>,
    val trendDataAggregated: List<TrendData>,
    val trendDataSamples: List<TrendData>,
    val recoveryData: List<RecoveryData>,
    val sleep: List<DayViewSleep>,
    val bmr: Energy,
    val sleepGoal: Duration,
    val stepsGoal: Int,
    val energyGoal: Energy,
    val showBubble: Boolean,
    val firstbeatSleepThresholds: Boolean,
    val sleepTrendGood: Boolean,
    val sleepTrendPoor: Boolean,
    val currentRecoveryData: RecoveryData? = null,
    val rewriteNavigator: WorkoutDetailsRewriteNavigator,
    val infoModelFormatter: InfoModelFormatter,
    val showHRVGraph: Boolean,
    val stats: ImmutableList<WorkoutStat>,
    val isPeriodDay: Boolean
) {
    val startOfDay: Long = startOfDayISO8601.toEpochMilli()

    /**
     * Convert data to list items for day view
     *
     * @param startOfTodayMillis Start of today in milliseconds since epoch in local time zone
     * @param clock Clock for providing current instant for diary list items
     * @param locale Locale used for date formatting in diary list items
     * @param editGoalAction Method called when Edit Goals button is clicked
     * @return List items to be shown for this date in day view
     */
    fun toItems(
        startOfTodayMillis: Long,
        clock: Clock,
        locale: Locale,
        editGoalAction: () -> Unit,
        onDeletePeriodDay: (LocalDate) -> Unit
    ): List<Group> {
        val items = mutableListOf<Group>()

        if (stats.isNotEmpty()) {
            items.add(
                Section().apply { add(DayViewStatisticsItem(stats)) }
            )
        }

        if (isPeriodDay) {
            items.add(
                Section().apply {
                    add(DayViewPeriodDayItem { onDeletePeriodDay(startOfDayISO8601.toLocalDate()) })
                }
            )
        }

        if (workouts.isNotEmpty()) {
            items.add(
                Section().apply {
                    setHeader(DayViewHeaderItem(R.string.day_view_workouts_header))
                    addWorkouts(this)
                }
            )
        }

        if (hasSleep()) {
            items.add(
                Section().apply {
                    setHeader(DayViewHeaderItem(R.string.diary_sleep_card_title))
                    addSleep(this)
                }
            )
        }

        if (has247Data()) {
            items.add(
                Section().apply {
                    setHeader(DayViewHeaderItem(R.string.diary_daily_activity_header))
                    if (hasRecoveryData()) {
                        addRecovery(this, clock, locale, startOfTodayMillis)
                    }
                    if (hasDailyHR()) {
                        addDailyHR(this, clock, locale)
                    }
                    if (showHRVGraph && hasHRV()) {
                        addHRV(this, clock, locale)
                    }
                    if (hasSpO2()) {
                        addSpO2(this, clock, locale)
                    }
                    if (hasSteps()) {
                        addSteps(this, clock, locale)
                    }
                    if (hasCalories()) {
                        addEnergy(this, clock, locale, startOfTodayMillis)
                    }
                }
            )
        }

        addEditGoalIfNeeded(items, editGoalAction)
        return items
    }

    /**
     * Calculate [DayType] for this date based on simple logic
     *
     * The current logic is as follows:
     * - Dates after today are [DayType.FutureDate]
     * - Today is [DayType.IncompleteDay] unless there is already a workout or the steps goal as been met
     * - Each day that has at least one workout is [DayType.TrainingDay]
     * - Each day that has the steps goal met is [DayType.ActiveDay]
     * - Other days are [DayType.RestDay]
     *
     * @param startOfTodayMillis Start of today in milliseconds since epoch in local time zone
     * @return [DayType] for this day
     */
    fun getDayType(startOfTodayMillis: Long): DayType = when {
        startOfDay > startOfTodayMillis ->
            DayType.FutureDate

        workouts.isNotEmpty() ->
            DayType.TrainingDay(numberOfWorkouts = workouts.size)

        trendDataAggregated.sumOf { it.steps } > stepsGoal ->
            DayType.ActiveDay

        startOfDay == startOfTodayMillis ->
            DayType.IncompleteDay

        else ->
            DayType.RestDay
    }

    fun isEmptyDay(): Boolean =
        !has247Data() && workouts.isEmpty() && !hasSleep() && stats.isEmpty() && !isPeriodDay

    private fun hasDailyHR() = trendDataAggregated.any { it.hasHr }

    private fun hasHRV() = trendDataAggregated.any { it.hasHRV }

    private fun hasCalories() = trendDataAggregated.any { it.energy.inCal > 0.0 }

    private fun hasSteps() = trendDataAggregated.any { it.steps > 0 }

    private fun hasSpO2() = trendDataAggregated.any { it.spo2 != null }

    private fun hasRecoveryData() = recoveryData.isNotEmpty()

    private fun hasSleep() = sleep.any { !it.sleep.isEmpty }

    private fun has247Data() =
        recoveryData.isNotEmpty() ||
            trendDataAggregated.any { it.steps > 0 || it.energy.inCal > 0.0 } ||
            hasDailyHR()

    private fun addWorkouts(section: Section) {
        val workoutItems = workouts.map { DayViewWorkoutItem(it, rewriteNavigator) }
        section.addAll(workoutItems)
    }

    private fun addSteps(section: Section, clock: Clock, locale: Locale) {
        section.addAll(
            trendDataAggregated
                .map { it ->
                    ExpandableGroup(
                        ExpandableStepsItem(
                            it,
                            clock,
                            locale,
                            ActivityDataType.Steps(it.steps, stepsGoal)
                        ),
                        true
                    )
                        .apply {
                            add(
                                ActivityBarGraphItem(
                                    startDate = startOfDayISO8601,
                                    data = trendDataSamples.map {
                                        Pair(it.timeISO8601, it.steps)
                                    },
                                    graphColor = CR.color.functional_cyan
                                )
                            )
                        }
                }
        )
    }

    private fun addSpO2(section: Section, clock: Clock, locale: Locale) {
        val altitudeRange = with(trendDataSamples.mapNotNull { it.altitude }) {
            val minAltitude = minOrNull()?.roundToInt()
            val maxAltitude = maxOrNull()?.roundToInt()
            if (minAltitude != null && maxAltitude != null) minAltitude..maxAltitude else null
        }
        section.addAll(
            trendDataAggregated
                .map { it ->
                    ExpandableGroup(
                        ExpandableSpO2Item(
                            trendData = it,
                            altitudeRange = altitudeRange,
                            clock = clock,
                            locale = locale,
                            infoModelFormatter = infoModelFormatter,
                            activityDataType = ActivityDataType.Spo2(it.spo2?.let { (it * 100).roundToInt() })
                        ),
                        true
                    )
                        .apply {
                            add(
                                ActivitySpO2GraphItem(
                                    startDate = startOfDayISO8601,
                                    data = trendDataSamples.filter { it.spo2 != null }
                                        .map {
                                            Pair(
                                                it.timeISO8601,
                                                it.spo2?.let { spo2 -> (spo2 * 100) } ?: 0f
                                            )
                                        },
                                    graphColor = CR.color.functional_cyan
                                )
                            )
                        }
                }
        )
    }

    private fun addEnergy(
        section: Section,
        clock: Clock,
        locale: Locale,
        startOfTodayMillis: Long
    ) {
        section.addAll(
            trendDataAggregated
                .map { it ->
                    val scaledBmr = if (it.timestamp == startOfTodayMillis) {
                        // For the current day, scale BMR according to the hour of day
                        EnergyUtil.scaledBmrForToday(bmr, clock)
                    } else {
                        bmr
                    }
                    ExpandableGroup(
                        ExpandableCaloriesItem.create(
                            it.timestamp,
                            scaledBmr,
                            it.energy,
                            clock,
                            locale,
                            energyGoal.inKcal.roundToInt(),
                        ),
                        true
                    ).apply {
                        add(
                            ActivityBarGraphItem(
                                startDate = startOfDayISO8601,
                                data = trendDataSamples.map {
                                    Pair(it.timeISO8601, it.energy.inKcal.roundToInt())
                                },
                                graphColor = CR.color.functional_yellow
                            )
                        )
                    }
                }
        )
    }

    private fun addDailyHR(section: Section, clock: Clock, locale: Locale) {
        section.addAll(
            trendDataAggregated
                .map { it ->
                    ExpandableGroup(
                        ExpandableDailyHRItem(
                            it,
                            clock,
                            locale
                        ),
                        true
                    )
                        .apply {
                            add(
                                ActivityLineGraphItem(
                                    startDate = startOfDayISO8601,
                                    data = trendDataSamples.mapNotNull {
                                        val hr = it.hr
                                        if (hr != null && hr > 0) {
                                            Pair(it.timeISO8601, hr.hz.inBpm.roundToInt())
                                        } else {
                                            null
                                        }
                                    },
                                    graphColor = CR.color.bright_red,
                                    fillDrawableRes = R.drawable.heart_rate_graph_gradient
                                )
                            )
                        }
                }
        )
    }

    private fun addHRV(section: Section, clock: Clock, locale: Locale) {
        section.addAll(
            trendDataAggregated
                .map { it ->
                    ExpandableGroup(
                        ExpandableDailyHRVItem(
                            it,
                            clock,
                            locale
                        ),
                        true
                    )
                        .apply {
                            add(
                                ActivityLineGraphItem(
                                    startDate = startOfDayISO8601,
                                    data = trendDataSamples.mapNotNull {
                                        val hrv = it.hrv
                                        if (hrv != null && hrv > 0) {
                                            Pair(it.timeISO8601, hrv)
                                        } else {
                                            null
                                        }
                                    },
                                    graphColor = CR.color.suunto_unspecified,
                                    fillDrawableRes = null,
                                )
                            )
                        }
                }
        )
    }

    private fun addRecovery(
        section: Section,
        clock: Clock,
        locale: Locale,
        startOfTodayMillis: Long
    ) {
        @StringRes val resourcesSubtitle: Int
        val resourcesValue: Float
        val isTodaysData = recoveryData.any { it.timeISO8601.toEpochMilli() >= startOfTodayMillis }
        val lastMeasurement = recoveryData.first()
        val resourcesSubtitleTimeString: String
        when {
            isTodaysData && currentRecoveryData != null -> {
                resourcesSubtitle = R.string.resources
                resourcesValue = currentRecoveryData.balance
                resourcesSubtitleTimeString = ""
            }

            isTodaysData && currentRecoveryData == null -> {
                resourcesSubtitle = R.string.resources_at
                resourcesValue = lastMeasurement.balance
                resourcesSubtitleTimeString =
                    TimeUtils.getZonedDateTimeAsHoursAndMinutes(lastMeasurement.timeISO8601)
            }

            else -> {
                // Add the interval so that recovery data measured at 23:30 are shown as 00:00 (30min interval [DATA_FREQUENCY_SECONDS])
                resourcesSubtitle = R.string.resources_until
                val untilTimeISO8601 =
                    lastMeasurement.timeISO8601.plusSeconds(DATA_FREQUENCY_SECONDS.toLong())
                resourcesSubtitleTimeString =
                    TimeUtils.getZonedDateTimeAsHoursAndMinutes(untilTimeISO8601)
                resourcesValue = lastMeasurement.balance
            }
        }

        section.add(
            ExpandableGroup(
                ExpandableRecoveryItem(
                    itemTimestamp = startOfTodayMillis,
                    resourcesValue = resourcesValue,
                    resourcesStringRes = resourcesSubtitle,
                    resourcesValueTimeString = resourcesSubtitleTimeString,
                    clock = clock,
                    locale = locale
                ),
                true
            ).apply {
                add(
                    RecoveryBarGraphItem(
                        startDate = startOfDayISO8601,
                        recoveryData = recoveryData
                    )
                )

                add(
                    RecoveryStatisticsItem(
                        activeSeconds = getSecondsByStressState(StressState.ACTIVE),
                        passiveSeconds = getSecondsByStressState(StressState.PASSIVE),
                        stressfulSeconds = getSecondsByStressState(StressState.STRESSFUL),
                        relaxingSeconds = getSecondsByStressState(StressState.RELAXING)
                    )
                )
            }
        )
    }

    private fun getSecondsByStressState(stressState: StressState): Long =
        (recoveryData.count { it.stressState == stressState } * DATA_FREQUENCY_SECONDS).roundToLong()

    private fun addSleep(section: Section) {
        section.addAll(
            sleep
                .map {
                    DayViewSleepItem(
                        sleep = it.sleep,
                        sleepDuration = ActivityDataType.SleepDuration(
                            it.sleep.totalSleepDuration.inWholeSeconds.toInt(),
                            sleepGoal.inWholeSeconds.toInt(),
                        ),
                        sleepQuality = ActivityDataType.SleepQuality(it.sleep.longSleep?.qualityPerc),
                        firstbeatSleepThresholds = firstbeatSleepThresholds,
                        sleepTrendGood = sleepTrendGood,
                        sleepTrendPoor = sleepTrendPoor,
                        balanceGained = it.balanceGained,
                        balanceOnWakeUp = it.balanceOnWakeUp,
                        infoModelFormatter = infoModelFormatter,
                        sleepStageIntervals = it.sleepStageIntervals
                    )
                }
        )
    }

    private fun addEditGoalIfNeeded(updated: MutableList<Group>, editGoalAction: () -> Unit) {
        if (updated.isNotEmpty() && !isEmptyDay()) {
            updated.add(DayViewEditGoalsButtonItem(editGoalAction))
        }
    }
}
