package com.stt.android.watch.device

import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.battery.UsbCableState
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetUsbCableStateUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
) {
    operator fun invoke(): Flow<UsbCableState> = suuntoWatchModel.getUsbCableStateObservable()
}
