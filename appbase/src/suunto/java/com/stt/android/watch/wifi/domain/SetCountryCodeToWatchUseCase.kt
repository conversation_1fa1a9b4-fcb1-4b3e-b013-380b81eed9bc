package com.stt.android.watch.wifi.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.watch.wifi.datasource.WifiNetworksDataSource
import com.stt.android.watch.wifi.entity.WifiGeneralSettings
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours

class SetCountryCodeToWatchUseCase
@Inject constructor(
    private val lastKnownLocationUseCase: LastKnownLocationUseCase,
    private val dataSource: WifiNetworksDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider,
) {
    suspend fun run() = withContext(dispatcherProvider.io) {
        val hourAgo = System.currentTimeMillis() - 1.hours.inWholeMilliseconds
        lastKnownLocationUseCase.getLastKnownLocationAsCountryCode(
            skipPassiveProvider = false,
            timeInMilliSecondsSinceEpoch = hourAgo
        )?.let { countryCode ->
            dataSource.setWifiGeneralSettings(WifiGeneralSettings(countryCode = countryCode))
        } ?: throw NullPointerException("Fetching last known location failed.")
    }
}
