package com.stt.android.watch.preference.personalinfo

import android.content.Context
import android.util.AttributeSet
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserSettings
import com.stt.android.home.settings.BaseWeightDialogPreference
import java.util.Locale

class SetupPreferenceWeightPreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : BaseWeightDialogPreference(context, attrs, defStyleAttr, defStyleRes) {

    override fun getMeasurementUnit(): MeasurementUnit {
        tempMeasurementUnit?.let { return it }
        val measurementUnitName =
            sharedPreferences!!.getString(SetupPreferencePersonalInfoFragment.KEY_UNIT, "metric")
        return MeasurementUnit.valueOf(measurementUnitName!!.uppercase(Locale.US))
    }

    private var tempMeasurementUnit: MeasurementUnit? = null
    internal fun refresh(unit: String) {
        tempMeasurementUnit = MeasurementUnit.valueOf(unit.uppercase(Locale.US))
        val summary = getWeightWithUnitSummary(
            getPersistedString(UserSettings.DEFAULT_WEIGHT_GRAMS.toString())
        )
        this.summary = summary
        tempMeasurementUnit = null
    }
}
