<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.stt.android.ui.utils.WidthLimiterLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- period -->

                <com.stt.android.goals.edit.GoalEditItemView
                    android:id="@+id/periodEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:editorTitle="@string/goal_period" />

                <com.stt.android.ui.components.GoalStartDateEditor
                    android:id="@+id/startDateEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:showTime="false" />

                <com.stt.android.ui.components.GoalDateTimeEditor
                    android:id="@+id/endDateEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:editorTitle="@string/goal_period_end"
                    app:showTime="false" />

                <!-- goal type -->

                <com.stt.android.goals.edit.GoalEditItemView
                    android:id="@+id/typeEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:editorTitle="@string/goal_type" />

                <!-- target -->

                <com.stt.android.goals.edit.GoalEditItemView
                    android:id="@+id/distanceEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:background="?android:attr/selectableItemBackground"
                    app:editorTitle="@string/distance" />

                <com.stt.android.goals.edit.GoalEditItemView
                    android:id="@+id/energyEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:background="?android:attr/selectableItemBackground"
                    app:editorTitle="@string/energy"
                    app:unit="@string/kcal" />

                <com.stt.android.goals.edit.GoalEditItemView
                    android:id="@+id/workoutAmountEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:background="?android:attr/selectableItemBackground"
                    app:editorTitle="@string/goal_type_amounts" />

                <com.stt.android.ui.components.GoalDurationEditor
                    android:id="@+id/durationEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:editorTitle="@string/duration" />

                <!-- activities -->

                <com.stt.android.ui.components.GoalActivityTypeSelectionEditor
                    android:id="@+id/activityTypeEditor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/item_height"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    app:editorTitle="@string/goal_activities" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </com.stt.android.ui.utils.WidthLimiterLayout>

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/loadingSpinner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        style="@style/Widget.AppCompat.ProgressBar"/>

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_scrollFlags="scroll|enterAlways"
            app:theme="@style/Toolbar.Native"
            style="@style/Toolbar.Native"/>
    </com.google.android.material.appbar.AppBarLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
