package com.stt.android.launcher

import androidx.work.WorkManager
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ProxyViewModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
    coroutinesDispatchers: CoroutinesDispatchers,
    private val workManager: dagger.Lazy<WorkManager>,
) : BaseProxyViewModel(
    workoutHeaderController = workoutHeaderController,
    currentUserController = currentUserController,
    coroutinesDispatchers = coroutinesDispatchers,
) {
    fun startIcpRequestJob() {
        IcpRequestJob.enqueue(workManager.get())
    }
}
