package com.stt.android.tutorial.impl.screen.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.HorizontalPagerIndicator
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.spacing

@Composable
internal fun TutorialNavigation(
    pagerState: PagerState,
    modifier: Modifier = Modifier,
) {
    if (pagerState.pageCount == 1) {
        return
    }

    Row(
        modifier = modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (pagerState.currentPage != 0) {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = {
                    if (pagerState.canScrollBackward) {
                        pagerState.requestScrollToPage(pagerState.currentPage - 1)
                    }
                },
            )
        } else {
            Spacer(Modifier.width(48.dp))
        }

        Spacer(Modifier.weight(1.0F))

        HorizontalPagerIndicator(pagerState)

        Spacer(Modifier.weight(1.0F))

        if (pagerState.currentPage != pagerState.pageCount - 1) {
            SuuntoIconButton(
                icon = R.drawable.all_ic_arrow_right,
                onClick = {
                    if (pagerState.canScrollForward) {
                        pagerState.requestScrollToPage(pagerState.currentPage + 1)
                    }
                },
            )
        } else {
            Spacer(Modifier.width(48.dp))
        }
    }
}
