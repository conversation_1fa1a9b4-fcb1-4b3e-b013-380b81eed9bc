package com.stt.android.tutorial.impl.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.tutorial.api.model.Tutorial
import com.stt.android.tutorial.impl.screen.components.TutorialImage
import com.stt.android.tutorial.impl.screen.components.TutorialNavigation
import com.stt.android.tutorial.impl.screen.components.TutorialTextAndButton
import com.stt.android.tutorial.impl.screen.components.TutorialVideo

@Composable
internal fun LandscapeTutorialScreen(
    tutorials: List<Tutorial>,
    onPrimaryButtonClick: (Int) -> Unit,
    onSecondaryButtonClick: (Int) -> Unit,
    close: () -> Unit,
    pagerState: PagerState,
    modifier: Modifier = Modifier,
) {
    HorizontalPager(
        state = pagerState,
        modifier = modifier
            .fillMaxSize()
            .narrowContent()
            .background(MaterialTheme.colorScheme.surface),
        beyondViewportPageCount = 1,
    ) { index ->
        DashboardIntroductionPage(
            tutorial = tutorials[index],
            pagerState = pagerState,
            onPrimaryButtonClick = { onPrimaryButtonClick(index) },
            onSecondaryButtonClick = { onSecondaryButtonClick(index) },
            close = close,
        )
    }
}

@Composable
private fun DashboardIntroductionPage(
    tutorial: Tutorial,
    pagerState: PagerState,
    onPrimaryButtonClick: () -> Unit,
    onSecondaryButtonClick: () -> Unit,
    close: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxSize(),
    ) {
        TutorialImage(
            tutorial = tutorial,
            modifier = Modifier
                .fillMaxHeight(),
        )

        TutorialVideo(
            tutorial = tutorial,
            modifier = Modifier
                .fillMaxHeight()
                .aspectRatio(1.0F),
        )

        Box(
            modifier = Modifier.weight(1.0F),
        ) {
            Column(
                modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                TutorialTextAndButton(
                    tutorial = tutorial,
                    onPrimaryButtonClick = onPrimaryButtonClick,
                    onSecondaryButtonClick = onSecondaryButtonClick,
                )

                TutorialNavigation(
                    pagerState = pagerState,
                )
            }

            SuuntoIconButton(
                icon = SuuntoIcons.ActionClose,
                onClick = close,
                modifier = Modifier
                    .align(Alignment.TopEnd),
                iconSize = MaterialTheme.iconSizes.small,
            )
        }
    }
}
