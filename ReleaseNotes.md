#### 6.3.0 (2025-09-03)
 - Fixes
  - 193293 Import route & create route reversed for ST by author:<PERSON><PERSON> (<PERSON>)
  - 193146 192381 Correct sleep widget time format by author:<PERSON><PERSON> (<PERSON>)
  - 193260 Add premium navigation check for MapWidgetInfo [develop] by author:<PERSON><PERSON> (<PERSON>)
  - 193222 193223 193230 193274 Fix TP bugs by author:wa<PERSON><PERSON><PERSON><PERSON>-suunto
  - Applied M3 theme to TagsCarouselView by author:<PERSON><PERSON> (<PERSON>)
  - 192016 Manually added activities cannot download the json files by author:<PERSON><PERSON>ianming2
  - chart detail update chart granularity by author:shuaiwenchen
  - 192289 192395 192464 192359 192501 AI planner design updates and fixes by author:<PERSON>
  - 192527 192254 some strings update by author:Jessiee<PERSON>i

 - Features
  - 192607 Caching with homepage widgets states, 3/x by author:<PERSON><PERSON> (<PERSON>)
  - 193141 Refactor SoaLibrary to support multiple device 4/x by author:<PERSON><PERSON> (<PERSON>)
  - 193262 NFC Transit Card, 1/x by author:<PERSON><PERSON> (<PERSON>)
  - 193141 Refactor SoaLibrary to support multiple device 3/x by author:Yazhou66
  - 191203 Fetch battery file and upload by author:moon
  - 193183 Support search workouts in homepage, 1/X by author:wa<PERSON><PERSON><PERSON><PERSON>-suunto
  - 187306 Support offline maps on mobile, 21/x by author:Xizhi <PERSON> (<PERSON>)
  - 193141 Refactor SoaLibrary to support heart belt 1/x by author:Yazhou66
  - chart detail add workout list by author:shuaiwenchen
  - 187306 Support offline maps on mobile, 20/x by author:Xizhi Zhu (Steven)
  - 192886 New activity colors by author:<PERSON> <PERSON>la
  - 187306 Support offline maps on mobile, 19/x by author:Xizhi Zhu (Steven)
  - 192963 Update default displays for adding widgets, 1/x by author:wangxiaochun-suunto
  - 192607 Fix remote-local widget mapping by author:wangxiaochun-suunto
  - 192607 Caching with homepage widgets states by author:wangxiaochun-suunto
  - 187306 Support offline maps on mobile, 18/x by author:Xizhi Zhu (Steven)
  - 192763 modify home screen analytics by author:shuaiwenchen
  - 191851 leave widgets detail page analytics 4 by author:shuaiwenchen
  - 192361 Implement heart belt setting 2/x by author:Yazhou66
  - 192592 add training zone recovery button click analytics by author:shuaiwenchen
  - Remove tip for connecting watch to charger for offline map downloading by author:Xizhi Zhu (Steven)
  - 187306 Support offline maps on mobile, 17/x by author:Xizhi Zhu (Steven)
  - 192463 add recovery sleep analytics by author:shuaiwenchen
  - 187306 Support offline maps on mobile, 16/x by author:Xizhi Zhu (Steven)

 - Technical
  - Generate app dependency list by author:JiangXianming2
  - Rename amplitude analytics tracker by author:JiangXianming2
  - Fix annotation warnings by author:Sami Rajala
  - Update SDS to 3.33.0 by author:JiangXianming2
  - Updated release notes by author:JiangXianming2
  - Inject SuuntoMaps in maps initializer by author:JiangXianming2
  - Use Fakt font for ST tracking text style and for data labels in ST by author:Sami Rajala
  - Suunto Typography V2 by author:Sami Rajala
  - Use workout impact from WorkoutHeader by author:Xizhi Zhu (Steven)
  - chart detail use lazycolumn by author:shuaiwenchen
  - Base compose themes and preparation for dark mode support  by author:Sami Rajala
  - Extract common text styles into a separate class by author:Sami Rajala